{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, Input } from '@angular/core';\nexport let NewsComponent = class NewsComponent {\n  constructor(authService) {\n    this.authService = authService;\n    this.showTitle = true;\n    this.compact = false;\n    // News data\n    this.news = [];\n    this.loading = false;\n    this.error = '';\n    // Filter properties\n    this.filteredNews = [];\n    this.searchTerm = '';\n    this.selectedCountry = '';\n    this.selectedSource = '';\n    // Available filter options\n    this.availableCountries = [];\n    this.availableSources = [];\n  }\n  ngOnInit() {\n    this.loadNews();\n  }\n  loadNews() {\n    this.loading = true;\n    this.error = '';\n    this.authService.ensureAuthenticated().subscribe({\n      next: () => {\n        this.authService.fetchNews('poland').subscribe({\n          next: response => {\n            this.news = response.news || [];\n            this.initializeFilters();\n            this.applyFilters();\n            this.loading = false;\n          },\n          error: error => {\n            this.error = 'Failed to load news';\n            this.loading = false;\n            console.error('Error loading news:', error);\n          }\n        });\n      },\n      error: error => {\n        this.error = 'Authentication failed';\n        this.loading = false;\n        console.error('Auth error:', error);\n      }\n    });\n  }\n  initializeFilters() {\n    this.availableCountries = [...new Set(this.news.map(item => item.country))].sort();\n    this.availableSources = [...new Set(this.news.map(item => item.source))].sort();\n  }\n  applyFilters() {\n    let filtered = [...this.news];\n    // Apply search filter\n    if (this.searchTerm.trim()) {\n      const searchLower = this.searchTerm.toLowerCase();\n      filtered = filtered.filter(item => item.title.toLowerCase().includes(searchLower) || item.description.toLowerCase().includes(searchLower) || item.source.toLowerCase().includes(searchLower));\n    }\n    // Apply country filter\n    if (this.selectedCountry) {\n      filtered = filtered.filter(item => item.country === this.selectedCountry);\n    }\n    // Apply source filter\n    if (this.selectedSource) {\n      filtered = filtered.filter(item => item.source === this.selectedSource);\n    }\n    this.filteredNews = filtered;\n  }\n  onSearchChange(event) {\n    this.searchTerm = event.target.value;\n    this.applyFilters();\n  }\n  onCountryChange(event) {\n    this.selectedCountry = event.target.value;\n    this.applyFilters();\n  }\n  onSourceChange(event) {\n    this.selectedSource = event.target.value;\n    this.applyFilters();\n  }\n  clearFilters() {\n    this.searchTerm = '';\n    this.selectedCountry = '';\n    this.selectedSource = '';\n    this.applyFilters();\n  }\n  trackByTitle(index, item) {\n    return item.title;\n  }\n  refreshNews() {\n    this.loadNews();\n  }\n};\n__decorate([Input()], NewsComponent.prototype, \"showTitle\", void 0);\n__decorate([Input()], NewsComponent.prototype, \"compact\", void 0);\nNewsComponent = __decorate([Component({\n  selector: 'app-news',\n  templateUrl: './news.component.html',\n  styleUrls: ['./news.component.scss']\n})], NewsComponent);", "map": {"version": 3, "names": ["Component", "Input", "NewsComponent", "constructor", "authService", "showTitle", "compact", "news", "loading", "error", "filteredNews", "searchTerm", "selectedCountry", "selectedSource", "availableCountries", "availableSources", "ngOnInit", "loadNews", "ensureAuthenticated", "subscribe", "next", "fetchNews", "response", "initializeFilters", "applyFilters", "console", "Set", "map", "item", "country", "sort", "source", "filtered", "trim", "searchLower", "toLowerCase", "filter", "title", "includes", "description", "onSearchChange", "event", "target", "value", "onCountryChange", "onSourceChange", "clearFilters", "trackByTitle", "index", "refreshNews", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\news_separate\\frontend\\src\\app\\news\\news.component.ts"], "sourcesContent": ["import { Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';\nimport { AuthService } from '../auth.service';\n\nexport interface NewsItem {\n  country: string;\n  source: string;\n  title: string;\n  link: string;\n  published: string; // ISO string\n  description: string;\n  fetched_at?: string;\n  // allow extra fields (e.g., id, created_at) from backend without strict typing\n  [key: string]: any;\n}\n\n@Component({\n  selector: 'app-news',\n  templateUrl: './news.component.html',\n  styleUrls: ['./news.component.scss']\n})\nexport class NewsComponent implements OnInit {\n  @Input() showTitle: boolean = true;\n  @Input() compact: boolean = false;\n\n  // News data\n  news: NewsItem[] = [];\n  loading: boolean = false;\n  error: string = '';\n\n  // Filter properties\n  filteredNews: NewsItem[] = [];\n  searchTerm: string = '';\n  selectedCountry: string = '';\n  selectedSource: string = '';\n\n  // Available filter options\n  availableCountries: string[] = [];\n  availableSources: string[] = [];\n\n  constructor(private authService: AuthService) {}\n\n  ngOnInit() {\n    this.loadNews();\n  }\n\n  loadNews() {\n    this.loading = true;\n    this.error = '';\n    \n    this.authService.ensureAuthenticated().subscribe({\n      next: () => {\n        this.authService.fetchNews('poland').subscribe({\n          next: (response) => {\n            this.news = response.news || [];\n            this.initializeFilters();\n            this.applyFilters();\n            this.loading = false;\n          },\n          error: (error) => {\n            this.error = 'Failed to load news';\n            this.loading = false;\n            console.error('Error loading news:', error);\n          }\n        });\n      },\n      error: (error) => {\n        this.error = 'Authentication failed';\n        this.loading = false;\n        console.error('Auth error:', error);\n      }\n    });\n  }\n\n  initializeFilters() {\n    this.availableCountries = [...new Set(this.news.map(item => item.country))].sort();\n    this.availableSources = [...new Set(this.news.map(item => item.source))].sort();\n  }\n\n  applyFilters() {\n    let filtered = [...this.news];\n\n    // Apply search filter\n    if (this.searchTerm.trim()) {\n      const searchLower = this.searchTerm.toLowerCase();\n      filtered = filtered.filter(item =>\n        item.title.toLowerCase().includes(searchLower) ||\n        item.description.toLowerCase().includes(searchLower) ||\n        item.source.toLowerCase().includes(searchLower)\n      );\n    }\n\n    // Apply country filter\n    if (this.selectedCountry) {\n      filtered = filtered.filter(item => item.country === this.selectedCountry);\n    }\n\n    // Apply source filter\n    if (this.selectedSource) {\n      filtered = filtered.filter(item => item.source === this.selectedSource);\n    }\n\n    this.filteredNews = filtered;\n  }\n\n  onSearchChange(event: any) {\n    this.searchTerm = event.target.value;\n    this.applyFilters();\n  }\n\n  onCountryChange(event: any) {\n    this.selectedCountry = event.target.value;\n    this.applyFilters();\n  }\n\n  onSourceChange(event: any) {\n    this.selectedSource = event.target.value;\n    this.applyFilters();\n  }\n\n  clearFilters() {\n    this.searchTerm = '';\n    this.selectedCountry = '';\n    this.selectedSource = '';\n    this.applyFilters();\n  }\n\n  trackByTitle(index: number, item: NewsItem) {\n    return item.title;\n  }\n\n  refreshNews() {\n    this.loadNews();\n  }\n}"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,KAAK,QAA0C,eAAe;AAoB3E,WAAMC,aAAa,GAAnB,MAAMA,aAAa;EAmBxBC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAlBtB,KAAAC,SAAS,GAAY,IAAI;IACzB,KAAAC,OAAO,GAAY,KAAK;IAEjC;IACA,KAAAC,IAAI,GAAe,EAAE;IACrB,KAAAC,OAAO,GAAY,KAAK;IACxB,KAAAC,KAAK,GAAW,EAAE;IAElB;IACA,KAAAC,YAAY,GAAe,EAAE;IAC7B,KAAAC,UAAU,GAAW,EAAE;IACvB,KAAAC,eAAe,GAAW,EAAE;IAC5B,KAAAC,cAAc,GAAW,EAAE;IAE3B;IACA,KAAAC,kBAAkB,GAAa,EAAE;IACjC,KAAAC,gBAAgB,GAAa,EAAE;EAEgB;EAE/CC,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,EAAE;EACjB;EAEAA,QAAQA,CAAA;IACN,IAAI,CAACT,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,KAAK,GAAG,EAAE;IAEf,IAAI,CAACL,WAAW,CAACc,mBAAmB,EAAE,CAACC,SAAS,CAAC;MAC/CC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAChB,WAAW,CAACiB,SAAS,CAAC,QAAQ,CAAC,CAACF,SAAS,CAAC;UAC7CC,IAAI,EAAGE,QAAQ,IAAI;YACjB,IAAI,CAACf,IAAI,GAAGe,QAAQ,CAACf,IAAI,IAAI,EAAE;YAC/B,IAAI,CAACgB,iBAAiB,EAAE;YACxB,IAAI,CAACC,YAAY,EAAE;YACnB,IAAI,CAAChB,OAAO,GAAG,KAAK;UACtB,CAAC;UACDC,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAACA,KAAK,GAAG,qBAAqB;YAClC,IAAI,CAACD,OAAO,GAAG,KAAK;YACpBiB,OAAO,CAAChB,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;UAC7C;SACD,CAAC;MACJ,CAAC;MACDA,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACA,KAAK,GAAG,uBAAuB;QACpC,IAAI,CAACD,OAAO,GAAG,KAAK;QACpBiB,OAAO,CAAChB,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACrC;KACD,CAAC;EACJ;EAEAc,iBAAiBA,CAAA;IACf,IAAI,CAACT,kBAAkB,GAAG,CAAC,GAAG,IAAIY,GAAG,CAAC,IAAI,CAACnB,IAAI,CAACoB,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,OAAO,CAAC,CAAC,CAAC,CAACC,IAAI,EAAE;IAClF,IAAI,CAACf,gBAAgB,GAAG,CAAC,GAAG,IAAIW,GAAG,CAAC,IAAI,CAACnB,IAAI,CAACoB,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACG,MAAM,CAAC,CAAC,CAAC,CAACD,IAAI,EAAE;EACjF;EAEAN,YAAYA,CAAA;IACV,IAAIQ,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACzB,IAAI,CAAC;IAE7B;IACA,IAAI,IAAI,CAACI,UAAU,CAACsB,IAAI,EAAE,EAAE;MAC1B,MAAMC,WAAW,GAAG,IAAI,CAACvB,UAAU,CAACwB,WAAW,EAAE;MACjDH,QAAQ,GAAGA,QAAQ,CAACI,MAAM,CAACR,IAAI,IAC7BA,IAAI,CAACS,KAAK,CAACF,WAAW,EAAE,CAACG,QAAQ,CAACJ,WAAW,CAAC,IAC9CN,IAAI,CAACW,WAAW,CAACJ,WAAW,EAAE,CAACG,QAAQ,CAACJ,WAAW,CAAC,IACpDN,IAAI,CAACG,MAAM,CAACI,WAAW,EAAE,CAACG,QAAQ,CAACJ,WAAW,CAAC,CAChD;;IAGH;IACA,IAAI,IAAI,CAACtB,eAAe,EAAE;MACxBoB,QAAQ,GAAGA,QAAQ,CAACI,MAAM,CAACR,IAAI,IAAIA,IAAI,CAACC,OAAO,KAAK,IAAI,CAACjB,eAAe,CAAC;;IAG3E;IACA,IAAI,IAAI,CAACC,cAAc,EAAE;MACvBmB,QAAQ,GAAGA,QAAQ,CAACI,MAAM,CAACR,IAAI,IAAIA,IAAI,CAACG,MAAM,KAAK,IAAI,CAAClB,cAAc,CAAC;;IAGzE,IAAI,CAACH,YAAY,GAAGsB,QAAQ;EAC9B;EAEAQ,cAAcA,CAACC,KAAU;IACvB,IAAI,CAAC9B,UAAU,GAAG8B,KAAK,CAACC,MAAM,CAACC,KAAK;IACpC,IAAI,CAACnB,YAAY,EAAE;EACrB;EAEAoB,eAAeA,CAACH,KAAU;IACxB,IAAI,CAAC7B,eAAe,GAAG6B,KAAK,CAACC,MAAM,CAACC,KAAK;IACzC,IAAI,CAACnB,YAAY,EAAE;EACrB;EAEAqB,cAAcA,CAACJ,KAAU;IACvB,IAAI,CAAC5B,cAAc,GAAG4B,KAAK,CAACC,MAAM,CAACC,KAAK;IACxC,IAAI,CAACnB,YAAY,EAAE;EACrB;EAEAsB,YAAYA,CAAA;IACV,IAAI,CAACnC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACW,YAAY,EAAE;EACrB;EAEAuB,YAAYA,CAACC,KAAa,EAAEpB,IAAc;IACxC,OAAOA,IAAI,CAACS,KAAK;EACnB;EAEAY,WAAWA,CAAA;IACT,IAAI,CAAChC,QAAQ,EAAE;EACjB;CACD;AAhHUiC,UAAA,EAARjD,KAAK,EAAE,C,+CAA2B;AAC1BiD,UAAA,EAARjD,KAAK,EAAE,C,6CAA0B;AAFvBC,aAAa,GAAAgD,UAAA,EALzBlD,SAAS,CAAC;EACTmD,QAAQ,EAAE,UAAU;EACpBC,WAAW,EAAE,uBAAuB;EACpCC,SAAS,EAAE,CAAC,uBAAuB;CACpC,CAAC,C,EACWnD,aAAa,CAiHzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}