{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../auth.service\";\nexport let AnonymousUserComponent = /*#__PURE__*/(() => {\n  class AnonymousUserComponent {\n    constructor(authService) {\n      this.authService = authService;\n    }\n    ngOnInit() {\n      this.initializeUser();\n    }\n    initializeUser() {\n      this.authService.ensureAuthenticated().subscribe(token => {\n        console.log('User authenticated successfully');\n      }, error => {\n        console.error('Failed to authenticate user:', error);\n      });\n    }\n    static #_ = this.ɵfac = function AnonymousUserComponent_Factory(t) {\n      return new (t || AnonymousUserComponent)(i0.ɵɵdirectiveInject(i1.AuthService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AnonymousUserComponent,\n      selectors: [[\"app-anonymous-user\"]],\n      decls: 0,\n      vars: 0,\n      template: function AnonymousUserComponent_Template(rf, ctx) {}\n    });\n  }\n  return AnonymousUserComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}