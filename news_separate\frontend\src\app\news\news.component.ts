import { Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { AuthService } from '../auth.service';

export interface NewsItem {
  country: string;
  source: string;
  title: string;
  link: string;
  published: string; // ISO string
  description: string;
  fetched_at?: string;
  // allow extra fields (e.g., id, created_at) from backend without strict typing
  [key: string]: any;
}

@Component({
  selector: 'app-news',
  templateUrl: './news.component.html',
  styleUrls: ['./news.component.scss']
})
export class NewsComponent implements OnInit {
  @Input() showTitle: boolean = true;
  @Input() compact: boolean = false;

  // News data
  news: NewsItem[] = [];
  loading: boolean = false;
  error: string = '';

  // Filter properties
  filteredNews: NewsItem[] = [];
  searchTerm: string = '';
  selectedCountry: string = '';
  selectedSource: string = '';

  // Available filter options
  availableCountries: string[] = [];
  availableSources: string[] = [];

  userCountry: string = '';

  constructor(private authService: AuthService,
  ) {}

  ngOnInit() {
    this.detectUserCountry();
  }
  
  detectUserCountry() {
    fetch('https://ipwhois.app/json/')
      .then(response => response.json())
      .then(data => {
        this.userCountry = data.country_code.toLowerCase();
        console.log('User country detected:', data.country, data.country_code);
        
        const supportedCountries = ['czech', 'russia', 'ukraine', 'poland', 'slovakia', 'bulgaria', 'croatia', 'serbia'];
        this.userCountry = 'poland';

        if (supportedCountries.includes(this.userCountry)) {
          console.log('Country is supported, loading news for:', this.userCountry);
          this.loadNews(this.userCountry);
        } else {
          console.log('Country not supported by API');
        }
      })
      .catch(error => {
        console.error('IP detection failed:', error);
      });
  }
  
  loadNews(country: string = 'poland') {
    this.loading = true;
    this.error = '';
    
    this.authService.ensureAuthenticated().subscribe({
      next: () => {
        this.authService.fetchNews(country).subscribe({
          next: (response) => {
            this.news = response.news || [];
            this.initializeFilters();
            this.applyFilters();
            this.loading = false;
          },
          error: (error) => {
            this.error = 'Failed to load news';
            this.loading = false;
            console.error('Error loading news:', error);
          }
        });
      },
      error: (error) => {
        this.error = 'Authentication failed';
        this.loading = false;
        console.error('Auth error:', error);
      }
    });
  }

  initializeFilters() {
    this.availableCountries = [...new Set(this.news.map(item => item.country))].sort();
    this.availableSources = [...new Set(this.news.map(item => item.source))].sort();
  }

  applyFilters() {
    let filtered = [...this.news];

    // Apply search filter
    if (this.searchTerm.trim()) {
      const searchLower = this.searchTerm.toLowerCase();
      filtered = filtered.filter(item =>
        item.title.toLowerCase().includes(searchLower) ||
        item.description.toLowerCase().includes(searchLower) ||
        item.source.toLowerCase().includes(searchLower)
      );
    }

    // Apply country filter
    if (this.selectedCountry) {
      filtered = filtered.filter(item => item.country === this.selectedCountry);
    }

    // Apply source filter
    if (this.selectedSource) {
      filtered = filtered.filter(item => item.source === this.selectedSource);
    }

    this.filteredNews = filtered;
  }

  onSearchChange(event: any) {
    this.searchTerm = event.target.value;
    this.applyFilters();
  }

  onCountryChange(event: any) {
    this.selectedCountry = event.target.value;
    this.applyFilters();
  }

  onSourceChange(event: any) {
    this.selectedSource = event.target.value;
    this.applyFilters();
  }

  clearFilters() {
    this.searchTerm = '';
    this.selectedCountry = '';
    this.selectedSource = '';
    this.applyFilters();
  }

  trackByTitle(index: number, item: NewsItem) {
    return item.title;
  }

  refreshNews() {
    this.loadNews();
  }
}