"use strict";(self.webpackChunkfrontend=self.webpackChunkfrontend||[]).push([[179],{382:()=>{function ee(e){return"function"==typeof e}function $o(e){const n=e(r=>{Error.call(r),r.stack=(new Error).stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}const Ds=$o(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:\n${n.map((r,o)=>`${o+1}) ${r.toString()}`).join("\n  ")}`:"",this.name="UnsubscriptionError",this.errors=n});function Bo(e,t){if(e){const n=e.indexOf(t);0<=n&&e.splice(n,1)}}class st{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;const{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(const i of n)i.remove(this);else n.remove(this);const{initialTeardown:r}=this;if(ee(r))try{r()}catch(i){t=i instanceof Ds?i.errors:[i]}const{_finalizers:o}=this;if(o){this._finalizers=null;for(const i of o)try{Kh(i)}catch(s){t=t??[],s instanceof Ds?t=[...t,...s.errors]:t.push(s)}}if(t)throw new Ds(t)}}add(t){var n;if(t&&t!==this)if(this.closed)Kh(t);else{if(t instanceof st){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=null!==(n=this._finalizers)&&void 0!==n?n:[]).push(t)}}_hasParent(t){const{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){const{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){const{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&Bo(n,t)}remove(t){const{_finalizers:n}=this;n&&Bo(n,t),t instanceof st&&t._removeParent(this)}}st.EMPTY=(()=>{const e=new st;return e.closed=!0,e})();const Xh=st.EMPTY;function Jh(e){return e instanceof st||e&&"closed"in e&&ee(e.remove)&&ee(e.add)&&ee(e.unsubscribe)}function Kh(e){ee(e)?e():e.unsubscribe()}const Kn={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1},ws={setTimeout(e,t,...n){const{delegate:r}=ws;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){const{delegate:t}=ws;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function ep(e){ws.setTimeout(()=>{const{onUnhandledError:t}=Kn;if(!t)throw e;t(e)})}function ec(){}const r0=tc("C",void 0,void 0);function tc(e,t,n){return{kind:e,value:t,error:n}}let er=null;function bs(e){if(Kn.useDeprecatedSynchronousErrorHandling){const t=!er;if(t&&(er={errorThrown:!1,error:null}),e(),t){const{errorThrown:n,error:r}=er;if(er=null,n)throw r}}else e()}class nc extends st{constructor(t){super(),this.isStopped=!1,t?(this.destination=t,Jh(t)&&t.add(this)):this.destination=d0}static create(t,n,r){return new Ho(t,n,r)}next(t){this.isStopped?oc(function s0(e){return tc("N",e,void 0)}(t),this):this._next(t)}error(t){this.isStopped?oc(function o0(e){return tc("E",void 0,e)}(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?oc(r0,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}}const l0=Function.prototype.bind;function rc(e,t){return l0.call(e,t)}class c0{constructor(t){this.partialObserver=t}next(t){const{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){Es(r)}}error(t){const{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){Es(r)}else Es(t)}complete(){const{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){Es(n)}}}class Ho extends nc{constructor(t,n,r){let o;if(super(),ee(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&Kn.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&rc(t.next,i),error:t.error&&rc(t.error,i),complete:t.complete&&rc(t.complete,i)}):o=t}this.destination=new c0(o)}}function Es(e){Kn.useDeprecatedSynchronousErrorHandling?function a0(e){Kn.useDeprecatedSynchronousErrorHandling&&er&&(er.errorThrown=!0,er.error=e)}(e):ep(e)}function oc(e,t){const{onStoppedNotification:n}=Kn;n&&ws.setTimeout(()=>n(e,t))}const d0={closed:!0,next:ec,error:function u0(e){throw e},complete:ec},ic="function"==typeof Symbol&&Symbol.observable||"@@observable";function On(e){return e}function tp(e){return 0===e.length?On:1===e.length?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}let he=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){const r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){const i=function p0(e){return e&&e instanceof nc||function h0(e){return e&&ee(e.next)&&ee(e.error)&&ee(e.complete)}(e)&&Jh(e)}(n)?n:new Ho(n,r,o);return bs(()=>{const{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return new(r=np(r))((o,i)=>{const s=new Ho({next:a=>{try{n(a)}catch(l){i(l),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return null===(r=this.source)||void 0===r?void 0:r.subscribe(n)}[ic](){return this}pipe(...n){return tp(n)(this)}toPromise(n){return new(n=np(n))((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function np(e){var t;return null!==(t=e??Kn.Promise)&&void 0!==t?t:Promise}const g0=$o(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});let Ot=(()=>{class e extends he{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){const r=new rp(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new g0}next(n){bs(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(const r of this.currentObservers)r.next(n)}})}error(n){bs(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;const{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){bs(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;const{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return(null===(n=this.observers)||void 0===n?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){const{hasError:r,isStopped:o,observers:i}=this;return r||o?Xh:(this.currentObservers=null,i.push(n),new st(()=>{this.currentObservers=null,Bo(i,n)}))}_checkFinalizedStatuses(n){const{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){const n=new he;return n.source=this,n}}return e.create=(t,n)=>new rp(t,n),e})();class rp extends Ot{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;null===(r=null===(n=this.destination)||void 0===n?void 0:n.next)||void 0===r||r.call(n,t)}error(t){var n,r;null===(r=null===(n=this.destination)||void 0===n?void 0:n.error)||void 0===r||r.call(n,t)}complete(){var t,n;null===(n=null===(t=this.destination)||void 0===t?void 0:t.complete)||void 0===n||n.call(t)}_subscribe(t){var n,r;return null!==(r=null===(n=this.source)||void 0===n?void 0:n.subscribe(t))&&void 0!==r?r:Xh}}function op(e){return ee(e?.lift)}function Ee(e){return t=>{if(op(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function De(e,t,n,r,o){return new m0(e,t,n,r,o)}class m0 extends nc{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(l){t.error(l)}}:super._next,this._error=o?function(a){try{o(a)}catch(l){t.error(l)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){const{closed:n}=this;super.unsubscribe(),!n&&(null===(t=this.onFinalize)||void 0===t||t.call(this))}}}function z(e,t){return Ee((n,r)=>{let o=0;n.subscribe(De(r,i=>{r.next(e.call(t,i,o++))}))})}function Nn(e){return this instanceof Nn?(this.v=e,this):new Nn(e)}function lp(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n,t=e[Symbol.asyncIterator];return t?t.call(e):(e=function cc(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}(e),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,l){!function o(i,s,a,l){Promise.resolve(l).then(function(c){i({value:c,done:a})},s)}(a,l,(s=e[i](s)).done,s.value)})}}}"function"==typeof SuppressedError&&SuppressedError;const cp=e=>e&&"number"==typeof e.length&&"function"!=typeof e;function up(e){return ee(e?.then)}function dp(e){return ee(e[ic])}function fp(e){return Symbol.asyncIterator&&ee(e?.[Symbol.asyncIterator])}function hp(e){return new TypeError(`You provided ${null!==e&&"object"==typeof e?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}const pp=function V0(){return"function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator"}();function gp(e){return ee(e?.[pp])}function mp(e){return function ap(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var o,r=n.apply(e,t||[]),i=[];return o={},a("next"),a("throw"),a("return",function s(h){return function(p){return Promise.resolve(p).then(h,d)}}),o[Symbol.asyncIterator]=function(){return this},o;function a(h,p){r[h]&&(o[h]=function(g){return new Promise(function(y,_){i.push([h,g,y,_])>1||l(h,g)})},p&&(o[h]=p(o[h])))}function l(h,p){try{!function c(h){h.value instanceof Nn?Promise.resolve(h.value.v).then(u,d):f(i[0][2],h)}(r[h](p))}catch(g){f(i[0][3],g)}}function u(h){l("next",h)}function d(h){l("throw",h)}function f(h,p){h(p),i.shift(),i.length&&l(i[0][0],i[0][1])}}(this,arguments,function*(){const n=e.getReader();try{for(;;){const{value:r,done:o}=yield Nn(n.read());if(o)return yield Nn(void 0);yield yield Nn(r)}}finally{n.releaseLock()}})}function yp(e){return ee(e?.getReader)}function at(e){if(e instanceof he)return e;if(null!=e){if(dp(e))return function j0(e){return new he(t=>{const n=e[ic]();if(ee(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}(e);if(cp(e))return function $0(e){return new he(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}(e);if(up(e))return function B0(e){return new he(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,ep)})}(e);if(fp(e))return vp(e);if(gp(e))return function H0(e){return new he(t=>{for(const n of e)if(t.next(n),t.closed)return;t.complete()})}(e);if(yp(e))return function U0(e){return vp(mp(e))}(e)}throw hp(e)}function vp(e){return new he(t=>{(function z0(e,t){var n,r,o,i;return function ip(e,t,n,r){return new(n||(n=Promise))(function(i,s){function a(u){try{c(r.next(u))}catch(d){s(d)}}function l(u){try{c(r.throw(u))}catch(d){s(d)}}function c(u){u.done?i(u.value):function o(i){return i instanceof n?i:new n(function(s){s(i)})}(u.value).then(a,l)}c((r=r.apply(e,t||[])).next())})}(this,void 0,void 0,function*(){try{for(n=lp(e);!(r=yield n.next()).done;)if(t.next(r.value),t.closed)return}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})})(e,t).catch(n=>t.error(n))})}function cn(e,t,n,r=0,o=!1){const i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Ae(e,t,n=1/0){return ee(t)?Ae((r,o)=>z((i,s)=>t(r,i,o,s))(at(e(r,o))),n):("number"==typeof t&&(n=t),Ee((r,o)=>function G0(e,t,n,r,o,i,s,a){const l=[];let c=0,u=0,d=!1;const f=()=>{d&&!l.length&&!c&&t.complete()},h=g=>c<r?p(g):l.push(g),p=g=>{i&&t.next(g),c++;let y=!1;at(n(g,u++)).subscribe(De(t,_=>{o?.(_),i?h(_):t.next(_)},()=>{y=!0},void 0,()=>{if(y)try{for(c--;l.length&&c<r;){const _=l.shift();s?cn(t,s,()=>p(_)):p(_)}f()}catch(_){t.error(_)}}))};return e.subscribe(De(t,h,()=>{d=!0,f()})),()=>{a?.()}}(r,o,e,n)))}function Sr(e=1/0){return Ae(On,e)}const Gt=new he(e=>e.complete());function uc(e){return e[e.length-1]}function _p(e){return ee(uc(e))?e.pop():void 0}function Uo(e){return function W0(e){return e&&ee(e.schedule)}(uc(e))?e.pop():void 0}function Cp(e,t=0){return Ee((n,r)=>{n.subscribe(De(r,o=>cn(r,e,()=>r.next(o),t),()=>cn(r,e,()=>r.complete(),t),o=>cn(r,e,()=>r.error(o),t)))})}function Dp(e,t=0){return Ee((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function wp(e,t){if(!e)throw new Error("Iterable cannot be null");return new he(n=>{cn(n,t,()=>{const r=e[Symbol.asyncIterator]();cn(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function Me(e,t){return t?function eM(e,t){if(null!=e){if(dp(e))return function Y0(e,t){return at(e).pipe(Dp(t),Cp(t))}(e,t);if(cp(e))return function X0(e,t){return new he(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}(e,t);if(up(e))return function Q0(e,t){return at(e).pipe(Dp(t),Cp(t))}(e,t);if(fp(e))return wp(e,t);if(gp(e))return function J0(e,t){return new he(n=>{let r;return cn(n,t,()=>{r=e[pp](),cn(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){return void n.error(s)}i?n.complete():n.next(o)},0,!0)}),()=>ee(r?.return)&&r.return()})}(e,t);if(yp(e))return function K0(e,t){return wp(mp(e),t)}(e,t)}throw hp(e)}(e,t):at(e)}class Ct extends Ot{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){const n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){const{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}}function A(...e){return Me(e,Uo(e))}function bp(e={}){const{connector:t=(()=>new Ot),resetOnError:n=!0,resetOnComplete:r=!0,resetOnRefCountZero:o=!0}=e;return i=>{let s,a,l,c=0,u=!1,d=!1;const f=()=>{a?.unsubscribe(),a=void 0},h=()=>{f(),s=l=void 0,u=d=!1},p=()=>{const g=s;h(),g?.unsubscribe()};return Ee((g,y)=>{c++,!d&&!u&&f();const _=l=l??t();y.add(()=>{c--,0===c&&!d&&!u&&(a=dc(p,o))}),_.subscribe(y),!s&&c>0&&(s=new Ho({next:m=>_.next(m),error:m=>{d=!0,f(),a=dc(h,n,m),_.error(m)},complete:()=>{u=!0,f(),a=dc(h,r),_.complete()}}),at(g).subscribe(s))})(i)}}function dc(e,t,...n){if(!0===t)return void e();if(!1===t)return;const r=new Ho({next:()=>{r.unsubscribe(),e()}});return at(t(...n)).subscribe(r)}function Nt(e,t){return Ee((n,r)=>{let o=null,i=0,s=!1;const a=()=>s&&!o&&r.complete();n.subscribe(De(r,l=>{o?.unsubscribe();let c=0;const u=i++;at(e(l,u)).subscribe(o=De(r,d=>r.next(t?t(l,d,u,c++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function rM(e,t){return e===t}function X(e){for(let t in e)if(e[t]===X)return t;throw Error("Could not find renamed property on target object.")}function Ms(e,t){for(const n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function Ie(e){if("string"==typeof e)return e;if(Array.isArray(e))return"["+e.map(Ie).join(", ")+"]";if(null==e)return""+e;if(e.overriddenName)return`${e.overriddenName}`;if(e.name)return`${e.name}`;const t=e.toString();if(null==t)return""+t;const n=t.indexOf("\n");return-1===n?t:t.substring(0,n)}function fc(e,t){return null==e||""===e?null===t?"":t:null==t||""===t?e:e+" "+t}const oM=X({__forward_ref__:X});function ne(e){return e.__forward_ref__=ne,e.toString=function(){return Ie(this())},e}function N(e){return hc(e)?e():e}function hc(e){return"function"==typeof e&&e.hasOwnProperty(oM)&&e.__forward_ref__===ne}function pc(e){return e&&!!e.\u0275providers}const Ep="https://g.co/ng/security#xss";class C extends Error{constructor(t,n){super(function Is(e,t){return`NG0${Math.abs(e)}${t?": "+t:""}`}(t,n)),this.code=t}}function R(e){return"string"==typeof e?e:null==e?"":String(e)}function gc(e,t){throw new C(-201,!1)}function Dt(e,t){null==e&&function T(e,t,n,r){throw new Error(`ASSERTION ERROR: ${e}`+(null==r?"":` [Expected=> ${n} ${r} ${t} <=Actual]`))}(t,e,null,"!=")}function x(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function ct(e){return{providers:e.providers||[],imports:e.imports||[]}}function Ss(e){return Mp(e,As)||Mp(e,Ip)}function Mp(e,t){return e.hasOwnProperty(t)?e[t]:null}function xs(e){return e&&(e.hasOwnProperty(mc)||e.hasOwnProperty(fM))?e[mc]:null}const As=X({\u0275prov:X}),mc=X({\u0275inj:X}),Ip=X({ngInjectableDef:X}),fM=X({ngInjectorDef:X});var $=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}($||{});let yc;function Ke(e){const t=yc;return yc=e,t}function xp(e,t,n){const r=Ss(e);return r&&"root"==r.providedIn?void 0===r.value?r.value=r.factory():r.value:n&$.Optional?null:void 0!==t?t:void gc(Ie(e))}const re=globalThis;class M{constructor(t,n){this._desc=t,this.ngMetadataName="InjectionToken",this.\u0275prov=void 0,"number"==typeof n?this.__NG_ELEMENT_ID__=n:void 0!==n&&(this.\u0275prov=x({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}}const zo={},wc="__NG_DI_FLAG__",Ts="ngTempTokenPath",gM=/\n/gm,Tp="__source";let xr;function Pn(e){const t=xr;return xr=e,t}function vM(e,t=$.Default){if(void 0===xr)throw new C(-203,!1);return null===xr?xp(e,void 0,t):xr.get(e,t&$.Optional?null:void 0,t)}function I(e,t=$.Default){return(function Sp(){return yc}()||vM)(N(e),t)}function E(e,t=$.Default){return I(e,Os(t))}function Os(e){return typeof e>"u"||"number"==typeof e?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function bc(e){const t=[];for(let n=0;n<e.length;n++){const r=N(e[n]);if(Array.isArray(r)){if(0===r.length)throw new C(900,!1);let o,i=$.Default;for(let s=0;s<r.length;s++){const a=r[s],l=_M(a);"number"==typeof l?-1===l?o=a.token:i|=l:o=a}t.push(I(o,i))}else t.push(I(r))}return t}function Go(e,t){return e[wc]=t,e.prototype[wc]=t,e}function _M(e){return e[wc]}function un(e){return{toString:e}.toString()}var Ns=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Ns||{}),Pt=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(Pt||{});const qt={},G=[],Ps=X({\u0275cmp:X}),Ec=X({\u0275dir:X}),Mc=X({\u0275pipe:X}),Np=X({\u0275mod:X}),dn=X({\u0275fac:X}),qo=X({__NG_ELEMENT_ID__:X}),Pp=X({__NG_ENV_ID__:X});function Rp(e,t,n){let r=e.length;for(;;){const o=e.indexOf(t,n);if(-1===o)return o;if(0===o||e.charCodeAt(o-1)<=32){const i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}function Ic(e,t,n){let r=0;for(;r<n.length;){const o=n[r];if("number"==typeof o){if(0!==o)break;r++;const i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{const i=o,s=n[++r];Fp(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function kp(e){return 3===e||4===e||6===e}function Fp(e){return 64===e.charCodeAt(0)}function Wo(e,t){if(null!==t&&0!==t.length)if(null===e||0===e.length)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){const o=t[r];"number"==typeof o?n=o:0===n||Lp(e,n,o,null,-1===n||2===n?t[++r]:null)}}return e}function Lp(e,t,n,r,o){let i=0,s=e.length;if(-1===t)s=-1;else for(;i<e.length;){const a=e[i++];if("number"==typeof a){if(a===t){s=-1;break}if(a>t){s=i-1;break}}}for(;i<e.length;){const a=e[i];if("number"==typeof a)break;if(a===n){if(null===r)return void(null!==o&&(e[i+1]=o));if(r===e[i+1])return void(e[i+2]=o)}i++,null!==r&&i++,null!==o&&i++}-1!==s&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),null!==r&&e.splice(i++,0,r),null!==o&&e.splice(i++,0,o)}const Vp="ng-template";function wM(e,t,n){let r=0,o=!0;for(;r<e.length;){let i=e[r++];if("string"==typeof i&&o){const s=e[r++];if(n&&"class"===i&&-1!==Rp(s.toLowerCase(),t,0))return!0}else{if(1===i){for(;r<e.length&&"string"==typeof(i=e[r++]);)if(i.toLowerCase()===t)return!0;return!1}"number"==typeof i&&(o=!1)}}return!1}function jp(e){return 4===e.type&&e.value!==Vp}function bM(e,t,n){return t===(4!==e.type||n?e.value:Vp)}function EM(e,t,n){let r=4;const o=e.attrs||[],i=function SM(e){for(let t=0;t<e.length;t++)if(kp(e[t]))return t;return e.length}(o);let s=!1;for(let a=0;a<t.length;a++){const l=t[a];if("number"!=typeof l){if(!s)if(4&r){if(r=2|1&r,""!==l&&!bM(e,l,n)||""===l&&1===t.length){if(Rt(r))return!1;s=!0}}else{const c=8&r?l:t[++a];if(8&r&&null!==e.attrs){if(!wM(e.attrs,c,n)){if(Rt(r))return!1;s=!0}continue}const d=MM(8&r?"class":l,o,jp(e),n);if(-1===d){if(Rt(r))return!1;s=!0;continue}if(""!==c){let f;f=d>i?"":o[d+1].toLowerCase();const h=8&r?f:null;if(h&&-1!==Rp(h,c,0)||2&r&&c!==f){if(Rt(r))return!1;s=!0}}}}else{if(!s&&!Rt(r)&&!Rt(l))return!1;if(s&&Rt(l))continue;s=!1,r=l|1&r}}return Rt(r)||s}function Rt(e){return 0==(1&e)}function MM(e,t,n,r){if(null===t)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){const s=t[o];if(s===e)return o;if(3===s||6===s)i=!0;else{if(1===s||2===s){let a=t[++o];for(;"string"==typeof a;)a=t[++o];continue}if(4===s)break;if(0===s){o+=4;continue}}o+=i?1:2}return-1}return function xM(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){const r=e[n];if("number"==typeof r)return-1;if(r===t)return n;n++}return-1}(t,e)}function $p(e,t,n=!1){for(let r=0;r<t.length;r++)if(EM(e,t[r],n))return!0;return!1}function Bp(e,t){return e?":not("+t.trim()+")":t}function TM(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if("string"==typeof s)if(2&r){const a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else 8&r?o+="."+s:4&r&&(o+=" "+s);else""!==o&&!Rt(s)&&(t+=Bp(i,o),o=""),r=s,i=i||!Rt(r);n++}return""!==o&&(t+=Bp(i,o)),t}function Ar(e){return un(()=>{const t=Up(e),n={...t,decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Ns.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||Pt.Emulated,styles:e.styles||G,_:null,schemas:e.schemas||null,tView:null,id:""};zp(n);const r=e.dependencies;return n.directiveDefs=Rs(r,!1),n.pipeDefs=Rs(r,!0),n.id=function VM(e){let t=0;const n=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,e.consts,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery].join("|");for(const o of n)t=Math.imul(31,t)+o.charCodeAt(0)<<0;return t+=2147483648,"c"+t}(n),n})}function RM(e){return H(e)||Te(e)}function kM(e){return null!==e}function wt(e){return un(()=>({type:e.type,bootstrap:e.bootstrap||G,declarations:e.declarations||G,imports:e.imports||G,exports:e.exports||G,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function Hp(e,t){if(null==e)return qt;const n={};for(const r in e)if(e.hasOwnProperty(r)){let o=e[r],i=o;Array.isArray(o)&&(i=o[1],o=o[0]),n[o]=r,t&&(t[o]=i)}return n}function P(e){return un(()=>{const t=Up(e);return zp(t),t})}function et(e){return{type:e.type,name:e.name,factory:null,pure:!1!==e.pure,standalone:!0===e.standalone,onDestroy:e.type.prototype.ngOnDestroy||null}}function H(e){return e[Ps]||null}function Te(e){return e[Ec]||null}function Ue(e){return e[Mc]||null}function ut(e,t){const n=e[Np]||null;if(!n&&!0===t)throw new Error(`Type ${Ie(e)} does not have '\u0275mod' property.`);return n}function Up(e){const t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputTransforms:null,inputConfig:e.inputs||qt,exportAs:e.exportAs||null,standalone:!0===e.standalone,signals:!0===e.signals,selectors:e.selectors||G,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:Hp(e.inputs,t),outputs:Hp(e.outputs)}}function zp(e){e.features?.forEach(t=>t(e))}function Rs(e,t){if(!e)return null;const n=t?Ue:RM;return()=>("function"==typeof e?e():e).map(r=>n(r)).filter(kM)}const pe=0,b=1,L=2,ce=3,kt=4,Zo=5,Fe=6,Or=7,ve=8,Rn=9,Nr=10,k=11,Yo=12,Gp=13,Pr=14,_e=15,Qo=16,Rr=17,Wt=18,Xo=19,qp=20,kn=21,fn=22,Jo=23,Ko=24,B=25,Sc=1,Wp=2,Zt=7,kr=9,Oe=11;function tt(e){return Array.isArray(e)&&"object"==typeof e[Sc]}function ze(e){return Array.isArray(e)&&!0===e[Sc]}function xc(e){return 0!=(4&e.flags)}function nr(e){return e.componentOffset>-1}function Fs(e){return 1==(1&e.flags)}function Ft(e){return!!e.template}function Ac(e){return 0!=(512&e[L])}function rr(e,t){return e.hasOwnProperty(dn)?e[dn]:null}let Ne=null,Ls=!1;function bt(e){const t=Ne;return Ne=e,t}const Qp={version:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{}};function Jp(e){if(!ti(e)||e.dirty){if(!e.producerMustRecompute(e)&&!tg(e))return void(e.dirty=!1);e.producerRecomputeValue(e),e.dirty=!1}}function eg(e){e.dirty=!0,function Kp(e){if(void 0===e.liveConsumerNode)return;const t=Ls;Ls=!0;try{for(const n of e.liveConsumerNode)n.dirty||eg(n)}finally{Ls=t}}(e),e.consumerMarkedDirty?.(e)}function Oc(e){return e&&(e.nextProducerIndex=0),bt(e)}function Nc(e,t){if(bt(t),e&&void 0!==e.producerNode&&void 0!==e.producerIndexOfThis&&void 0!==e.producerLastReadVersion){if(ti(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)Vs(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function tg(e){Fr(e);for(let t=0;t<e.producerNode.length;t++){const n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(Jp(n),r!==n.version))return!0}return!1}function ng(e){if(Fr(e),ti(e))for(let t=0;t<e.producerNode.length;t++)Vs(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function Vs(e,t){if(function og(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}(e),Fr(e),1===e.liveConsumerNode.length)for(let r=0;r<e.producerNode.length;r++)Vs(e.producerNode[r],e.producerIndexOfThis[r]);const n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){const r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];Fr(o),o.producerIndexOfThis[r]=t}}function ti(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function Fr(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}let ig=null;const cg=()=>{},XM=(()=>({...Qp,consumerIsAlwaysLive:!0,consumerAllowSignalWrites:!1,consumerMarkedDirty:e=>{e.schedule(e.ref)},hasRun:!1,cleanupFn:cg}))();class JM{constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}}function Et(){return ug}function ug(e){return e.type.prototype.ngOnChanges&&(e.setInput=eI),KM}function KM(){const e=fg(this),t=e?.current;if(t){const n=e.previous;if(n===qt)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function eI(e,t,n,r){const o=this.declaredInputs[n],i=fg(e)||function tI(e,t){return e[dg]=t}(e,{previous:qt,current:null}),s=i.current||(i.current={}),a=i.previous,l=a[o];s[o]=new JM(l&&l.currentValue,t,a===qt),e[r]=t}Et.ngInherit=!0;const dg="__ngSimpleChanges__";function fg(e){return e[dg]||null}const Yt=function(e,t,n){},hg="svg";function oe(e){for(;Array.isArray(e);)e=e[pe];return e}function js(e,t){return oe(t[e])}function nt(e,t){return oe(t[e.index])}function gg(e,t){return e.data[t]}function Lr(e,t){return e[t]}function dt(e,t){const n=t[e];return tt(n)?n:n[pe]}function Ln(e,t){return null==t?null:e[t]}function mg(e){e[Rr]=0}function aI(e){1024&e[L]||(e[L]|=1024,vg(e,1))}function yg(e){1024&e[L]&&(e[L]&=-1025,vg(e,-1))}function vg(e,t){let n=e[ce];if(null===n)return;n[Zo]+=t;let r=n;for(n=n[ce];null!==n&&(1===t&&1===r[Zo]||-1===t&&0===r[Zo]);)n[Zo]+=t,r=n,n=n[ce]}const O={lFrame:Ag(null),bindingsEnabled:!0,skipHydrationRootTNode:null};function Dg(){return O.bindingsEnabled}function v(){return O.lFrame.lView}function U(){return O.lFrame.tView}function or(e){return O.lFrame.contextLView=e,e[ve]}function ir(e){return O.lFrame.contextLView=null,e}function Pe(){let e=wg();for(;null!==e&&64===e.type;)e=e.parent;return e}function wg(){return O.lFrame.currentTNode}function Qt(e,t){const n=O.lFrame;n.currentTNode=e,n.isParent=t}function Lc(){return O.lFrame.isParent}function Ge(){const e=O.lFrame;let t=e.bindingRootIndex;return-1===t&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function jr(){return O.lFrame.bindingIndex++}function _I(e,t){const n=O.lFrame;n.bindingIndex=n.bindingRootIndex=e,jc(t)}function jc(e){O.lFrame.currentDirectiveIndex=e}function Ig(){return O.lFrame.currentQueryIndex}function Bc(e){O.lFrame.currentQueryIndex=e}function DI(e){const t=e[b];return 2===t.type?t.declTNode:1===t.type?e[Fe]:null}function Sg(e,t,n){if(n&$.SkipSelf){let o=t,i=e;for(;!(o=o.parent,null!==o||n&$.Host||(o=DI(i),null===o||(i=i[Pr],10&o.type))););if(null===o)return!1;t=o,e=i}const r=O.lFrame=xg();return r.currentTNode=t,r.lView=e,!0}function Hc(e){const t=xg(),n=e[b];O.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function xg(){const e=O.lFrame,t=null===e?null:e.child;return null===t?Ag(e):t}function Ag(e){const t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return null!==e&&(e.child=t),t}function Tg(){const e=O.lFrame;return O.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}const Og=Tg;function Uc(){const e=Tg();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function qe(){return O.lFrame.selectedIndex}function sr(e){O.lFrame.selectedIndex=e}let Rg=!0;function $s(){return Rg}function Vn(e){Rg=e}function Bs(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){const i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:l,ngAfterViewChecked:c,ngOnDestroy:u}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),l&&(e.viewHooks??=[]).push(-n,l),c&&((e.viewHooks??=[]).push(n,c),(e.viewCheckHooks??=[]).push(n,c)),null!=u&&(e.destroyHooks??=[]).push(n,u)}}function Hs(e,t,n){kg(e,t,3,n)}function Us(e,t,n,r){(3&e[L])===n&&kg(e,t,n,r)}function zc(e,t){let n=e[L];(3&n)===t&&(n&=8191,n+=1,e[L]=n)}function kg(e,t,n,r){const i=r??-1,s=t.length-1;let a=0;for(let l=void 0!==r?65535&e[Rr]:0;l<s;l++)if("number"==typeof t[l+1]){if(a=t[l],null!=r&&a>=r)break}else t[l]<0&&(e[Rr]+=65536),(a<i||-1==i)&&(xI(e,n,t,l),e[Rr]=(**********&e[Rr])+l+2),l++}function Fg(e,t){Yt(4,e,t);const n=bt(null);try{t.call(e)}finally{bt(n),Yt(5,e,t)}}function xI(e,t,n,r){const o=n[r]<0,i=n[r+1],a=e[o?-n[r]:n[r]];o?e[L]>>13<e[Rr]>>16&&(3&e[L])===t&&(e[L]+=8192,Fg(a,i)):Fg(a,i)}const $r=-1;class ri{constructor(t,n,r){this.factory=t,this.resolving=!1,this.canSeeViewProviders=n,this.injectImpl=r}}function qc(e){return e!==$r}function oi(e){return 32767&e}function ii(e,t){let n=function NI(e){return e>>16}(e),r=t;for(;n>0;)r=r[Pr],n--;return r}let Wc=!0;function zs(e){const t=Wc;return Wc=e,t}const Lg=255,Vg=5;let PI=0;const Xt={};function Gs(e,t){const n=jg(e,t);if(-1!==n)return n;const r=t[b];r.firstCreatePass&&(e.injectorIndex=t.length,Zc(r.data,e),Zc(t,null),Zc(r.blueprint,null));const o=qs(e,t),i=e.injectorIndex;if(qc(o)){const s=oi(o),a=ii(o,t),l=a[b].data;for(let c=0;c<8;c++)t[i+c]=a[s+c]|l[s+c]}return t[i+8]=o,i}function Zc(e,t){e.push(0,0,0,0,0,0,0,0,t)}function jg(e,t){return-1===e.injectorIndex||e.parent&&e.parent.injectorIndex===e.injectorIndex||null===t[e.injectorIndex+8]?-1:e.injectorIndex}function qs(e,t){if(e.parent&&-1!==e.parent.injectorIndex)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;null!==o;){if(r=qg(o),null===r)return $r;if(n++,o=o[Pr],-1!==r.injectorIndex)return r.injectorIndex|n<<16}return $r}function Yc(e,t,n){!function RI(e,t,n){let r;"string"==typeof n?r=n.charCodeAt(0)||0:n.hasOwnProperty(qo)&&(r=n[qo]),null==r&&(r=n[qo]=PI++);const o=r&Lg;t.data[e+(o>>Vg)]|=1<<o}(e,t,n)}function $g(e,t,n){if(n&$.Optional||void 0!==e)return e;gc()}function Bg(e,t,n,r){if(n&$.Optional&&void 0===r&&(r=null),!(n&($.Self|$.Host))){const o=e[Rn],i=Ke(void 0);try{return o?o.get(t,r,n&$.Optional):xp(t,r,n&$.Optional)}finally{Ke(i)}}return $g(r,0,n)}function Hg(e,t,n,r=$.Default,o){if(null!==e){if(2048&t[L]&&!(r&$.Self)){const s=function $I(e,t,n,r,o){let i=e,s=t;for(;null!==i&&null!==s&&2048&s[L]&&!(512&s[L]);){const a=Ug(i,s,n,r|$.Self,Xt);if(a!==Xt)return a;let l=i.parent;if(!l){const c=s[qp];if(c){const u=c.get(n,Xt,r);if(u!==Xt)return u}l=qg(s),s=s[Pr]}i=l}return o}(e,t,n,r,Xt);if(s!==Xt)return s}const i=Ug(e,t,n,r,Xt);if(i!==Xt)return i}return Bg(t,n,r,o)}function Ug(e,t,n,r,o){const i=function LI(e){if("string"==typeof e)return e.charCodeAt(0)||0;const t=e.hasOwnProperty(qo)?e[qo]:void 0;return"number"==typeof t?t>=0?t&Lg:jI:t}(n);if("function"==typeof i){if(!Sg(t,e,r))return r&$.Host?$g(o,0,r):Bg(t,n,r,o);try{let s;if(s=i(r),null!=s||r&$.Optional)return s;gc()}finally{Og()}}else if("number"==typeof i){let s=null,a=jg(e,t),l=$r,c=r&$.Host?t[_e][Fe]:null;for((-1===a||r&$.SkipSelf)&&(l=-1===a?qs(e,t):t[a+8],l!==$r&&Gg(r,!1)?(s=t[b],a=oi(l),t=ii(l,t)):a=-1);-1!==a;){const u=t[b];if(zg(i,a,u.data)){const d=FI(a,t,n,s,r,c);if(d!==Xt)return d}l=t[a+8],l!==$r&&Gg(r,t[b].data[a+8]===c)&&zg(i,a,t)?(s=u,a=oi(l),t=ii(l,t)):a=-1}}return o}function FI(e,t,n,r,o,i){const s=t[b],a=s.data[e+8],u=Ws(a,s,n,null==r?nr(a)&&Wc:r!=s&&0!=(3&a.type),o&$.Host&&i===a);return null!==u?ar(t,s,u,a):Xt}function Ws(e,t,n,r,o){const i=e.providerIndexes,s=t.data,a=1048575&i,l=e.directiveStart,u=i>>20,f=o?a+u:e.directiveEnd;for(let h=r?a:a+u;h<f;h++){const p=s[h];if(h<l&&n===p||h>=l&&p.type===n)return h}if(o){const h=s[l];if(h&&Ft(h)&&h.type===n)return l}return null}function ar(e,t,n,r){let o=e[n];const i=t.data;if(function AI(e){return e instanceof ri}(o)){const s=o;s.resolving&&function iM(e,t){const n=t?`. Dependency path: ${t.join(" > ")} > ${e}`:"";throw new C(-200,`Circular dependency in DI detected for ${e}${n}`)}(function Z(e){return"function"==typeof e?e.name||e.toString():"object"==typeof e&&null!=e&&"function"==typeof e.type?e.type.name||e.type.toString():R(e)}(i[n]));const a=zs(s.canSeeViewProviders);s.resolving=!0;const c=s.injectImpl?Ke(s.injectImpl):null;Sg(e,r,$.Default);try{o=e[n]=s.factory(void 0,i,e,r),t.firstCreatePass&&n>=r.directiveStart&&function SI(e,t,n){const{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){const s=ug(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}(n,i[n],t)}finally{null!==c&&Ke(c),zs(a),s.resolving=!1,Og()}}return o}function zg(e,t,n){return!!(n[t+(e>>Vg)]&1<<e)}function Gg(e,t){return!(e&$.Self||e&$.Host&&t)}class We{constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return Hg(this._tNode,this._lView,t,Os(r),n)}}function jI(){return new We(Pe(),v())}function Re(e){return un(()=>{const t=e.prototype.constructor,n=t[dn]||Qc(t),r=Object.prototype;let o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){const i=o[dn]||Qc(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function Qc(e){return hc(e)?()=>{const t=Qc(N(e));return t&&t()}:rr(e)}function qg(e){const t=e[b],n=t.type;return 2===n?t.declTNode:1===n?e[Fe]:null}const Hr="__parameters__";function zr(e,t,n){return un(()=>{const r=function Xc(e){return function(...n){if(e){const r=e(...n);for(const o in r)this[o]=r[o]}}}(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;const s=new o(...i);return a.annotation=s,a;function a(l,c,u){const d=l.hasOwnProperty(Hr)?l[Hr]:Object.defineProperty(l,Hr,{value:[]})[Hr];for(;d.length<=u;)d.push(null);return(d[u]=d[u]||[]).push(s),l}}return n&&(o.prototype=Object.create(n.prototype)),o.prototype.ngMetadataName=e,o.annotationCls=o,o})}function qr(e,t){e.forEach(n=>Array.isArray(n)?qr(n,t):t(n))}function Zg(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function Ys(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function ft(e,t,n){let r=Wr(e,t);return r>=0?e[1|r]=n:(r=~r,function WI(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(1===o)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;)e[o]=e[o-2],o--;e[t]=n,e[t+1]=r}}(e,r,t,n)),r}function Jc(e,t){const n=Wr(e,t);if(n>=0)return e[1|n]}function Wr(e,t){return function Yg(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){const i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}(e,t,1)}const Xs=Go(zr("Optional"),8),Js=Go(zr("SkipSelf"),4);function ra(e){return 128==(128&e.flags)}var jn=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(jn||{});const ru=new Map;let yS=0;const iu="__ngContext__";function Le(e,t){tt(t)?(e[iu]=t[Xo],function _S(e){ru.set(e[Xo],e)}(t)):e[iu]=t}let su;function au(e,t){return su(e,t)}function di(e){const t=e[ce];return ze(t)?t[ce]:t}function gm(e){return ym(e[Yo])}function mm(e){return ym(e[kt])}function ym(e){for(;null!==e&&!ze(e);)e=e[kt];return e}function Qr(e,t,n,r,o){if(null!=r){let i,s=!1;ze(r)?i=r:tt(r)&&(s=!0,r=r[pe]);const a=oe(r);0===e&&null!==n?null==o?Dm(t,n,a):lr(t,n,a,o||null,!0):1===e&&null!==n?lr(t,n,a,o||null,!0):2===e?function ua(e,t,n){const r=la(e,t);r&&function VS(e,t,n,r){e.removeChild(t,n,r)}(e,r,t,n)}(t,a,s):3===e&&t.destroyNode(a),null!=i&&function BS(e,t,n,r,o){const i=n[Zt];i!==oe(n)&&Qr(t,e,r,i,o);for(let a=Oe;a<n.length;a++){const l=n[a];hi(l[b],l,e,t,r,i)}}(t,e,i,n,o)}}function sa(e,t,n){return e.createElement(t,n)}function _m(e,t){const n=e[kr],r=n.indexOf(t);yg(t),n.splice(r,1)}function aa(e,t){if(e.length<=Oe)return;const n=Oe+t,r=e[n];if(r){const o=r[Qo];null!==o&&o!==e&&_m(o,r),t>0&&(e[n-1][kt]=r[kt]);const i=Ys(e,Oe+t);!function TS(e,t){hi(e,t,t[k],2,null,null),t[pe]=null,t[Fe]=null}(r[b],r);const s=i[Wt];null!==s&&s.detachView(i[b]),r[ce]=null,r[kt]=null,r[L]&=-129}return r}function cu(e,t){if(!(256&t[L])){const n=t[k];t[Jo]&&ng(t[Jo]),t[Ko]&&ng(t[Ko]),n.destroyNode&&hi(e,t,n,3,null,null),function PS(e){let t=e[Yo];if(!t)return uu(e[b],e);for(;t;){let n=null;if(tt(t))n=t[Yo];else{const r=t[Oe];r&&(n=r)}if(!n){for(;t&&!t[kt]&&t!==e;)tt(t)&&uu(t[b],t),t=t[ce];null===t&&(t=e),tt(t)&&uu(t[b],t),n=t&&t[kt]}t=n}}(t)}}function uu(e,t){if(!(256&t[L])){t[L]&=-129,t[L]|=256,function LS(e,t){let n;if(null!=e&&null!=(n=e.destroyHooks))for(let r=0;r<n.length;r+=2){const o=t[n[r]];if(!(o instanceof ri)){const i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){const a=o[i[s]],l=i[s+1];Yt(4,a,l);try{l.call(a)}finally{Yt(5,a,l)}}else{Yt(4,o,i);try{i.call(o)}finally{Yt(5,o,i)}}}}}(e,t),function FS(e,t){const n=e.cleanup,r=t[Or];if(null!==n)for(let i=0;i<n.length-1;i+=2)if("string"==typeof n[i]){const s=n[i+3];s>=0?r[s]():r[-s].unsubscribe(),i+=2}else n[i].call(r[n[i+1]]);null!==r&&(t[Or]=null);const o=t[kn];if(null!==o){t[kn]=null;for(let i=0;i<o.length;i++)(0,o[i])()}}(e,t),1===t[b].type&&t[k].destroy();const n=t[Qo];if(null!==n&&ze(t[ce])){n!==t[ce]&&_m(n,t);const r=t[Wt];null!==r&&r.detachView(e)}!function CS(e){ru.delete(e[Xo])}(t)}}function du(e,t,n){return function Cm(e,t,n){let r=t;for(;null!==r&&40&r.type;)r=(t=r).parent;if(null===r)return n[pe];{const{componentOffset:o}=r;if(o>-1){const{encapsulation:i}=e.data[r.directiveStart+o];if(i===Pt.None||i===Pt.Emulated)return null}return nt(r,n)}}(e,t.parent,n)}function lr(e,t,n,r,o){e.insertBefore(t,n,r,o)}function Dm(e,t,n){e.appendChild(t,n)}function wm(e,t,n,r,o){null!==r?lr(e,t,n,r,o):Dm(e,t,n)}function la(e,t){return e.parentNode(t)}let fu,da,mu,fa,Mm=function Em(e,t,n){return 40&e.type?nt(e,n):null};function ca(e,t,n,r){const o=du(e,r,t),i=t[k],a=function bm(e,t,n){return Mm(e,t,n)}(r.parent||t[Fe],r,t);if(null!=o)if(Array.isArray(n))for(let l=0;l<n.length;l++)wm(i,o,n[l],a,!1);else wm(i,o,n,a,!1);void 0!==fu&&fu(i,r,t,n,o)}function fi(e,t){if(null!==t){const n=t.type;if(3&n)return nt(t,e);if(4&n)return hu(-1,e[t.index]);if(8&n){const r=t.child;if(null!==r)return fi(e,r);{const o=e[t.index];return ze(o)?hu(-1,o):oe(o)}}if(32&n)return au(t,e)()||oe(e[t.index]);{const r=Sm(e,t);return null!==r?Array.isArray(r)?r[0]:fi(di(e[_e]),r):fi(e,t.next)}}return null}function Sm(e,t){return null!==t?e[_e][Fe].projection[t.projection]:null}function hu(e,t){const n=Oe+e+1;if(n<t.length){const r=t[n],o=r[b].firstChild;if(null!==o)return fi(r,o)}return t[Zt]}function pu(e,t,n,r,o,i,s){for(;null!=n;){const a=r[n.index],l=n.type;if(s&&0===t&&(a&&Le(oe(a),r),n.flags|=2),32!=(32&n.flags))if(8&l)pu(e,t,n.child,r,o,i,!1),Qr(t,e,o,a,i);else if(32&l){const c=au(n,r);let u;for(;u=c();)Qr(t,e,o,u,i);Qr(t,e,o,a,i)}else 16&l?Am(e,t,r,n,o,i):Qr(t,e,o,a,i);n=s?n.projectionNext:n.next}}function hi(e,t,n,r,o,i){pu(n,r,e.firstChild,t,o,i,!1)}function Am(e,t,n,r,o,i){const s=n[_e],l=s[Fe].projection[r.projection];if(Array.isArray(l))for(let c=0;c<l.length;c++)Qr(t,e,o,l[c],i);else{let c=l;const u=s[ce];ra(r)&&(c.flags|=128),pu(e,t,c,u,o,i,!0)}}function Tm(e,t,n){""===n?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function Om(e,t,n){const{mergedAttrs:r,classes:o,styles:i}=n;null!==r&&Ic(e,t,r),null!==o&&Tm(e,t,o),null!==i&&function US(e,t,n){e.setAttribute(t,"style",n)}(e,t,i)}function Xr(e){return function gu(){if(void 0===da&&(da=null,re.trustedTypes))try{da=re.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return da}()?.createHTML(e)||e}function Jr(){if(void 0!==mu)return mu;if(typeof document<"u")return document;throw new C(210,!1)}function Nm(e){return function yu(){if(void 0===fa&&(fa=null,re.trustedTypes))try{fa=re.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return fa}()?.createHTML(e)||e}class km{constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Ep})`}}function $n(e){return e instanceof km?e.changingThisBreaksApplicationSecurity:e}function pi(e,t){const n=function KS(e){return e instanceof km&&e.getTypeName()||null}(e);if(null!=n&&n!==t){if("ResourceURL"===n&&"URL"===t)return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${Ep})`)}return n===t}class ex{constructor(t){this.inertDocumentHelper=t}getInertBodyElement(t){t="<body><remove></remove>"+t;try{const n=(new window.DOMParser).parseFromString(Xr(t),"text/html").body;return null===n?this.inertDocumentHelper.getInertBodyElement(t):(n.removeChild(n.firstChild),n)}catch{return null}}}class tx{constructor(t){this.defaultDoc=t,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(t){const n=this.inertDocument.createElement("template");return n.innerHTML=Xr(t),n}}const rx=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function vu(e){return(e=String(e)).match(rx)?e:"unsafe:"+e}function gn(e){const t={};for(const n of e.split(","))t[n]=!0;return t}function gi(...e){const t={};for(const n of e)for(const r in n)n.hasOwnProperty(r)&&(t[r]=!0);return t}const Lm=gn("area,br,col,hr,img,wbr"),Vm=gn("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),jm=gn("rp,rt"),_u=gi(Lm,gi(Vm,gn("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),gi(jm,gn("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),gi(jm,Vm)),Cu=gn("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),$m=gi(Cu,gn("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),gn("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext")),ox=gn("script,style,template");class ix{constructor(){this.sanitizedSomething=!1,this.buf=[]}sanitizeChildren(t){let n=t.firstChild,r=!0;for(;n;)if(n.nodeType===Node.ELEMENT_NODE?r=this.startElement(n):n.nodeType===Node.TEXT_NODE?this.chars(n.nodeValue):this.sanitizedSomething=!0,r&&n.firstChild)n=n.firstChild;else for(;n;){n.nodeType===Node.ELEMENT_NODE&&this.endElement(n);let o=this.checkClobberedElement(n,n.nextSibling);if(o){n=o;break}n=this.checkClobberedElement(n,n.parentNode)}return this.buf.join("")}startElement(t){const n=t.nodeName.toLowerCase();if(!_u.hasOwnProperty(n))return this.sanitizedSomething=!0,!ox.hasOwnProperty(n);this.buf.push("<"),this.buf.push(n);const r=t.attributes;for(let o=0;o<r.length;o++){const i=r.item(o),s=i.name,a=s.toLowerCase();if(!$m.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let l=i.value;Cu[a]&&(l=vu(l)),this.buf.push(" ",s,'="',Bm(l),'"')}return this.buf.push(">"),!0}endElement(t){const n=t.nodeName.toLowerCase();_u.hasOwnProperty(n)&&!Lm.hasOwnProperty(n)&&(this.buf.push("</"),this.buf.push(n),this.buf.push(">"))}chars(t){this.buf.push(Bm(t))}checkClobberedElement(t,n){if(n&&(t.compareDocumentPosition(n)&Node.DOCUMENT_POSITION_CONTAINED_BY)===Node.DOCUMENT_POSITION_CONTAINED_BY)throw new Error(`Failed to sanitize html because the element is clobbered: ${t.outerHTML}`);return n}}const sx=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,ax=/([^\#-~ |!])/g;function Bm(e){return e.replace(/&/g,"&amp;").replace(sx,function(t){return"&#"+(1024*(t.charCodeAt(0)-55296)+(t.charCodeAt(1)-56320)+65536)+";"}).replace(ax,function(t){return"&#"+t.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}let ha;function Du(e){return"content"in e&&function cx(e){return e.nodeType===Node.ELEMENT_NODE&&"TEMPLATE"===e.nodeName}(e)?e.content:null}var Kr=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(Kr||{});function Hm(e){const t=mi();return t?Nm(t.sanitize(Kr.HTML,e)||""):pi(e,"HTML")?Nm($n(e)):function lx(e,t){let n=null;try{ha=ha||function Fm(e){const t=new tx(e);return function nx(){try{return!!(new window.DOMParser).parseFromString(Xr(""),"text/html")}catch{return!1}}()?new ex(t):t}(e);let r=t?String(t):"";n=ha.getInertBodyElement(r);let o=5,i=r;do{if(0===o)throw new Error("Failed to sanitize html because the input is unstable");o--,r=i,i=n.innerHTML,n=ha.getInertBodyElement(r)}while(r!==i);return Xr((new ix).sanitizeChildren(Du(n)||n))}finally{if(n){const r=Du(n)||n;for(;r.firstChild;)r.removeChild(r.firstChild)}}}(Jr(),R(e))}function wu(e){const t=mi();return t?t.sanitize(Kr.URL,e)||"":pi(e,"URL")?$n(e):vu(R(e))}function mi(){const e=v();return e&&e[Nr].sanitizer}const yi=new M("ENVIRONMENT_INITIALIZER"),Gm=new M("INJECTOR",-1),qm=new M("INJECTOR_DEF_TYPES");class bu{get(t,n=zo){if(n===zo){const r=new Error(`NullInjectorError: No provider for ${Ie(t)}!`);throw r.name="NullInjectorError",r}return n}}function gx(...e){return{\u0275providers:Wm(0,e),\u0275fromNgModule:!0}}function Wm(e,...t){const n=[],r=new Set;let o;const i=s=>{n.push(s)};return qr(t,s=>{const a=s;pa(a,i,[],r)&&(o||=[],o.push(a))}),void 0!==o&&Zm(o,i),n}function Zm(e,t){for(let n=0;n<e.length;n++){const{ngModule:r,providers:o}=e[n];Mu(o,i=>{t(i,r)})}}function pa(e,t,n,r){if(!(e=N(e)))return!1;let o=null,i=xs(e);const s=!i&&H(e);if(i||s){if(s&&!s.standalone)return!1;o=e}else{const l=e.ngModule;if(i=xs(l),!i)return!1;o=l}const a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){const l="function"==typeof s.dependencies?s.dependencies():s.dependencies;for(const c of l)pa(c,t,n,r)}}else{if(!i)return!1;{if(null!=i.imports&&!a){let c;r.add(o);try{qr(i.imports,u=>{pa(u,t,n,r)&&(c||=[],c.push(u))})}finally{}void 0!==c&&Zm(c,t)}if(!a){const c=rr(o)||(()=>new o);t({provide:o,useFactory:c,deps:G},o),t({provide:qm,useValue:o,multi:!0},o),t({provide:yi,useValue:()=>I(o),multi:!0},o)}const l=i.providers;if(null!=l&&!a){const c=e;Mu(l,u=>{t(u,c)})}}}return o!==e&&void 0!==e.providers}function Mu(e,t){for(let n of e)pc(n)&&(n=n.\u0275providers),Array.isArray(n)?Mu(n,t):t(n)}const mx=X({provide:String,useValue:X});function Iu(e){return null!==e&&"object"==typeof e&&mx in e}function cr(e){return"function"==typeof e}const Su=new M("Set Injector scope."),ga={},vx={};let xu;function ma(){return void 0===xu&&(xu=new bu),xu}class ht{}class eo extends ht{get destroyed(){return this._destroyed}constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,this.records=new Map,this._ngOnDestroyHooks=new Set,this._onDestroyHooks=[],this._destroyed=!1,Tu(t,s=>this.processProvider(s)),this.records.set(Gm,to(void 0,this)),o.has("environment")&&this.records.set(ht,to(void 0,this));const i=this.records.get(Su);null!=i&&"string"==typeof i.value&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(qm.multi,G,$.Self))}destroy(){this.assertNotDestroyed(),this._destroyed=!0;try{for(const n of this._ngOnDestroyHooks)n.ngOnDestroy();const t=this._onDestroyHooks;this._onDestroyHooks=[];for(const n of t)n()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear()}}onDestroy(t){return this.assertNotDestroyed(),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){this.assertNotDestroyed();const n=Pn(this),r=Ke(void 0);try{return t()}finally{Pn(n),Ke(r)}}get(t,n=zo,r=$.Default){if(this.assertNotDestroyed(),t.hasOwnProperty(Pp))return t[Pp](this);r=Os(r);const i=Pn(this),s=Ke(void 0);try{if(!(r&$.SkipSelf)){let l=this.records.get(t);if(void 0===l){const c=function bx(e){return"function"==typeof e||"object"==typeof e&&e instanceof M}(t)&&Ss(t);l=c&&this.injectableDefInScope(c)?to(Au(t),ga):null,this.records.set(t,l)}if(null!=l)return this.hydrate(t,l)}return(r&$.Self?ma():this.parent).get(t,n=r&$.Optional&&n===zo?null:n)}catch(a){if("NullInjectorError"===a.name){if((a[Ts]=a[Ts]||[]).unshift(Ie(t)),i)throw a;return function CM(e,t,n,r){const o=e[Ts];throw t[Tp]&&o.unshift(t[Tp]),e.message=function DM(e,t,n,r=null){e=e&&"\n"===e.charAt(0)&&"\u0275"==e.charAt(1)?e.slice(2):e;let o=Ie(t);if(Array.isArray(t))o=t.map(Ie).join(" -> ");else if("object"==typeof t){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+("string"==typeof a?JSON.stringify(a):Ie(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(gM,"\n  ")}`}("\n"+e.message,o,n,r),e.ngTokenPath=o,e[Ts]=null,e}(a,t,"R3InjectorError",this.source)}throw a}finally{Ke(s),Pn(i)}}resolveInjectorInitializers(){const t=Pn(this),n=Ke(void 0);try{const o=this.get(yi.multi,G,$.Self);for(const i of o)i()}finally{Pn(t),Ke(n)}}toString(){const t=[],n=this.records;for(const r of n.keys())t.push(Ie(r));return`R3Injector[${t.join(", ")}]`}assertNotDestroyed(){if(this._destroyed)throw new C(205,!1)}processProvider(t){let n=cr(t=N(t))?t:N(t&&t.provide);const r=function Cx(e){return Iu(e)?to(void 0,e.useValue):to(Xm(e),ga)}(t);if(cr(t)||!0!==t.multi)this.records.get(n);else{let o=this.records.get(n);o||(o=to(void 0,ga,!0),o.factory=()=>bc(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n){return n.value===ga&&(n.value=vx,n.value=n.factory()),"object"==typeof n.value&&n.value&&function wx(e){return null!==e&&"object"==typeof e&&"function"==typeof e.ngOnDestroy}(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}injectableDefInScope(t){if(!t.providedIn)return!1;const n=N(t.providedIn);return"string"==typeof n?"any"===n||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){const n=this._onDestroyHooks.indexOf(t);-1!==n&&this._onDestroyHooks.splice(n,1)}}function Au(e){const t=Ss(e),n=null!==t?t.factory:rr(e);if(null!==n)return n;if(e instanceof M)throw new C(204,!1);if(e instanceof Function)return function _x(e){const t=e.length;if(t>0)throw function li(e,t){const n=[];for(let r=0;r<e;r++)n.push(t);return n}(t,"?"),new C(204,!1);const n=function dM(e){return e&&(e[As]||e[Ip])||null}(e);return null!==n?()=>n.factory(e):()=>new e}(e);throw new C(204,!1)}function Xm(e,t,n){let r;if(cr(e)){const o=N(e);return rr(o)||Au(o)}if(Iu(e))r=()=>N(e.useValue);else if(function Qm(e){return!(!e||!e.useFactory)}(e))r=()=>e.useFactory(...bc(e.deps||[]));else if(function Ym(e){return!(!e||!e.useExisting)}(e))r=()=>I(N(e.useExisting));else{const o=N(e&&(e.useClass||e.provide));if(!function Dx(e){return!!e.deps}(e))return rr(o)||Au(o);r=()=>new o(...bc(e.deps))}return r}function to(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function Tu(e,t){for(const n of e)Array.isArray(n)?Tu(n,t):n&&pc(n)?Tu(n.\u0275providers,t):t(n)}const ya=new M("AppId",{providedIn:"root",factory:()=>Ex}),Ex="ng",Jm=new M("Platform Initializer"),ur=new M("Platform ID",{providedIn:"platform",factory:()=>"unknown"}),Km=new M("CSP nonce",{providedIn:"root",factory:()=>Jr().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});let ey=(e,t,n)=>null;function Vu(e,t,n=!1){return ey(e,t,n)}class Rx{}class ry{}class Fx{resolveComponentFactory(t){throw function kx(e){const t=Error(`No component factory found for ${Ie(e)}.`);return t.ngComponent=e,t}(t)}}let ba=(()=>{class e{static#e=this.NULL=new Fx}return e})();function Lx(){return oo(Pe(),v())}function oo(e,t){return new pt(nt(e,t))}let pt=(()=>{class e{constructor(n){this.nativeElement=n}static#e=this.__NG_ELEMENT_ID__=Lx}return e})();function Vx(e){return e instanceof pt?e.nativeElement:e}class iy{}let mn=(()=>{class e{constructor(){this.destroyNode=null}static#e=this.__NG_ELEMENT_ID__=()=>function jx(){const e=v(),n=dt(Pe().index,e);return(tt(n)?n:e)[k]}()}return e})(),$x=(()=>{class e{static#e=this.\u0275prov=x({token:e,providedIn:"root",factory:()=>null})}return e})();class Ci{constructor(t){this.full=t,this.major=t.split(".")[0],this.minor=t.split(".")[1],this.patch=t.split(".").slice(2).join(".")}}const Bx=new Ci("16.2.12"),Bu={};function cy(e,t=null,n=null,r){const o=uy(e,t,n,r);return o.resolveInjectorInitializers(),o}function uy(e,t=null,n=null,r,o=new Set){const i=[n||G,gx(e)];return r=r||("object"==typeof e?void 0:Ie(e)),new eo(i,t||ma(),r||null,o)}let gt=(()=>{class e{static#e=this.THROW_IF_NOT_FOUND=zo;static#t=this.NULL=new bu;static create(n,r){if(Array.isArray(n))return cy({name:""},r,n,"");{const o=n.name??"";return cy({name:o},n.parent,n.providers,o)}}static#n=this.\u0275prov=x({token:e,providedIn:"any",factory:()=>I(Gm)});static#r=this.__NG_ELEMENT_ID__=-1}return e})();function Uu(e){return e.ngOriginalError}class yn{constructor(){this._console=console}handleError(t){const n=this._findOriginalError(t);this._console.error("ERROR",t),n&&this._console.error("ORIGINAL ERROR",n)}_findOriginalError(t){let n=t&&Uu(t);for(;n&&Uu(n);)n=Uu(n);return n||null}}function Gu(e){return t=>{setTimeout(e,void 0,t)}}const ge=class Zx extends Ot{constructor(t=!1){super(),this.__isAsync=t}emit(t){super.next(t)}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&"object"==typeof t){const l=t;o=l.next?.bind(l),i=l.error?.bind(l),s=l.complete?.bind(l)}this.__isAsync&&(i=Gu(i),o&&(o=Gu(o)),s&&(s=Gu(s)));const a=super.subscribe({next:o,error:i,complete:s});return t instanceof st&&t.add(a),a}};function fy(...e){}class ie{constructor({enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:n=!1,shouldCoalesceRunChangeDetection:r=!1}){if(this.hasPendingMacrotasks=!1,this.hasPendingMicrotasks=!1,this.isStable=!0,this.onUnstable=new ge(!1),this.onMicrotaskEmpty=new ge(!1),this.onStable=new ge(!1),this.onError=new ge(!1),typeof Zone>"u")throw new C(908,!1);Zone.assertZonePatched();const o=this;o._nesting=0,o._outer=o._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(o._inner=o._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(o._inner=o._inner.fork(Zone.longStackTraceZoneSpec)),o.shouldCoalesceEventChangeDetection=!r&&n,o.shouldCoalesceRunChangeDetection=r,o.lastRequestAnimationFrameId=-1,o.nativeRequestAnimationFrame=function Yx(){const e="function"==typeof re.requestAnimationFrame;let t=re[e?"requestAnimationFrame":"setTimeout"],n=re[e?"cancelAnimationFrame":"clearTimeout"];if(typeof Zone<"u"&&t&&n){const r=t[Zone.__symbol__("OriginalDelegate")];r&&(t=r);const o=n[Zone.__symbol__("OriginalDelegate")];o&&(n=o)}return{nativeRequestAnimationFrame:t,nativeCancelAnimationFrame:n}}().nativeRequestAnimationFrame,function Jx(e){const t=()=>{!function Xx(e){e.isCheckStableRunning||-1!==e.lastRequestAnimationFrameId||(e.lastRequestAnimationFrameId=e.nativeRequestAnimationFrame.call(re,()=>{e.fakeTopEventTask||(e.fakeTopEventTask=Zone.root.scheduleEventTask("fakeTopEventTask",()=>{e.lastRequestAnimationFrameId=-1,Wu(e),e.isCheckStableRunning=!0,qu(e),e.isCheckStableRunning=!1},void 0,()=>{},()=>{})),e.fakeTopEventTask.invoke()}),Wu(e))}(e)};e._inner=e._inner.fork({name:"angular",properties:{isAngularZone:!0},onInvokeTask:(n,r,o,i,s,a)=>{if(function eA(e){return!(!Array.isArray(e)||1!==e.length)&&!0===e[0].data?.__ignore_ng_zone__}(a))return n.invokeTask(o,i,s,a);try{return hy(e),n.invokeTask(o,i,s,a)}finally{(e.shouldCoalesceEventChangeDetection&&"eventTask"===i.type||e.shouldCoalesceRunChangeDetection)&&t(),py(e)}},onInvoke:(n,r,o,i,s,a,l)=>{try{return hy(e),n.invoke(o,i,s,a,l)}finally{e.shouldCoalesceRunChangeDetection&&t(),py(e)}},onHasTask:(n,r,o,i)=>{n.hasTask(o,i),r===o&&("microTask"==i.change?(e._hasPendingMicrotasks=i.microTask,Wu(e),qu(e)):"macroTask"==i.change&&(e.hasPendingMacrotasks=i.macroTask))},onHandleError:(n,r,o,i)=>(n.handleError(o,i),e.runOutsideAngular(()=>e.onError.emit(i)),!1)})}(o)}static isInAngularZone(){return typeof Zone<"u"&&!0===Zone.current.get("isAngularZone")}static assertInAngularZone(){if(!ie.isInAngularZone())throw new C(909,!1)}static assertNotInAngularZone(){if(ie.isInAngularZone())throw new C(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){const i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,Qx,fy,fy);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}}const Qx={};function qu(e){if(0==e._nesting&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function Wu(e){e.hasPendingMicrotasks=!!(e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&-1!==e.lastRequestAnimationFrameId)}function hy(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function py(e){e._nesting--,qu(e)}class Kx{constructor(){this.hasPendingMicrotasks=!1,this.hasPendingMacrotasks=!1,this.isStable=!0,this.onUnstable=new ge,this.onMicrotaskEmpty=new ge,this.onStable=new ge,this.onError=new ge}run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}}const gy=new M("",{providedIn:"root",factory:my});function my(){const e=E(ie);let t=!0;return function tM(...e){const t=Uo(e),n=function Z0(e,t){return"number"==typeof uc(e)?e.pop():t}(e,1/0),r=e;return r.length?1===r.length?at(r[0]):Sr(n)(Me(r,t)):Gt}(new he(o=>{t=e.isStable&&!e.hasPendingMacrotasks&&!e.hasPendingMicrotasks,e.runOutsideAngular(()=>{o.next(t),o.complete()})}),new he(o=>{let i;e.runOutsideAngular(()=>{i=e.onStable.subscribe(()=>{ie.assertNotInAngularZone(),queueMicrotask(()=>{!t&&!e.hasPendingMacrotasks&&!e.hasPendingMicrotasks&&(t=!0,o.next(!0))})})});const s=e.onUnstable.subscribe(()=>{ie.assertInAngularZone(),t&&(t=!1,e.runOutsideAngular(()=>{o.next(!1)}))});return()=>{i.unsubscribe(),s.unsubscribe()}}).pipe(bp()))}function vn(e){return e instanceof Function?e():e}let Zu=(()=>{class e{constructor(){this.renderDepth=0,this.handler=null}begin(){this.handler?.validateBegin(),this.renderDepth++}end(){this.renderDepth--,0===this.renderDepth&&this.handler?.execute()}ngOnDestroy(){this.handler?.destroy(),this.handler=null}static#e=this.\u0275prov=x({token:e,providedIn:"root",factory:()=>new e})}return e})();function Di(e){for(;e;){e[L]|=64;const t=di(e);if(Ac(e)&&!t)return e;e=t}return null}const Dy=new M("",{providedIn:"root",factory:()=>!1});let Ma=null;function My(e,t){return e[t]??xy()}function Iy(e,t){const n=xy();n.producerNode?.length&&(e[t]=Ma,n.lView=e,Ma=Sy())}const uA={...Qp,consumerIsAlwaysLive:!0,consumerMarkedDirty:e=>{Di(e.lView)},lView:null};function Sy(){return Object.create(uA)}function xy(){return Ma??=Sy(),Ma}const F={};function we(e){Ay(U(),v(),qe()+e,!1)}function Ay(e,t,n,r){if(!r)if(3==(3&t[L])){const i=e.preOrderCheckHooks;null!==i&&Hs(t,i,n)}else{const i=e.preOrderHooks;null!==i&&Us(t,i,0,n)}sr(n)}function D(e,t=$.Default){const n=v();return null===n?I(e,t):Hg(Pe(),n,N(e),t)}function Ia(e,t,n,r,o,i,s,a,l,c,u){const d=t.blueprint.slice();return d[pe]=o,d[L]=140|r,(null!==c||e&&2048&e[L])&&(d[L]|=2048),mg(d),d[ce]=d[Pr]=e,d[ve]=n,d[Nr]=s||e&&e[Nr],d[k]=a||e&&e[k],d[Rn]=l||e&&e[Rn]||null,d[Fe]=i,d[Xo]=function vS(){return yS++}(),d[fn]=u,d[qp]=c,d[_e]=2==t.type?e[_e]:d,d}function ao(e,t,n,r,o){let i=e.data[t];if(null===i)i=function Yu(e,t,n,r,o){const i=wg(),s=Lc(),l=e.data[t]=function vA(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return function Vr(){return null!==O.skipHydrationRootTNode}()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}(0,s?i:i&&i.parent,n,t,r,o);return null===e.firstChild&&(e.firstChild=l),null!==i&&(s?null==i.child&&null!==l.parent&&(i.child=l):null===i.next&&(i.next=l,l.prev=i)),l}(e,t,n,r,o),function vI(){return O.lFrame.inI18n}()&&(i.flags|=32);else if(64&i.type){i.type=n,i.value=r,i.attrs=o;const s=function ni(){const e=O.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}();i.injectorIndex=null===s?-1:s.injectorIndex}return Qt(i,!0),i}function wi(e,t,n,r){if(0===n)return-1;const o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function Oy(e,t,n,r,o){const i=My(t,Jo),s=qe(),a=2&r;try{sr(-1),a&&t.length>B&&Ay(e,t,B,!1),Yt(a?2:0,o);const c=a?i:null,u=Oc(c);try{null!==c&&(c.dirty=!1),n(r,o)}finally{Nc(c,u)}}finally{a&&null===t[Jo]&&Iy(t,Jo),sr(s),Yt(a?3:1,o)}}function Qu(e,t,n){if(xc(t)){const r=bt(null);try{const i=t.directiveEnd;for(let s=t.directiveStart;s<i;s++){const a=e.data[s];a.contentQueries&&a.contentQueries(1,n[s],s)}}finally{bt(r)}}}function Xu(e,t,n){Dg()&&(function MA(e,t,n,r){const o=n.directiveStart,i=n.directiveEnd;nr(n)&&function NA(e,t,n){const r=nt(t,e),o=Ny(n);let s=16;n.signals?s=4096:n.onPush&&(s=64);const a=Sa(e,Ia(e,o,null,s,r,t,null,e[Nr].rendererFactory.createRenderer(r,n),null,null,null));e[t.index]=a}(t,n,e.data[o+n.componentOffset]),e.firstCreatePass||Gs(n,t),Le(r,t);const s=n.initialInputs;for(let a=o;a<i;a++){const l=e.data[a],c=ar(t,e,a,n);Le(c,t),null!==s&&PA(0,a-o,c,l,0,s),Ft(l)&&(dt(n.index,t)[ve]=ar(t,e,a,n))}}(e,t,n,nt(n,t)),64==(64&n.flags)&&Ly(e,t,n))}function Ju(e,t,n=nt){const r=t.localNames;if(null!==r){let o=t.index+1;for(let i=0;i<r.length;i+=2){const s=r[i+1],a=-1===s?n(t,e):e[s];e[o++]=a}}}function Ny(e){const t=e.tView;return null===t||t.incompleteFirstPass?e.tView=Ku(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function Ku(e,t,n,r,o,i,s,a,l,c,u){const d=B+r,f=d+o,h=function fA(e,t){const n=[];for(let r=0;r<t;r++)n.push(r<e?null:F);return n}(d,f),p="function"==typeof c?c():c;return h[b]={type:e,blueprint:h,template:n,queries:null,viewQuery:a,declTNode:t,data:h.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:f,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:"function"==typeof i?i():i,pipeRegistry:"function"==typeof s?s():s,firstChild:null,schemas:l,consts:p,incompleteFirstPass:!1,ssrId:u}}let Py=e=>null;function Ry(e,t,n,r){for(let o in e)if(e.hasOwnProperty(o)){n=null===n?{}:n;const i=e[o];null===r?ky(n,t,o,i):r.hasOwnProperty(o)&&ky(n,t,r[o],i)}return n}function ky(e,t,n,r){e.hasOwnProperty(n)?e[n].push(t,r):e[n]=[t,r]}function ed(e,t,n,r){if(Dg()){const o=null===r?null:{"":-1},i=function SA(e,t){const n=e.directiveRegistry;let r=null,o=null;if(n)for(let i=0;i<n.length;i++){const s=n[i];if($p(t,s.selectors,!1))if(r||(r=[]),Ft(s))if(null!==s.findHostDirectiveDefs){const a=[];o=o||new Map,s.findHostDirectiveDefs(s,a,o),r.unshift(...a,s),td(e,t,a.length)}else r.unshift(s),td(e,t,0);else o=o||new Map,s.findHostDirectiveDefs?.(s,r,o),r.push(s)}return null===r?null:[r,o]}(e,n);let s,a;null===i?s=a=null:[s,a]=i,null!==s&&Fy(e,t,n,s,o,a),o&&function xA(e,t,n){if(t){const r=e.localNames=[];for(let o=0;o<t.length;o+=2){const i=n[t[o+1]];if(null==i)throw new C(-301,!1);r.push(t[o],i)}}}(n,r,o)}n.mergedAttrs=Wo(n.mergedAttrs,n.attrs)}function Fy(e,t,n,r,o,i){for(let c=0;c<r.length;c++)Yc(Gs(n,t),e,r[c].type);!function TA(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}(n,e.data.length,r.length);for(let c=0;c<r.length;c++){const u=r[c];u.providersResolver&&u.providersResolver(u)}let s=!1,a=!1,l=wi(e,t,r.length,null);for(let c=0;c<r.length;c++){const u=r[c];n.mergedAttrs=Wo(n.mergedAttrs,u.hostAttrs),OA(e,n,t,l,u),AA(l,u,o),null!==u.contentQueries&&(n.flags|=4),(null!==u.hostBindings||null!==u.hostAttrs||0!==u.hostVars)&&(n.flags|=64);const d=u.type.prototype;!s&&(d.ngOnChanges||d.ngOnInit||d.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),s=!0),!a&&(d.ngOnChanges||d.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),a=!0),l++}!function _A(e,t,n){const o=t.directiveEnd,i=e.data,s=t.attrs,a=[];let l=null,c=null;for(let u=t.directiveStart;u<o;u++){const d=i[u],f=n?n.get(d):null,p=f?f.outputs:null;l=Ry(d.inputs,u,l,f?f.inputs:null),c=Ry(d.outputs,u,c,p);const g=null===l||null===s||jp(t)?null:RA(l,u,s);a.push(g)}null!==l&&(l.hasOwnProperty("class")&&(t.flags|=8),l.hasOwnProperty("style")&&(t.flags|=16)),t.initialInputs=a,t.inputs=l,t.outputs=c}(e,n,i)}function Ly(e,t,n){const r=n.directiveStart,o=n.directiveEnd,i=n.index,s=function CI(){return O.lFrame.currentDirectiveIndex}();try{sr(i);for(let a=r;a<o;a++){const l=e.data[a],c=t[a];jc(a),(null!==l.hostBindings||0!==l.hostVars||null!==l.hostAttrs)&&IA(l,c)}}finally{sr(-1),jc(s)}}function IA(e,t){null!==e.hostBindings&&e.hostBindings(1,t)}function td(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function AA(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;Ft(t)&&(n[""]=e)}}function OA(e,t,n,r,o){e.data[r]=o;const i=o.factory||(o.factory=rr(o.type)),s=new ri(i,Ft(o),D);e.blueprint[r]=s,n[r]=s,function bA(e,t,n,r,o){const i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;null===s&&(s=e.hostBindingOpCodes=[]);const a=~t.index;(function EA(e){let t=e.length;for(;t>0;){const n=e[--t];if("number"==typeof n&&n<0)return n}return 0})(s)!=a&&s.push(a),s.push(n,r,i)}}(e,t,r,wi(e,n,o.hostVars,F),o)}function PA(e,t,n,r,o,i){const s=i[t];if(null!==s)for(let a=0;a<s.length;)Vy(r,n,s[a++],s[a++],s[a++])}function Vy(e,t,n,r,o){const i=bt(null);try{const s=e.inputTransforms;null!==s&&s.hasOwnProperty(r)&&(o=s[r].call(t,o)),null!==e.setInput?e.setInput(t,o,n,r):t[r]=o}finally{bt(i)}}function RA(e,t,n){let r=null,o=0;for(;o<n.length;){const i=n[o];if(0!==i)if(5!==i){if("number"==typeof i)break;if(e.hasOwnProperty(i)){null===r&&(r=[]);const s=e[i];for(let a=0;a<s.length;a+=2)if(s[a]===t){r.push(i,s[a+1],n[o+1]);break}}o+=2}else o+=2;else o+=4}return r}function jy(e,t,n,r){return[e,!0,!1,t,null,0,r,n,null,null,null]}function $y(e,t){const n=e.contentQueries;if(null!==n)for(let r=0;r<n.length;r+=2){const i=n[r+1];if(-1!==i){const s=e.data[i];Bc(n[r]),s.contentQueries(2,t[i],i)}}}function Sa(e,t){return e[Yo]?e[Gp][kt]=t:e[Yo]=t,e[Gp]=t,t}function rd(e,t,n){Bc(0);const r=bt(null);try{t(e,n)}finally{bt(r)}}function By(e){return e[Or]||(e[Or]=[])}function Hy(e){return e.cleanup||(e.cleanup=[])}function zy(e,t){const n=e[Rn],r=n?n.get(yn,null):null;r&&r.handleError(t)}function od(e,t,n,r,o){for(let i=0;i<n.length;){const s=n[i++],a=n[i++];Vy(e.data[s],t[s],r,a,o)}}function kA(e,t){const n=dt(t,e),r=n[b];!function FA(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}(r,n);const o=n[pe];null!==o&&null===n[fn]&&(n[fn]=Vu(o,n[Rn])),id(r,n,n[ve])}function id(e,t,n){Hc(t);try{const r=e.viewQuery;null!==r&&rd(1,r,n);const o=e.template;null!==o&&Oy(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),e.staticContentQueries&&$y(e,t),e.staticViewQueries&&rd(2,e.viewQuery,n);const i=e.components;null!==i&&function LA(e,t){for(let n=0;n<t.length;n++)kA(e,t[n])}(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[L]&=-5,Uc()}}let Gy=(()=>{class e{constructor(){this.all=new Set,this.queue=new Map}create(n,r,o){const i=typeof Zone>"u"?null:Zone.current,s=function QM(e,t,n){const r=Object.create(XM);n&&(r.consumerAllowSignalWrites=!0),r.fn=e,r.schedule=t;const o=s=>{r.cleanupFn=s};return r.ref={notify:()=>eg(r),run:()=>{if(r.dirty=!1,r.hasRun&&!tg(r))return;r.hasRun=!0;const s=Oc(r);try{r.cleanupFn(),r.cleanupFn=cg,r.fn(o)}finally{Nc(r,s)}},cleanup:()=>r.cleanupFn()},r.ref}(n,c=>{this.all.has(c)&&this.queue.set(c,i)},o);let a;this.all.add(s),s.notify();const l=()=>{s.cleanup(),a?.(),this.all.delete(s),this.queue.delete(s)};return a=r?.onDestroy(l),{destroy:l}}flush(){if(0!==this.queue.size)for(const[n,r]of this.queue)this.queue.delete(n),r?r.run(()=>n.run()):n.run()}get isQueueEmpty(){return 0===this.queue.size}static#e=this.\u0275prov=x({token:e,providedIn:"root",factory:()=>new e})}return e})();function xa(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(null!==t)for(let s=0;s<t.length;s++){const a=t[s];"number"==typeof a?i=a:1==i?o=fc(o,a):2==i&&(r=fc(r,a+": "+t[++s]+";"))}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}function bi(e,t,n,r,o=!1){for(;null!==n;){const i=t[n.index];null!==i&&r.push(oe(i)),ze(i)&&qy(i,r);const s=n.type;if(8&s)bi(e,t,n.child,r);else if(32&s){const a=au(n,t);let l;for(;l=a();)r.push(l)}else if(16&s){const a=Sm(t,n);if(Array.isArray(a))r.push(...a);else{const l=di(t[_e]);bi(l[b],l,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function qy(e,t){for(let n=Oe;n<e.length;n++){const r=e[n],o=r[b].firstChild;null!==o&&bi(r[b],r,o,t)}e[Zt]!==e[pe]&&t.push(e[Zt])}function Aa(e,t,n,r=!0){const o=t[Nr],i=o.rendererFactory,s=o.afterRenderEventManager;i.begin?.(),s?.begin();try{Wy(e,t,e.template,n)}catch(l){throw r&&zy(t,l),l}finally{i.end?.(),o.effectManager?.flush(),s?.end()}}function Wy(e,t,n,r){const o=t[L];if(256!=(256&o)){t[Nr].effectManager?.flush(),Hc(t);try{mg(t),function Eg(e){return O.lFrame.bindingIndex=e}(e.bindingStartIndex),null!==n&&Oy(e,t,n,2,r);const s=3==(3&o);if(s){const c=e.preOrderCheckHooks;null!==c&&Hs(t,c,null)}else{const c=e.preOrderHooks;null!==c&&Us(t,c,0,null),zc(t,0)}if(function $A(e){for(let t=gm(e);null!==t;t=mm(t)){if(!t[Wp])continue;const n=t[kr];for(let r=0;r<n.length;r++){aI(n[r])}}}(t),Zy(t,2),null!==e.contentQueries&&$y(e,t),s){const c=e.contentCheckHooks;null!==c&&Hs(t,c)}else{const c=e.contentHooks;null!==c&&Us(t,c,1),zc(t,1)}!function dA(e,t){const n=e.hostBindingOpCodes;if(null===n)return;const r=My(t,Ko);try{for(let o=0;o<n.length;o++){const i=n[o];if(i<0)sr(~i);else{const s=i,a=n[++o],l=n[++o];_I(a,s),r.dirty=!1;const c=Oc(r);try{l(2,t[s])}finally{Nc(r,c)}}}}finally{null===t[Ko]&&Iy(t,Ko),sr(-1)}}(e,t);const a=e.components;null!==a&&Qy(t,a,0);const l=e.viewQuery;if(null!==l&&rd(2,l,r),s){const c=e.viewCheckHooks;null!==c&&Hs(t,c)}else{const c=e.viewHooks;null!==c&&Us(t,c,2),zc(t,2)}!0===e.firstUpdatePass&&(e.firstUpdatePass=!1),t[L]&=-73,yg(t)}finally{Uc()}}}function Zy(e,t){for(let n=gm(e);null!==n;n=mm(n))for(let r=Oe;r<n.length;r++)Yy(n[r],t)}function BA(e,t,n){Yy(dt(t,e),n)}function Yy(e,t){if(!function iI(e){return 128==(128&e[L])}(e))return;const n=e[b],r=e[L];if(80&r&&0===t||1024&r||2===t)Wy(n,e,n.template,e[ve]);else if(e[Zo]>0){Zy(e,1);const o=n.components;null!==o&&Qy(e,o,1)}}function Qy(e,t,n){for(let r=0;r<t.length;r++)BA(e,t[r],n)}class Ei{get rootNodes(){const t=this._lView,n=t[b];return bi(n,t,n.firstChild,[])}constructor(t,n){this._lView=t,this._cdRefInjectingView=n,this._appRef=null,this._attachedToViewContainer=!1}get context(){return this._lView[ve]}set context(t){this._lView[ve]=t}get destroyed(){return 256==(256&this._lView[L])}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){const t=this._lView[ce];if(ze(t)){const n=t[8],r=n?n.indexOf(this):-1;r>-1&&(aa(t,r),Ys(n,r))}this._attachedToViewContainer=!1}cu(this._lView[b],this._lView)}onDestroy(t){!function _g(e,t){if(256==(256&e[L]))throw new C(911,!1);null===e[kn]&&(e[kn]=[]),e[kn].push(t)}(this._lView,t)}markForCheck(){Di(this._cdRefInjectingView||this._lView)}detach(){this._lView[L]&=-129}reattach(){this._lView[L]|=128}detectChanges(){Aa(this._lView[b],this._lView,this.context)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new C(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null,function NS(e,t){hi(e,t,t[k],2,null,null)}(this._lView[b],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new C(902,!1);this._appRef=t}}class HA extends Ei{constructor(t){super(t),this._view=t}detectChanges(){const t=this._view;Aa(t[b],t,t[ve],!1)}checkNoChanges(){}get context(){return null}}class Xy extends ba{constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){const n=H(t);return new Mi(n,this.ngModule)}}function Jy(e){const t=[];for(let n in e)e.hasOwnProperty(n)&&t.push({propName:e[n],templateName:n});return t}class zA{constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=Os(r);const o=this.injector.get(t,Bu,r);return o!==Bu||n===Bu?o:this.parentInjector.get(t,n,r)}}class Mi extends ry{get inputs(){const t=this.componentDef,n=t.inputTransforms,r=Jy(t.inputs);if(null!==n)for(const o of r)n.hasOwnProperty(o.propName)&&(o.transform=n[o.propName]);return r}get outputs(){return Jy(this.componentDef.outputs)}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=function OM(e){return e.map(TM).join(",")}(t.selectors),this.ngContentSelectors=t.ngContentSelectors?t.ngContentSelectors:[],this.isBoundToModule=!!n}create(t,n,r,o){let i=(o=o||this.ngModule)instanceof ht?o:o?.injector;i&&null!==this.componentDef.getStandaloneInjector&&(i=this.componentDef.getStandaloneInjector(i)||i);const s=i?new zA(t,i):t,a=s.get(iy,null);if(null===a)throw new C(407,!1);const d={rendererFactory:a,sanitizer:s.get($x,null),effectManager:s.get(Gy,null),afterRenderEventManager:s.get(Zu,null)},f=a.createRenderer(null,this.componentDef),h=this.componentDef.selectors[0][0]||"div",p=r?function hA(e,t,n,r){const i=r.get(Dy,!1)||n===Pt.ShadowDom,s=e.selectRootElement(t,i);return function pA(e){Py(e)}(s),s}(f,r,this.componentDef.encapsulation,s):sa(f,h,function UA(e){const t=e.toLowerCase();return"svg"===t?hg:"math"===t?"math":null}(h)),_=this.componentDef.signals?4608:this.componentDef.onPush?576:528;let m=null;null!==p&&(m=Vu(p,s,!0));const w=Ku(0,null,null,1,0,null,null,null,null,null,null),S=Ia(null,w,null,_,null,null,d,f,s,null,m);let j,ye;Hc(S);try{const He=this.componentDef;let Ir,Zh=null;He.findHostDirectiveDefs?(Ir=[],Zh=new Map,He.findHostDirectiveDefs(He,Ir,Zh),Ir.push(He)):Ir=[He];const M$=function qA(e,t){const n=e[b],r=B;return e[r]=t,ao(n,r,2,"#host",null)}(S,p),I$=function WA(e,t,n,r,o,i,s){const a=o[b];!function ZA(e,t,n,r){for(const o of e)t.mergedAttrs=Wo(t.mergedAttrs,o.hostAttrs);null!==t.mergedAttrs&&(xa(t,t.mergedAttrs,!0),null!==n&&Om(r,n,t))}(r,e,t,s);let l=null;null!==t&&(l=Vu(t,o[Rn]));const c=i.rendererFactory.createRenderer(t,n);let u=16;n.signals?u=4096:n.onPush&&(u=64);const d=Ia(o,Ny(n),null,u,o[e.index],e,i,c,null,null,l);return a.firstCreatePass&&td(a,e,r.length-1),Sa(o,d),o[e.index]=d}(M$,p,He,Ir,S,d,f);ye=gg(w,B),p&&function QA(e,t,n,r){if(r)Ic(e,n,["ng-version",Bx.full]);else{const{attrs:o,classes:i}=function NM(e){const t=[],n=[];let r=1,o=2;for(;r<e.length;){let i=e[r];if("string"==typeof i)2===o?""!==i&&t.push(i,e[++r]):8===o&&n.push(i);else{if(!Rt(o))break;o=i}r++}return{attrs:t,classes:n}}(t.selectors[0]);o&&Ic(e,n,o),i&&i.length>0&&Tm(e,n,i.join(" "))}}(f,He,p,r),void 0!==n&&function XA(e,t,n){const r=e.projection=[];for(let o=0;o<t.length;o++){const i=n[o];r.push(null!=i?Array.from(i):null)}}(ye,this.ngContentSelectors,n),j=function YA(e,t,n,r,o,i){const s=Pe(),a=o[b],l=nt(s,o);Fy(a,o,s,n,null,r);for(let u=0;u<n.length;u++)Le(ar(o,a,s.directiveStart+u,s),o);Ly(a,o,s),l&&Le(l,o);const c=ar(o,a,s.directiveStart+s.componentOffset,s);if(e[ve]=o[ve]=c,null!==i)for(const u of i)u(c,t);return Qu(a,s,e),c}(I$,He,Ir,Zh,S,[JA]),id(w,S,null)}finally{Uc()}return new GA(this.componentType,j,oo(ye,S),S,ye)}}class GA extends Rx{constructor(t,n,r,o,i){super(),this.location=r,this._rootLView=o,this._tNode=i,this.previousInputValues=null,this.instance=n,this.hostView=this.changeDetectorRef=new HA(o),this.componentType=t}setInput(t,n){const r=this._tNode.inputs;let o;if(null!==r&&(o=r[t])){if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;const i=this._rootLView;od(i[b],i,o,t,n),this.previousInputValues.set(t,n),Di(dt(this._tNode.index,i))}}get injector(){return new We(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}}function JA(){const e=Pe();Bs(v()[b],e)}function J(e){let t=function Ky(e){return Object.getPrototypeOf(e.prototype).constructor}(e.type),n=!0;const r=[e];for(;t;){let o;if(Ft(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new C(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);const s=e;s.inputs=Ta(e.inputs),s.inputTransforms=Ta(e.inputTransforms),s.declaredInputs=Ta(e.declaredInputs),s.outputs=Ta(e.outputs);const a=o.hostBindings;a&&nT(e,a);const l=o.viewQuery,c=o.contentQueries;if(l&&eT(e,l),c&&tT(e,c),Ms(e.inputs,o.inputs),Ms(e.declaredInputs,o.declaredInputs),Ms(e.outputs,o.outputs),null!==o.inputTransforms&&(null===s.inputTransforms&&(s.inputTransforms={}),Ms(s.inputTransforms,o.inputTransforms)),Ft(o)&&o.data.animation){const u=e.data;u.animation=(u.animation||[]).concat(o.data.animation)}}const i=o.features;if(i)for(let s=0;s<i.length;s++){const a=i[s];a&&a.ngInherit&&a(e),a===J&&(n=!1)}}t=Object.getPrototypeOf(t)}!function KA(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){const o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=Wo(o.hostAttrs,n=Wo(n,o.hostAttrs))}}(r)}function Ta(e){return e===qt?{}:e===G?[]:e}function eT(e,t){const n=e.viewQuery;e.viewQuery=n?(r,o)=>{t(r,o),n(r,o)}:t}function tT(e,t){const n=e.contentQueries;e.contentQueries=n?(r,o,i)=>{t(r,o,i),n(r,o,i)}:t}function nT(e,t){const n=e.hostBindings;e.hostBindings=n?(r,o)=>{t(r,o),n(r,o)}:t}function Oa(e){return!!sd(e)&&(Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e)}function sd(e){return null!==e&&("function"==typeof e||"object"==typeof e)}function Kt(e,t,n){return e[t]=n}function Ve(e,t,n){return!Object.is(e[t],n)&&(e[t]=n,!0)}function tn(e,t,n,r,o,i,s,a){const l=v(),c=U(),u=e+B,d=c.firstCreatePass?function ST(e,t,n,r,o,i,s,a,l){const c=t.consts,u=ao(t,e,4,s||null,Ln(c,a));ed(t,n,u,Ln(c,l)),Bs(t,u);const d=u.tView=Ku(2,u,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,c,null);return null!==t.queries&&(t.queries.template(t,u),d.queries=t.queries.embeddedTView(u)),u}(u,c,l,t,n,r,o,i,s):c.data[u];Qt(d,!1);const f=mv(c,l,d,e);$s()&&ca(c,l,f,d),Le(f,l),Sa(l,l[u]=jy(f,l,f,d)),Fs(d)&&Xu(c,l,d),null!=s&&Ju(l,d,a)}let mv=function yv(e,t,n,r){return Vn(!0),t[k].createComment("")};function ke(e,t,n){const r=v();return Ve(r,jr(),t)&&function mt(e,t,n,r,o,i,s,a){const l=nt(t,n);let u,c=t.inputs;!a&&null!=c&&(u=c[r])?(od(e,n,u,r,o),nr(t)&&function DA(e,t){const n=dt(t,e);16&n[L]||(n[L]|=64)}(n,t.index)):3&t.type&&(r=function CA(e){return"class"===e?"className":"for"===e?"htmlFor":"formaction"===e?"formAction":"innerHtml"===e?"innerHTML":"readonly"===e?"readOnly":"tabindex"===e?"tabIndex":e}(r),o=null!=s?s(o,t.value||"",r):o,i.setProperty(l,r,o))}(U(),function de(){const e=O.lFrame;return gg(e.tView,e.selectedIndex)}(),r,e,t,r[k],n,!1),ke}function fd(e,t,n,r,o){const s=o?"class":"style";od(e,n,t.inputs[s],s,r)}function se(e,t,n,r){const o=v(),i=U(),s=B+e,a=o[k],l=i.firstCreatePass?function NT(e,t,n,r,o,i){const s=t.consts,l=ao(t,e,2,r,Ln(s,o));return ed(t,n,l,Ln(s,i)),null!==l.attrs&&xa(l,l.attrs,!1),null!==l.mergedAttrs&&xa(l,l.mergedAttrs,!0),null!==t.queries&&t.queries.elementStart(t,l),l}(s,i,o,t,n,r):i.data[s],c=vv(i,o,l,a,t,e);o[s]=c;const u=Fs(l);return Qt(l,!0),Om(a,c,l),32!=(32&l.flags)&&$s()&&ca(i,o,c,l),0===function cI(){return O.lFrame.elementDepthCount}()&&Le(c,o),function uI(){O.lFrame.elementDepthCount++}(),u&&(Xu(i,o,l),Qu(i,l,o)),null!==r&&Ju(o,l),se}function fe(){let e=Pe();Lc()?function Vc(){O.lFrame.isParent=!1}():(e=e.parent,Qt(e,!1));const t=e;(function fI(e){return O.skipHydrationRootTNode===e})(t)&&function mI(){O.skipHydrationRootTNode=null}(),function dI(){O.lFrame.elementDepthCount--}();const n=U();return n.firstCreatePass&&(Bs(n,e),xc(e)&&n.queries.elementEnd(e)),null!=t.classesWithoutHost&&function TI(e){return 0!=(8&e.flags)}(t)&&fd(n,t,v(),t.classesWithoutHost,!0),null!=t.stylesWithoutHost&&function OI(e){return 0!=(16&e.flags)}(t)&&fd(n,t,v(),t.stylesWithoutHost,!1),fe}function Lt(e,t,n,r){return se(e,t,n,r),fe(),Lt}let vv=(e,t,n,r,o,i)=>(Vn(!0),sa(r,o,function Pg(){return O.lFrame.currentNamespace}()));function Fa(){return v()}function Ti(e){return!!e&&"function"==typeof e.then}function Dv(e){return!!e&&"function"==typeof e.subscribe}function je(e,t,n,r){const o=v(),i=U(),s=Pe();return function bv(e,t,n,r,o,i,s){const a=Fs(r),c=e.firstCreatePass&&Hy(e),u=t[ve],d=By(t);let f=!0;if(3&r.type||s){const g=nt(r,t),y=s?s(g):g,_=d.length,m=s?S=>s(oe(S[r.index])):r.index;let w=null;if(!s&&a&&(w=function VT(e,t,n,r){const o=e.cleanup;if(null!=o)for(let i=0;i<o.length-1;i+=2){const s=o[i];if(s===n&&o[i+1]===r){const a=t[Or],l=o[i+2];return a.length>l?a[l]:null}"string"==typeof s&&(i+=2)}return null}(e,t,o,r.index)),null!==w)(w.__ngLastListenerFn__||w).__ngNextListenerFn__=i,w.__ngLastListenerFn__=i,f=!1;else{i=Mv(r,t,u,i,!1);const S=n.listen(y,o,i);d.push(i,S),c&&c.push(o,m,_,_+1)}}else i=Mv(r,t,u,i,!1);const h=r.outputs;let p;if(f&&null!==h&&(p=h[o])){const g=p.length;if(g)for(let y=0;y<g;y+=2){const j=t[p[y]][p[y+1]].subscribe(i),ye=d.length;d.push(i,j),c&&c.push(o,r.index,ye,-(ye+1))}}}(i,o,o[k],s,e,t,r),je}function Ev(e,t,n,r){try{return Yt(6,t,n),!1!==n(r)}catch(o){return zy(e,o),!1}finally{Yt(7,t,n)}}function Mv(e,t,n,r,o){return function i(s){if(s===Function)return r;Di(e.componentOffset>-1?dt(e.index,t):t);let l=Ev(t,n,r,s),c=i.__ngNextListenerFn__;for(;c;)l=Ev(t,n,c,s)&&l,c=c.__ngNextListenerFn__;return o&&!1===l&&s.preventDefault(),l}}function Vt(e=1){return function wI(e){return(O.lFrame.contextLView=function bI(e,t){for(;e>0;)t=t[Pr],e--;return t}(e,O.lFrame.contextLView))[ve]}(e)}function La(e,t){return e<<17|t<<2}function Bn(e){return e>>17&32767}function md(e){return 2|e}function fr(e){return(131068&e)>>2}function yd(e,t){return-131069&e|t<<2}function vd(e){return 1|e}function kv(e,t,n,r,o){const i=e[n+1],s=null===t;let a=r?Bn(i):fr(i),l=!1;for(;0!==a&&(!1===l||s);){const u=e[a+1];WT(e[a],t)&&(l=!0,e[a+1]=r?vd(u):md(u)),a=r?Bn(u):fr(u)}l&&(e[n+1]=r?md(i):vd(i))}function WT(e,t){return null===e||null==t||(Array.isArray(e)?e[1]:e)===t||!(!Array.isArray(e)||"string"!=typeof t)&&Wr(e,t)>=0}function Oi(e,t){return function jt(e,t,n,r){const o=v(),i=U(),s=function pn(e){const t=O.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}(2);i.firstUpdatePass&&function zv(e,t,n,r){const o=e.data;if(null===o[n+1]){const i=o[qe()],s=function Uv(e,t){return t>=e.expandoStartIndex}(e,n);(function Zv(e,t){return 0!=(e.flags&(t?8:16))})(i,r)&&null===t&&!s&&(t=!1),t=function nO(e,t,n,r){const o=function $c(e){const t=O.lFrame.currentDirectiveIndex;return-1===t?null:e[t]}(e);let i=r?t.residualClasses:t.residualStyles;if(null===o)0===(r?t.classBindings:t.styleBindings)&&(n=Ni(n=_d(null,e,t,n,r),t.attrs,r),i=null);else{const s=t.directiveStylingLast;if(-1===s||e[s]!==o)if(n=_d(o,e,t,n,r),null===i){let l=function rO(e,t,n){const r=n?t.classBindings:t.styleBindings;if(0!==fr(r))return e[Bn(r)]}(e,t,r);void 0!==l&&Array.isArray(l)&&(l=_d(null,e,t,l[1],r),l=Ni(l,t.attrs,r),function oO(e,t,n,r){e[Bn(n?t.classBindings:t.styleBindings)]=r}(e,t,r,l))}else i=function iO(e,t,n){let r;const o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++)r=Ni(r,e[i].hostAttrs,n);return Ni(r,t.attrs,n)}(e,t,r)}return void 0!==i&&(r?t.residualClasses=i:t.residualStyles=i),n}(o,i,t,r),function GT(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=Bn(s),l=fr(s);e[r]=n;let u,c=!1;if(Array.isArray(n)?(u=n[1],(null===u||Wr(n,u)>0)&&(c=!0)):u=n,o)if(0!==l){const f=Bn(e[a+1]);e[r+1]=La(f,a),0!==f&&(e[f+1]=yd(e[f+1],r)),e[a+1]=function UT(e,t){return 131071&e|t<<17}(e[a+1],r)}else e[r+1]=La(a,0),0!==a&&(e[a+1]=yd(e[a+1],r)),a=r;else e[r+1]=La(l,0),0===a?a=r:e[l+1]=yd(e[l+1],r),l=r;c&&(e[r+1]=md(e[r+1])),kv(e,u,r,!0),kv(e,u,r,!1),function qT(e,t,n,r,o){const i=o?e.residualClasses:e.residualStyles;null!=i&&"string"==typeof t&&Wr(i,t)>=0&&(n[r+1]=vd(n[r+1]))}(t,u,e,r,i),s=La(a,l),i?t.classBindings=s:t.styleBindings=s}(o,i,t,n,s,r)}}(i,e,s,r),t!==F&&Ve(o,s,t)&&function qv(e,t,n,r,o,i,s,a){if(!(3&t.type))return;const l=e.data,c=l[a+1],u=function zT(e){return 1==(1&e)}(c)?Wv(l,t,n,o,fr(c),s):void 0;Va(u)||(Va(i)||function HT(e){return 2==(2&e)}(c)&&(i=Wv(l,null,n,o,a,s)),function HS(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=-1===r.indexOf("-")?void 0:jn.DashCase;null==o?e.removeStyle(n,r,i):("string"==typeof o&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=jn.Important),e.setStyle(n,r,o,i))}}(r,s,js(qe(),n),o,i))}(i,i.data[qe()],o,o[k],e,o[s+1]=function cO(e,t){return null==e||""===e||("string"==typeof t?e+=t:"object"==typeof e&&(e=Ie($n(e)))),e}(t,n),r,s)}(e,t,null,!0),Oi}function _d(e,t,n,r,o){let i=null;const s=n.directiveEnd;let a=n.directiveStylingLast;for(-1===a?a=n.directiveStart:a++;a<s&&(i=t[a],r=Ni(r,i.hostAttrs,o),i!==e);)a++;return null!==e&&(n.directiveStylingLast=a),r}function Ni(e,t,n){const r=n?1:2;let o=-1;if(null!==t)for(let i=0;i<t.length;i++){const s=t[i];"number"==typeof s?o=s:o===r&&(Array.isArray(e)||(e=void 0===e?[]:["",e]),ft(e,s,!!n||t[++i]))}return void 0===e?null:e}function Wv(e,t,n,r,o,i){const s=null===t;let a;for(;o>0;){const l=e[o],c=Array.isArray(l),u=c?l[1]:l,d=null===u;let f=n[o+1];f===F&&(f=d?G:void 0);let h=d?Jc(f,r):u===r?f:void 0;if(c&&!Va(h)&&(h=Jc(l,r)),Va(h)&&(a=h,s))return a;const p=e[o+1];o=s?Bn(p):fr(p)}if(null!==t){let l=i?t.residualClasses:t.residualStyles;null!=l&&(a=Jc(l,r))}return a}function Va(e){return void 0!==e}function yt(e,t=""){const n=v(),r=U(),o=e+B,i=r.firstCreatePass?ao(r,o,1,t,null):r.data[o],s=Yv(r,n,i,t,e);n[o]=s,$s()&&ca(r,n,s,i),Qt(i,!1)}let Yv=(e,t,n,r,o)=>(Vn(!0),function ia(e,t){return e.createText(t)}(t[k],r));function Cn(e){return Cd("",e,""),Cn}function Cd(e,t,n){const r=v(),o=function co(e,t,n,r){return Ve(e,jr(),n)?t+R(n)+r:F}(r,e,t,n);return o!==F&&function _n(e,t,n){const r=js(t,e);!function vm(e,t,n){e.setValue(t,n)}(e[k],r,n)}(r,qe(),o),Cd}const hr=void 0;var OO=["en",[["a","p"],["AM","PM"],hr],[["AM","PM"],hr,hr],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],hr,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],hr,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",hr,"{1} 'at' {0}",hr],[".",",",";","%","+","-","E","\xd7","\u2030","\u221e","NaN",":"],["#,##0.###","#,##0%","\xa4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",function TO(e){const n=Math.floor(Math.abs(e)),r=e.toString().replace(/^[^.]*\.?/,"").length;return 1===n&&0===r?1:5}];let _o={};function Ze(e){const t=function NO(e){return e.toLowerCase().replace(/_/g,"-")}(e);let n=m_(t);if(n)return n;const r=t.split("-")[0];if(n=m_(r),n)return n;if("en"===r)return OO;throw new C(701,!1)}function m_(e){return e in _o||(_o[e]=re.ng&&re.ng.common&&re.ng.common.locales&&re.ng.common.locales[e]),_o[e]}var ae=function(e){return e[e.LocaleId=0]="LocaleId",e[e.DayPeriodsFormat=1]="DayPeriodsFormat",e[e.DayPeriodsStandalone=2]="DayPeriodsStandalone",e[e.DaysFormat=3]="DaysFormat",e[e.DaysStandalone=4]="DaysStandalone",e[e.MonthsFormat=5]="MonthsFormat",e[e.MonthsStandalone=6]="MonthsStandalone",e[e.Eras=7]="Eras",e[e.FirstDayOfWeek=8]="FirstDayOfWeek",e[e.WeekendRange=9]="WeekendRange",e[e.DateFormat=10]="DateFormat",e[e.TimeFormat=11]="TimeFormat",e[e.DateTimeFormat=12]="DateTimeFormat",e[e.NumberSymbols=13]="NumberSymbols",e[e.NumberFormats=14]="NumberFormats",e[e.CurrencyCode=15]="CurrencyCode",e[e.CurrencySymbol=16]="CurrencySymbol",e[e.CurrencyName=17]="CurrencyName",e[e.Currencies=18]="Currencies",e[e.Directionality=19]="Directionality",e[e.PluralCase=20]="PluralCase",e[e.ExtraData=21]="ExtraData",e}(ae||{});const Co="en-US";let y_=Co;function bd(e,t,n,r,o){if(e=N(e),Array.isArray(e))for(let i=0;i<e.length;i++)bd(e[i],t,n,r,o);else{const i=U(),s=v(),a=Pe();let l=cr(e)?e:N(e.provide);const c=Xm(e),u=1048575&a.providerIndexes,d=a.directiveStart,f=a.providerIndexes>>20;if(cr(e)||!e.multi){const h=new ri(c,o,D),p=Md(l,t,o?u:u+f,d);-1===p?(Yc(Gs(a,s),i,l),Ed(i,e,t.length),t.push(l),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(h),s.push(h)):(n[p]=h,s[p]=h)}else{const h=Md(l,t,u+f,d),p=Md(l,t,u,u+f),y=p>=0&&n[p];if(o&&!y||!o&&!(h>=0&&n[h])){Yc(Gs(a,s),i,l);const _=function TN(e,t,n,r,o){const i=new ri(e,n,D);return i.multi=[],i.index=t,i.componentProviders=0,H_(i,o,r&&!n),i}(o?AN:xN,n.length,o,r,c);!o&&y&&(n[p].providerFactory=_),Ed(i,e,t.length,0),t.push(l),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(_),s.push(_)}else Ed(i,e,h>-1?h:p,H_(n[o?p:h],c,!o&&r));!o&&r&&y&&n[p].componentProviders++}}}function Ed(e,t,n,r){const o=cr(t),i=function yx(e){return!!e.useClass}(t);if(o||i){const l=(i?N(t.useClass):t).prototype.ngOnDestroy;if(l){const c=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){const u=c.indexOf(n);-1===u?c.push(n,[r,l]):c[u+1].push(r,l)}else c.push(n,l)}}}function H_(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function Md(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function xN(e,t,n,r){return Id(this.multi,[])}function AN(e,t,n,r){const o=this.multi;let i;if(this.providerFactory){const s=this.providerFactory.componentProviders,a=ar(n,n[b],this.providerFactory.index,r);i=a.slice(0,s),Id(o,i);for(let l=s;l<a.length;l++)i.push(a[l])}else i=[],Id(o,i);return i}function Id(e,t){for(let n=0;n<e.length;n++)t.push((0,e[n])());return t}function ue(e,t=[]){return n=>{n.providersResolver=(r,o)=>function SN(e,t,n){const r=U();if(r.firstCreatePass){const o=Ft(e);bd(n,r.data,r.blueprint,o,!0),bd(t,r.data,r.blueprint,o,!1)}}(r,o?o(e):e,t)}}class pr{}class U_{}class Sd extends pr{constructor(t,n,r){super(),this._parent=n,this._bootstrapComponents=[],this.destroyCbs=[],this.componentFactoryResolver=new Xy(this);const o=ut(t);this._bootstrapComponents=vn(o.bootstrap),this._r3Injector=uy(t,n,[{provide:pr,useValue:this},{provide:ba,useValue:this.componentFactoryResolver},...r],Ie(t),new Set(["environment"])),this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(t)}get injector(){return this._r3Injector}destroy(){const t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}}class xd extends U_{constructor(t){super(),this.moduleType=t}create(t){return new Sd(this.moduleType,t,[])}}class z_ extends pr{constructor(t){super(),this.componentFactoryResolver=new Xy(this),this.instance=null;const n=new eo([...t.providers,{provide:pr,useValue:this},{provide:ba,useValue:this.componentFactoryResolver}],t.parent||ma(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}}function Ad(e,t,n=null){return new z_({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}let PN=(()=>{class e{constructor(n){this._injector=n,this.cachedInjectors=new Map}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){const r=Wm(0,n.type),o=r.length>0?Ad([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(const n of this.cachedInjectors.values())null!==n&&n.destroy()}finally{this.cachedInjectors.clear()}}static#e=this.\u0275prov=x({token:e,providedIn:"environment",factory:()=>new e(I(ht))})}return e})();function G_(e){e.getStandaloneInjector=t=>t.get(PN).getOrCreateStandaloneInjector(e)}function Vi(e,t){const n=e[t];return n===F?void 0:n}function eC(e,t,n,r,o,i){const s=t+n;return Ve(e,s,o)?Kt(e,s+1,i?r.call(i,o):r(o)):Vi(e,s+1)}function tC(e,t,n,r,o,i,s){const a=t+n;return function dr(e,t,n,r){const o=Ve(e,t,n);return Ve(e,t+1,r)||o}(e,a,o,i)?Kt(e,a+2,s?r.call(s,o,i):r(o,i)):Vi(e,a+2)}function Ua(e,t){const n=U();let r;const o=e+B;n.firstCreatePass?(r=function QN(e,t){if(t)for(let n=t.length-1;n>=0;n--){const r=t[n];if(e===r.name)return r}}(t,n.pipeRegistry),n.data[o]=r,r.onDestroy&&(n.destroyHooks??=[]).push(o,r.onDestroy)):r=n.data[o];const i=r.factory||(r.factory=rr(r.type)),a=Ke(D);try{const l=zs(!1),c=i();return zs(l),function TT(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}(n,v(),o,c),c}finally{Ke(a)}}function Od(e,t,n,r){const o=e+B,i=v(),s=Lr(i,o);return ji(i,o)?tC(i,Ge(),t,s.transform,n,r,s):s.transform(n,r)}function ji(e,t){return e[b].data[t].pure}function eP(){return this._results[Symbol.iterator]()}class Nd{static#e=Symbol.iterator;get changes(){return this._changes||(this._changes=new ge)}constructor(t=!1){this._emitDistinctChangesOnly=t,this.dirty=!0,this._results=[],this._changesDetected=!1,this._changes=null,this.length=0,this.first=void 0,this.last=void 0;const n=Nd.prototype;n[Symbol.iterator]||(n[Symbol.iterator]=eP)}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){const r=this;r.dirty=!1;const o=function Mt(e){return e.flat(Number.POSITIVE_INFINITY)}(t);(this._changesDetected=!function GI(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=t[r];if(n&&(o=n(o),i=n(i)),i!==o)return!1}return!0}(r._results,o,n))&&(r._results=o,r.length=o.length,r.last=o[this.length-1],r.first=o[0])}notifyOnChanges(){this._changes&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.emit(this)}setDirty(){this.dirty=!0}destroy(){this.changes.complete(),this.changes.unsubscribe()}}function nP(e,t,n,r=!0){const o=t[b];if(function RS(e,t,n,r){const o=Oe+r,i=n.length;r>0&&(n[o-1][kt]=t),r<i-Oe?(t[kt]=n[o],Zg(n,Oe+r,t)):(n.push(t),t[kt]=null),t[ce]=n;const s=t[Qo];null!==s&&n!==s&&function kS(e,t){const n=e[kr];t[_e]!==t[ce][ce][_e]&&(e[Wp]=!0),null===n?e[kr]=[t]:n.push(t)}(s,t);const a=t[Wt];null!==a&&a.insertView(e),t[L]|=128}(o,t,e,n),r){const i=hu(n,e),s=t[k],a=la(s,e[Zt]);null!==a&&function OS(e,t,n,r,o,i){r[pe]=o,r[Fe]=t,hi(e,r,n,1,o,i)}(o,e[Fe],s,t,a,i)}}let Dn=(()=>{class e{static#e=this.__NG_ELEMENT_ID__=iP}return e})();const rP=Dn,oP=class extends rP{constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){const o=function tP(e,t,n,r){const o=t.tView,a=Ia(e,o,n,4096&e[L]?4096:16,null,t,null,null,null,r?.injector??null,r?.hydrationInfo??null);a[Qo]=e[t.index];const c=e[Wt];return null!==c&&(a[Wt]=c.createEmbeddedView(o)),id(o,a,n),a}(this._declarationLView,this._declarationTContainer,t,{injector:n,hydrationInfo:r});return new Ei(o)}};function iP(){return za(Pe(),v())}function za(e,t){return 4&e.type?new oP(t,e,oo(e,t)):null}let Bt=(()=>{class e{static#e=this.__NG_ELEMENT_ID__=dP}return e})();function dP(){return fC(Pe(),v())}const fP=Bt,uC=class extends fP{constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return oo(this._hostTNode,this._hostLView)}get injector(){return new We(this._hostTNode,this._hostLView)}get parentInjector(){const t=qs(this._hostTNode,this._hostLView);if(qc(t)){const n=ii(t,this._hostLView),r=oi(t);return new We(n[b].data[r+8],n)}return new We(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){const n=dC(this._lContainer);return null!==n&&n[t]||null}get length(){return this._lContainer.length-Oe}createEmbeddedView(t,n,r){let o,i;"number"==typeof r?o=r:null!=r&&(o=r.index,i=r.injector);const a=t.createEmbeddedViewImpl(n||{},i,null);return this.insertImpl(a,o,false),a}createComponent(t,n,r,o,i){const s=t&&!function ai(e){return"function"==typeof e}(t);let a;if(s)a=n;else{const g=n||{};a=g.index,r=g.injector,o=g.projectableNodes,i=g.environmentInjector||g.ngModuleRef}const l=s?t:new Mi(H(t)),c=r||this.parentInjector;if(!i&&null==l.ngModule){const y=(s?c:this.parentInjector).get(ht,null);y&&(i=y)}H(l.componentType??{});const h=l.create(c,o,null,i);return this.insertImpl(h.hostView,a,false),h}insert(t,n){return this.insertImpl(t,n,!1)}insertImpl(t,n,r){const o=t._lView;if(function sI(e){return ze(e[ce])}(o)){const l=this.indexOf(t);if(-1!==l)this.detach(l);else{const c=o[ce],u=new uC(c,c[Fe],c[ce]);u.detach(u.indexOf(t))}}const s=this._adjustIndex(n),a=this._lContainer;return nP(a,o,s,!r),t.attachToViewContainerRef(),Zg(Pd(a),s,t),t}move(t,n){return this.insert(t,n)}indexOf(t){const n=dC(this._lContainer);return null!==n?n.indexOf(t):-1}remove(t){const n=this._adjustIndex(t,-1),r=aa(this._lContainer,n);r&&(Ys(Pd(this._lContainer),n),cu(r[b],r))}detach(t){const n=this._adjustIndex(t,-1),r=aa(this._lContainer,n);return r&&null!=Ys(Pd(this._lContainer),n)?new Ei(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function dC(e){return e[8]}function Pd(e){return e[8]||(e[8]=[])}function fC(e,t){let n;const r=t[e.index];return ze(r)?n=r:(n=jy(r,t,null,e),t[e.index]=n,Sa(t,n)),hC(n,t,e,r),new uC(n,e,t)}let hC=function pC(e,t,n,r){if(e[Zt])return;let o;o=8&n.type?oe(r):function hP(e,t){const n=e[k],r=n.createComment(""),o=nt(t,e);return lr(n,la(n,o),r,function jS(e,t){return e.nextSibling(t)}(n,o),!1),r}(t,n),e[Zt]=o};class Rd{constructor(t){this.queryList=t,this.matches=null}clone(){return new Rd(this.queryList)}setDirty(){this.queryList.setDirty()}}class kd{constructor(t=[]){this.queries=t}createEmbeddedView(t){const n=t.queries;if(null!==n){const r=null!==t.contentQueries?t.contentQueries[0]:n.length,o=[];for(let i=0;i<r;i++){const s=n.getByIndex(i);o.push(this.queries[s.indexInDeclarationView].clone())}return new kd(o)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)null!==DC(t,n).matches&&this.queries[n].setDirty()}}class gC{constructor(t,n,r=null){this.predicate=t,this.flags=n,this.read=r}}class Fd{constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){const o=null!==n?n.length:0,i=this.getByIndex(r).embeddedTView(t,o);i&&(i.indexInDeclarationView=r,null!==n?n.push(i):n=[i])}return null!==n?new Fd(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}}class Ld{constructor(t,n=-1){this.metadata=t,this.matches=null,this.indexInDeclarationView=-1,this.crossesNgTemplate=!1,this._appliesToNextNode=!0,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new Ld(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&1!=(1&this.metadata.flags)){const n=this._declarationNodeIndex;let r=t.parent;for(;null!==r&&8&r.type&&r.index!==n;)r=r.parent;return n===(null!==r?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){const r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){const i=r[o];this.matchTNodeWithReadOption(t,n,mP(n,i)),this.matchTNodeWithReadOption(t,n,Ws(n,t,i,!1,!1))}else r===Dn?4&n.type&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,Ws(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(null!==r){const o=this.metadata.read;if(null!==o)if(o===pt||o===Bt||o===Dn&&4&n.type)this.addMatch(n.index,-2);else{const i=Ws(n,t,o,!1,!1);null!==i&&this.addMatch(n.index,i)}else this.addMatch(n.index,r)}}addMatch(t,n){null===this.matches?this.matches=[t,n]:this.matches.push(t,n)}}function mP(e,t){const n=e.localNames;if(null!==n)for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1];return null}function vP(e,t,n,r){return-1===n?function yP(e,t){return 11&e.type?oo(e,t):4&e.type?za(e,t):null}(t,e):-2===n?function _P(e,t,n){return n===pt?oo(t,e):n===Dn?za(t,e):n===Bt?fC(t,e):void 0}(e,t,r):ar(e,e[b],n,t)}function mC(e,t,n,r){const o=t[Wt].queries[r];if(null===o.matches){const i=e.data,s=n.matches,a=[];for(let l=0;l<s.length;l+=2){const c=s[l];a.push(c<0?null:vP(t,i[c],s[l+1],n.metadata.read))}o.matches=a}return o.matches}function Vd(e,t,n,r){const o=e.queries.getByIndex(n),i=o.matches;if(null!==i){const s=mC(e,t,o,n);for(let a=0;a<i.length;a+=2){const l=i[a];if(l>0)r.push(s[a/2]);else{const c=i[a+1],u=t[-l];for(let d=Oe;d<u.length;d++){const f=u[d];f[Qo]===f[ce]&&Vd(f[b],f,c,r)}if(null!==u[kr]){const d=u[kr];for(let f=0;f<d.length;f++){const h=d[f];Vd(h[b],h,c,r)}}}}}return r}function jd(e){const t=v(),n=U(),r=Ig();Bc(r+1);const o=DC(n,r);if(e.dirty&&function oI(e){return 4==(4&e[L])}(t)===(2==(2&o.metadata.flags))){if(null===o.matches)e.reset([]);else{const i=o.crossesNgTemplate?Vd(n,t,r,[]):mC(n,t,o,r);e.reset(i,Vx),e.notifyOnChanges()}return!0}return!1}function yC(e,t,n){const r=U();r.firstCreatePass&&(function CC(e,t,n){null===e.queries&&(e.queries=new Fd),e.queries.track(new Ld(t,n))}(r,new gC(e,t,n),-1),2==(2&t)&&(r.staticViewQueries=!0)),function _C(e,t,n){const r=new Nd(4==(4&n));(function yA(e,t,n,r){const o=By(t);o.push(n),e.firstCreatePass&&Hy(e).push(r,o.length-1)})(e,t,r,r.destroy),null===t[Wt]&&(t[Wt]=new kd),t[Wt].queries.push(new Rd(r))}(r,v(),t)}function DC(e,t){return e.queries.getByIndex(t)}const qd=new M("Application Initializer");let Wd=(()=>{class e{constructor(){this.initialized=!1,this.done=!1,this.donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r}),this.appInits=E(qd,{optional:!0})??[]}runInitializers(){if(this.initialized)return;const n=[];for(const o of this.appInits){const i=o();if(Ti(i))n.push(i);else if(Dv(i)){const s=new Promise((a,l)=>{i.subscribe({complete:a,error:l})});n.push(s)}}const r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),0===n.length&&r(),this.initialized=!0}static#e=this.\u0275fac=function(r){return new(r||e)};static#t=this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),VC=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static#e=this.\u0275fac=function(r){return new(r||e)};static#t=this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();const wn=new M("LocaleId",{providedIn:"root",factory:()=>E(wn,$.Optional|$.SkipSelf)||function UP(){return typeof $localize<"u"&&$localize.locale||Co}()});let Wa=(()=>{class e{constructor(){this.taskId=0,this.pendingTasks=new Set,this.hasPendingTasks=new Ct(!1)}add(){this.hasPendingTasks.next(!0);const n=this.taskId++;return this.pendingTasks.add(n),n}remove(n){this.pendingTasks.delete(n),0===this.pendingTasks.size&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this.hasPendingTasks.next(!1)}static#e=this.\u0275fac=function(r){return new(r||e)};static#t=this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();class qP{constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}}let jC=(()=>{class e{compileModuleSync(n){return new xd(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){const r=this.compileModuleSync(n),i=vn(ut(n).declarations).reduce((s,a)=>{const l=H(a);return l&&s.push(new Mi(l)),s},[]);return new qP(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static#e=this.\u0275fac=function(r){return new(r||e)};static#t=this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();const UC=new M(""),Ya=new M("");let Jd,Qd=(()=>{class e{constructor(n,r,o){this._ngZone=n,this.registry=r,this._pendingCount=0,this._isZoneStable=!0,this._didWork=!1,this._callbacks=[],this.taskTrackingZone=null,Jd||(function pR(e){Jd=e}(o),o.addToWindow(r)),this._watchAngularEvents(),n.run(()=>{this.taskTrackingZone=typeof Zone>"u"?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){this._ngZone.onUnstable.subscribe({next:()=>{this._didWork=!0,this._isZoneStable=!1}}),this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.subscribe({next:()=>{ie.assertNotInAngularZone(),queueMicrotask(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}})})}increasePendingRequestCount(){return this._pendingCount+=1,this._didWork=!0,this._pendingCount}decreasePendingRequestCount(){if(this._pendingCount-=1,this._pendingCount<0)throw new Error("pending async requests below zero");return this._runCallbacksIfReady(),this._pendingCount}isStable(){return this._isZoneStable&&0===this._pendingCount&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())queueMicrotask(()=>{for(;0!==this._callbacks.length;){let n=this._callbacks.pop();clearTimeout(n.timeoutId),n.doneCb(this._didWork)}this._didWork=!1});else{let n=this.getPendingTasks();this._callbacks=this._callbacks.filter(r=>!r.updateCb||!r.updateCb(n)||(clearTimeout(r.timeoutId),!1)),this._didWork=!0}}getPendingTasks(){return this.taskTrackingZone?this.taskTrackingZone.macroTasks.map(n=>({source:n.source,creationLocation:n.creationLocation,data:n.data})):[]}addCallback(n,r,o){let i=-1;r&&r>0&&(i=setTimeout(()=>{this._callbacks=this._callbacks.filter(s=>s.timeoutId!==i),n(this._didWork,this.getPendingTasks())},r)),this._callbacks.push({doneCb:n,timeoutId:i,updateCb:o})}whenStable(n,r,o){if(o&&!this.taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/plugins/task-tracking" loaded?');this.addCallback(n,r,o),this._runCallbacksIfReady()}getPendingRequestCount(){return this._pendingCount}registerApplication(n){this.registry.registerApplication(n,this)}unregisterApplication(n){this.registry.unregisterApplication(n)}findProviders(n,r,o){return[]}static#e=this.\u0275fac=function(r){return new(r||e)(I(ie),I(Xd),I(Ya))};static#t=this.\u0275prov=x({token:e,factory:e.\u0275fac})}return e})(),Xd=(()=>{class e{constructor(){this._applications=new Map}registerApplication(n,r){this._applications.set(n,r)}unregisterApplication(n){this._applications.delete(n)}unregisterAllApplications(){this._applications.clear()}getTestability(n){return this._applications.get(n)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(n,r=!0){return Jd?.findTestabilityInTree(this,n,r)??null}static#e=this.\u0275fac=function(r){return new(r||e)};static#t=this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})(),Hn=null;const zC=new M("AllowMultipleToken"),Kd=new M("PlatformDestroyListeners"),ef=new M("appBootstrapListener");class qC{constructor(t,n){this.name=t,this.token=n}}function ZC(e,t,n=[]){const r=`Platform: ${t}`,o=new M(r);return(i=[])=>{let s=tf();if(!s||s.injector.get(zC,!1)){const a=[...n,...i,{provide:o,useValue:!0}];e?e(a):function yR(e){if(Hn&&!Hn.get(zC,!1))throw new C(400,!1);(function GC(){!function GM(e){ig=e}(()=>{throw new C(600,!1)})})(),Hn=e;const t=e.get(QC);(function WC(e){e.get(Jm,null)?.forEach(n=>n())})(e)}(function YC(e=[],t){return gt.create({name:t,providers:[{provide:Su,useValue:"platform"},{provide:Kd,useValue:new Set([()=>Hn=null])},...e]})}(a,r))}return function _R(e){const t=tf();if(!t)throw new C(401,!1);return t}()}}function tf(){return Hn?.get(QC)??null}let QC=(()=>{class e{constructor(n){this._injector=n,this._modules=[],this._destroyListeners=[],this._destroyed=!1}bootstrapModuleFactory(n,r){const o=function CR(e="zone.js",t){return"noop"===e?new Kx:"zone.js"===e?new ie(t):e}(r?.ngZone,function XC(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}({eventCoalescing:r?.ngZoneEventCoalescing,runCoalescing:r?.ngZoneRunCoalescing}));return o.run(()=>{const i=function NN(e,t,n){return new Sd(e,t,n)}(n.moduleType,this.injector,function nD(e){return[{provide:ie,useFactory:e},{provide:yi,multi:!0,useFactory:()=>{const t=E(wR,{optional:!0});return()=>t.initialize()}},{provide:tD,useFactory:DR},{provide:gy,useFactory:my}]}(()=>o)),s=i.injector.get(yn,null);return o.runOutsideAngular(()=>{const a=o.onError.subscribe({next:l=>{s.handleError(l)}});i.onDestroy(()=>{Qa(this._modules,i),a.unsubscribe()})}),function JC(e,t,n){try{const r=n();return Ti(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}(s,o,()=>{const a=i.injector.get(Wd);return a.runInitializers(),a.donePromise.then(()=>(function v_(e){Dt(e,"Expected localeId to be defined"),"string"==typeof e&&(y_=e.toLowerCase().replace(/_/g,"-"))}(i.injector.get(wn,Co)||Co),this._moduleDoBootstrap(i),i))})})}bootstrapModule(n,r=[]){const o=KC({},r);return function gR(e,t,n){const r=new xd(n);return Promise.resolve(r)}(0,0,n).then(i=>this.bootstrapModuleFactory(i,o))}_moduleDoBootstrap(n){const r=n.injector.get(bo);if(n._bootstrapComponents.length>0)n._bootstrapComponents.forEach(o=>r.bootstrap(o));else{if(!n.instance.ngDoBootstrap)throw new C(-403,!1);n.instance.ngDoBootstrap(r)}this._modules.push(n)}onDestroy(n){this._destroyListeners.push(n)}get injector(){return this._injector}destroy(){if(this._destroyed)throw new C(404,!1);this._modules.slice().forEach(r=>r.destroy()),this._destroyListeners.forEach(r=>r());const n=this._injector.get(Kd,null);n&&(n.forEach(r=>r()),n.clear()),this._destroyed=!0}get destroyed(){return this._destroyed}static#e=this.\u0275fac=function(r){return new(r||e)(I(gt))};static#t=this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();function KC(e,t){return Array.isArray(t)?t.reduce(KC,e):{...e,...t}}let bo=(()=>{class e{constructor(){this._bootstrapListeners=[],this._runningTick=!1,this._destroyed=!1,this._destroyListeners=[],this._views=[],this.internalErrorHandler=E(tD),this.zoneIsStable=E(gy),this.componentTypes=[],this.components=[],this.isStable=E(Wa).hasPendingTasks.pipe(Nt(n=>n?A(!1):this.zoneIsStable),function nM(e,t=On){return e=e??rM,Ee((n,r)=>{let o,i=!0;n.subscribe(De(r,s=>{const a=t(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}(),bp()),this._injector=E(ht)}get destroyed(){return this._destroyed}get injector(){return this._injector}bootstrap(n,r){const o=n instanceof ry;if(!this._injector.get(Wd).done)throw!o&&function Tr(e){const t=H(e)||Te(e)||Ue(e);return null!==t&&t.standalone}(n),new C(405,!1);let s;s=o?n:this._injector.get(ba).resolveComponentFactory(n),this.componentTypes.push(s.componentType);const a=function mR(e){return e.isBoundToModule}(s)?void 0:this._injector.get(pr),c=s.create(gt.NULL,[],r||s.selector,a),u=c.location.nativeElement,d=c.injector.get(UC,null);return d?.registerApplication(u),c.onDestroy(()=>{this.detachView(c.hostView),Qa(this.components,c),d?.unregisterApplication(u)}),this._loadComponent(c),c}tick(){if(this._runningTick)throw new C(101,!1);try{this._runningTick=!0;for(let n of this._views)n.detectChanges()}catch(n){this.internalErrorHandler(n)}finally{this._runningTick=!1}}attachView(n){const r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){const r=n;Qa(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n);const r=this._injector.get(ef,[]);r.push(...this._bootstrapListeners),r.forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._bootstrapListeners=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>Qa(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new C(406,!1);const n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}warnIfDestroyed(){}static#e=this.\u0275fac=function(r){return new(r||e)};static#t=this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Qa(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}const tD=new M("",{providedIn:"root",factory:()=>E(yn).handleError.bind(void 0)});function DR(){const e=E(ie),t=E(yn);return n=>e.runOutsideAngular(()=>t.handleError(n))}let wR=(()=>{class e{constructor(){this.zone=E(ie),this.applicationRef=E(bo)}initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static#e=this.\u0275fac=function(r){return new(r||e)};static#t=this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();let Xa=(()=>{class e{static#e=this.__NG_ELEMENT_ID__=ER}return e})();function ER(e){return function MR(e,t,n){if(nr(e)&&!n){const r=dt(e.index,t);return new Ei(r,r)}return 47&e.type?new Ei(t[_e],t):null}(Pe(),v(),16==(16&e))}class sD{constructor(){}supports(t){return Oa(t)}create(t){return new OR(t)}}const TR=(e,t)=>t;class OR{constructor(t){this.length=0,this._linkedRecords=null,this._unlinkedRecords=null,this._previousItHead=null,this._itHead=null,this._itTail=null,this._additionsHead=null,this._additionsTail=null,this._movesHead=null,this._movesTail=null,this._removalsHead=null,this._removalsTail=null,this._identityChangesHead=null,this._identityChangesTail=null,this._trackByFn=t||TR}forEachItem(t){let n;for(n=this._itHead;null!==n;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){const s=!r||n&&n.currentIndex<lD(r,o,i)?n:r,a=lD(s,o,i),l=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,null==s.previousIndex)o++;else{i||(i=[]);const c=a-o,u=l-o;if(c!=u){for(let f=0;f<c;f++){const h=f<i.length?i[f]:i[f]=0,p=h+f;u<=p&&p<c&&(i[f]=h+1)}i[s.previousIndex]=u-c}}a!==l&&t(s,a,l)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;null!==n;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;null!==n;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;null!==n;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;null!==n;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;null!==n;n=n._nextIdentityChange)t(n)}diff(t){if(null==t&&(t=[]),!Oa(t))throw new C(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let o,i,s,n=this._itHead,r=!1;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)i=t[a],s=this._trackByFn(a,i),null!==n&&Object.is(n.trackById,s)?(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)):(n=this._mismatch(n,i,s,a),r=!0),n=n._next}else o=0,function cT(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{const n=e[Symbol.iterator]();let r;for(;!(r=n.next()).done;)t(r.value)}}(t,a=>{s=this._trackByFn(o,a),null!==n&&Object.is(n.trackById,s)?(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)):(n=this._mismatch(n,a,s,o),r=!0),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return null!==this._additionsHead||null!==this._movesHead||null!==this._removalsHead||null!==this._identityChangesHead}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;null!==t;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;null!==t;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;null!==t;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,o){let i;return null===t?i=this._itTail:(i=t._prev,this._remove(t)),null!==(t=null===this._unlinkedRecords?null:this._unlinkedRecords.get(r,null))?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,i,o)):null!==(t=null===this._linkedRecords?null:this._linkedRecords.get(r,o))?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,i,o)):t=this._addAfter(new NR(n,r),i,o),t}_verifyReinsertion(t,n,r,o){let i=null===this._unlinkedRecords?null:this._unlinkedRecords.get(r,null);return null!==i?t=this._reinsertAfter(i,t._prev,o):t.currentIndex!=o&&(t.currentIndex=o,this._addToMoves(t,o)),t}_truncate(t){for(;null!==t;){const n=t._next;this._addToRemovals(this._unlink(t)),t=n}null!==this._unlinkedRecords&&this._unlinkedRecords.clear(),null!==this._additionsTail&&(this._additionsTail._nextAdded=null),null!==this._movesTail&&(this._movesTail._nextMoved=null),null!==this._itTail&&(this._itTail._next=null),null!==this._removalsTail&&(this._removalsTail._nextRemoved=null),null!==this._identityChangesTail&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){null!==this._unlinkedRecords&&this._unlinkedRecords.remove(t);const o=t._prevRemoved,i=t._nextRemoved;return null===o?this._removalsHead=i:o._nextRemoved=i,null===i?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail=null===this._additionsTail?this._additionsHead=t:this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){const o=null===n?this._itHead:n._next;return t._next=o,t._prev=n,null===o?this._itTail=t:o._prev=t,null===n?this._itHead=t:n._next=t,null===this._linkedRecords&&(this._linkedRecords=new aD),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){null!==this._linkedRecords&&this._linkedRecords.remove(t);const n=t._prev,r=t._next;return null===n?this._itHead=r:n._next=r,null===r?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail=null===this._movesTail?this._movesHead=t:this._movesTail._nextMoved=t),t}_addToRemovals(t){return null===this._unlinkedRecords&&(this._unlinkedRecords=new aD),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,null===this._removalsTail?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail=null===this._identityChangesTail?this._identityChangesHead=t:this._identityChangesTail._nextIdentityChange=t,t}}class NR{constructor(t,n){this.item=t,this.trackById=n,this.currentIndex=null,this.previousIndex=null,this._nextPrevious=null,this._prev=null,this._next=null,this._prevDup=null,this._nextDup=null,this._prevRemoved=null,this._nextRemoved=null,this._nextAdded=null,this._nextMoved=null,this._nextIdentityChange=null}}class PR{constructor(){this._head=null,this._tail=null}add(t){null===this._head?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;null!==r;r=r._nextDup)if((null===n||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){const n=t._prevDup,r=t._nextDup;return null===n?this._head=r:n._nextDup=r,null===r?this._tail=n:r._prevDup=n,null===this._head}}class aD{constructor(){this.map=new Map}put(t){const n=t.trackById;let r=this.map.get(n);r||(r=new PR,this.map.set(n,r)),r.add(t)}get(t,n){const o=this.map.get(t);return o?o.get(t,n):null}remove(t){const n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return 0===this.map.size}clear(){this.map.clear()}}function lD(e,t,n){const r=e.previousIndex;if(null===r)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+t+o}class cD{constructor(){}supports(t){return t instanceof Map||sd(t)}create(){return new RR}}class RR{constructor(){this._records=new Map,this._mapHead=null,this._appendAfter=null,this._previousMapHead=null,this._changesHead=null,this._changesTail=null,this._additionsHead=null,this._additionsTail=null,this._removalsHead=null,this._removalsTail=null}get isDirty(){return null!==this._additionsHead||null!==this._changesHead||null!==this._removalsHead}forEachItem(t){let n;for(n=this._mapHead;null!==n;n=n._next)t(n)}forEachPreviousItem(t){let n;for(n=this._previousMapHead;null!==n;n=n._nextPrevious)t(n)}forEachChangedItem(t){let n;for(n=this._changesHead;null!==n;n=n._nextChanged)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;null!==n;n=n._nextAdded)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;null!==n;n=n._nextRemoved)t(n)}diff(t){if(t){if(!(t instanceof Map||sd(t)))throw new C(900,!1)}else t=new Map;return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._mapHead;if(this._appendAfter=null,this._forEach(t,(r,o)=>{if(n&&n.key===o)this._maybeAddToChanges(n,r),this._appendAfter=n,n=n._next;else{const i=this._getOrCreateRecordForKey(o,r);n=this._insertBeforeOrAppend(n,i)}}),n){n._prev&&(n._prev._next=null),this._removalsHead=n;for(let r=n;null!==r;r=r._nextRemoved)r===this._mapHead&&(this._mapHead=null),this._records.delete(r.key),r._nextRemoved=r._next,r.previousValue=r.currentValue,r.currentValue=null,r._prev=null,r._next=null}return this._changesTail&&(this._changesTail._nextChanged=null),this._additionsTail&&(this._additionsTail._nextAdded=null),this.isDirty}_insertBeforeOrAppend(t,n){if(t){const r=t._prev;return n._next=t,n._prev=r,t._prev=n,r&&(r._next=n),t===this._mapHead&&(this._mapHead=n),this._appendAfter=t,t}return this._appendAfter?(this._appendAfter._next=n,n._prev=this._appendAfter):this._mapHead=n,this._appendAfter=n,null}_getOrCreateRecordForKey(t,n){if(this._records.has(t)){const o=this._records.get(t);this._maybeAddToChanges(o,n);const i=o._prev,s=o._next;return i&&(i._next=s),s&&(s._prev=i),o._next=null,o._prev=null,o}const r=new kR(t);return this._records.set(t,r),r.currentValue=n,this._addToAdditions(r),r}_reset(){if(this.isDirty){let t;for(this._previousMapHead=this._mapHead,t=this._previousMapHead;null!==t;t=t._next)t._nextPrevious=t._next;for(t=this._changesHead;null!==t;t=t._nextChanged)t.previousValue=t.currentValue;for(t=this._additionsHead;null!=t;t=t._nextAdded)t.previousValue=t.currentValue;this._changesHead=this._changesTail=null,this._additionsHead=this._additionsTail=null,this._removalsHead=null}}_maybeAddToChanges(t,n){Object.is(n,t.currentValue)||(t.previousValue=t.currentValue,t.currentValue=n,this._addToChanges(t))}_addToAdditions(t){null===this._additionsHead?this._additionsHead=this._additionsTail=t:(this._additionsTail._nextAdded=t,this._additionsTail=t)}_addToChanges(t){null===this._changesHead?this._changesHead=this._changesTail=t:(this._changesTail._nextChanged=t,this._changesTail=t)}_forEach(t,n){t instanceof Map?t.forEach(n):Object.keys(t).forEach(r=>n(t[r],r))}}class kR{constructor(t){this.key=t,this.previousValue=null,this.currentValue=null,this._nextPrevious=null,this._next=null,this._prev=null,this._nextAdded=null,this._nextRemoved=null,this._nextChanged=null}}function uD(){return new el([new sD])}let el=(()=>{class e{static#e=this.\u0275prov=x({token:e,providedIn:"root",factory:uD});constructor(n){this.factories=n}static create(n,r){if(null!=r){const o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||uD()),deps:[[e,new Js,new Xs]]}}find(n){const r=this.factories.find(o=>o.supports(n));if(null!=r)return r;throw new C(901,!1)}}return e})();function dD(){return new Hi([new cD])}let Hi=(()=>{class e{static#e=this.\u0275prov=x({token:e,providedIn:"root",factory:dD});constructor(n){this.factories=n}static create(n,r){if(r){const o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||dD()),deps:[[e,new Js,new Xs]]}}find(n){const r=this.factories.find(o=>o.supports(n));if(r)return r;throw new C(901,!1)}}return e})();const VR=ZC(null,"core",[]);let jR=(()=>{class e{constructor(n){}static#e=this.\u0275fac=function(r){return new(r||e)(I(bo))};static#t=this.\u0275mod=wt({type:e});static#n=this.\u0275inj=ct({})}return e})();let cf=null;function Un(){return cf}class KR{}const vt=new M("DocumentToken");let uf=(()=>{class e{historyGo(n){throw new Error("Not implemented")}static#e=this.\u0275fac=function(r){return new(r||e)};static#t=this.\u0275prov=x({token:e,factory:function(){return E(tk)},providedIn:"platform"})}return e})();const ek=new M("Location Initialized");let tk=(()=>{class e extends uf{constructor(){super(),this._doc=E(vt),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return Un().getBaseHref(this._doc)}onPopState(n){const r=Un().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){const r=Un().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,o){this._history.pushState(n,r,o)}replaceState(n,r,o){this._history.replaceState(n,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static#e=this.\u0275fac=function(r){return new(r||e)};static#t=this.\u0275prov=x({token:e,factory:function(){return new e},providedIn:"platform"})}return e})();function df(e,t){if(0==e.length)return t;if(0==t.length)return e;let n=0;return e.endsWith("/")&&n++,t.startsWith("/")&&n++,2==n?e+t.substring(1):1==n?e+t:e+"/"+t}function CD(e){const t=e.match(/#|\?|$/),n=t&&t.index||e.length;return e.slice(0,n-("/"===e[n-1]?1:0))+e.slice(n)}function bn(e){return e&&"?"!==e[0]?"?"+e:e}let mr=(()=>{class e{historyGo(n){throw new Error("Not implemented")}static#e=this.\u0275fac=function(r){return new(r||e)};static#t=this.\u0275prov=x({token:e,factory:function(){return E(wD)},providedIn:"root"})}return e})();const DD=new M("appBaseHref");let wD=(()=>{class e extends mr{constructor(n,r){super(),this._platformLocation=n,this._removeListenerFns=[],this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??E(vt).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return df(this._baseHref,n)}path(n=!1){const r=this._platformLocation.pathname+bn(this._platformLocation.search),o=this._platformLocation.hash;return o&&n?`${r}${o}`:r}pushState(n,r,o,i){const s=this.prepareExternalUrl(o+bn(i));this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){const s=this.prepareExternalUrl(o+bn(i));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static#e=this.\u0275fac=function(r){return new(r||e)(I(uf),I(DD,8))};static#t=this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),nk=(()=>{class e extends mr{constructor(n,r){super(),this._platformLocation=n,this._baseHref="",this._removeListenerFns=[],null!=r&&(this._baseHref=r)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}path(n=!1){let r=this._platformLocation.hash;return null==r&&(r="#"),r.length>0?r.substring(1):r}prepareExternalUrl(n){const r=df(this._baseHref,n);return r.length>0?"#"+r:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+bn(i));0==s.length&&(s=this._platformLocation.pathname),this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+bn(i));0==s.length&&(s=this._platformLocation.pathname),this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static#e=this.\u0275fac=function(r){return new(r||e)(I(uf),I(DD,8))};static#t=this.\u0275prov=x({token:e,factory:e.\u0275fac})}return e})(),ff=(()=>{class e{constructor(n){this._subject=new ge,this._urlChangeListeners=[],this._urlChangeSubscription=null,this._locationStrategy=n;const r=this._locationStrategy.getBaseHref();this._basePath=function ik(e){if(new RegExp("^(https?:)?//").test(e)){const[,n]=e.split(/\/\/[^\/]+/);return n}return e}(CD(bD(r))),this._locationStrategy.onPopState(o=>{this._subject.emit({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+bn(r))}normalize(n){return e.stripTrailingSlash(function ok(e,t){if(!e||!t.startsWith(e))return t;const n=t.substring(e.length);return""===n||["/",";","?","#"].includes(n[0])?n:t}(this._basePath,bD(n)))}prepareExternalUrl(n){return n&&"/"!==n[0]&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",o=null){this._locationStrategy.pushState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+bn(r)),o)}replaceState(n,r="",o=null){this._locationStrategy.replaceState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+bn(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription||(this._urlChangeSubscription=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)})),()=>{const r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),0===this._urlChangeListeners.length&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(o=>o(n,r))}subscribe(n,r,o){return this._subject.subscribe({next:n,error:r,complete:o})}static#e=this.normalizeQueryParams=bn;static#t=this.joinWithSlash=df;static#n=this.stripTrailingSlash=CD;static#r=this.\u0275fac=function(r){return new(r||e)(I(mr))};static#o=this.\u0275prov=x({token:e,factory:function(){return function rk(){return new ff(I(mr))}()},providedIn:"root"})}return e})();function bD(e){return e.replace(/\/index.html$/,"")}var Qe=function(e){return e[e.Format=0]="Format",e[e.Standalone=1]="Standalone",e}(Qe||{}),te=function(e){return e[e.Narrow=0]="Narrow",e[e.Abbreviated=1]="Abbreviated",e[e.Wide=2]="Wide",e[e.Short=3]="Short",e}(te||{}),_t=function(e){return e[e.Short=0]="Short",e[e.Medium=1]="Medium",e[e.Long=2]="Long",e[e.Full=3]="Full",e}(_t||{}),Ce=function(e){return e[e.Decimal=0]="Decimal",e[e.Group=1]="Group",e[e.List=2]="List",e[e.PercentSign=3]="PercentSign",e[e.PlusSign=4]="PlusSign",e[e.MinusSign=5]="MinusSign",e[e.Exponential=6]="Exponential",e[e.SuperscriptingExponent=7]="SuperscriptingExponent",e[e.PerMille=8]="PerMille",e[e.Infinity=9]="Infinity",e[e.NaN=10]="NaN",e[e.TimeSeparator=11]="TimeSeparator",e[e.CurrencyDecimal=12]="CurrencyDecimal",e[e.CurrencyGroup=13]="CurrencyGroup",e}(Ce||{});function rl(e,t){return xt(Ze(e)[ae.DateFormat],t)}function ol(e,t){return xt(Ze(e)[ae.TimeFormat],t)}function il(e,t){return xt(Ze(e)[ae.DateTimeFormat],t)}function St(e,t){const n=Ze(e),r=n[ae.NumberSymbols][t];if(typeof r>"u"){if(t===Ce.CurrencyDecimal)return n[ae.NumberSymbols][Ce.Decimal];if(t===Ce.CurrencyGroup)return n[ae.NumberSymbols][Ce.Group]}return r}function MD(e){if(!e[ae.ExtraData])throw new Error(`Missing extra locale data for the locale "${e[ae.LocaleId]}". Use "registerLocaleData" to load new data. See the "I18n guide" on angular.io to know more.`)}function xt(e,t){for(let n=t;n>-1;n--)if(typeof e[n]<"u")return e[n];throw new Error("Locale data API: locale data undefined")}function pf(e){const[t,n]=e.split(":");return{hours:+t,minutes:+n}}const _k=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,Ui={},Ck=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/;var En=function(e){return e[e.Short=0]="Short",e[e.ShortGMT=1]="ShortGMT",e[e.Long=2]="Long",e[e.Extended=3]="Extended",e}(En||{}),q=function(e){return e[e.FullYear=0]="FullYear",e[e.Month=1]="Month",e[e.Date=2]="Date",e[e.Hours=3]="Hours",e[e.Minutes=4]="Minutes",e[e.Seconds=5]="Seconds",e[e.FractionalSeconds=6]="FractionalSeconds",e[e.Day=7]="Day",e}(q||{}),W=function(e){return e[e.DayPeriods=0]="DayPeriods",e[e.Days=1]="Days",e[e.Months=2]="Months",e[e.Eras=3]="Eras",e}(W||{});function Dk(e,t,n,r){let o=function Tk(e){if(xD(e))return e;if("number"==typeof e&&!isNaN(e))return new Date(e);if("string"==typeof e){if(e=e.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(e)){const[o,i=1,s=1]=e.split("-").map(a=>+a);return sl(o,i-1,s)}const n=parseFloat(e);if(!isNaN(e-n))return new Date(n);let r;if(r=e.match(_k))return function Ok(e){const t=new Date(0);let n=0,r=0;const o=e[8]?t.setUTCFullYear:t.setFullYear,i=e[8]?t.setUTCHours:t.setHours;e[9]&&(n=Number(e[9]+e[10]),r=Number(e[9]+e[11])),o.call(t,Number(e[1]),Number(e[2])-1,Number(e[3]));const s=Number(e[4]||0)-n,a=Number(e[5]||0)-r,l=Number(e[6]||0),c=Math.floor(1e3*parseFloat("0."+(e[7]||0)));return i.call(t,s,a,l,c),t}(r)}const t=new Date(e);if(!xD(t))throw new Error(`Unable to convert "${e}" into a date`);return t}(e);t=Mn(n,t)||t;let a,s=[];for(;t;){if(a=Ck.exec(t),!a){s.push(t);break}{s=s.concat(a.slice(1));const u=s.pop();if(!u)break;t=u}}let l=o.getTimezoneOffset();r&&(l=SD(r,l),o=function Ak(e,t,n){const r=n?-1:1,o=e.getTimezoneOffset();return function xk(e,t){return(e=new Date(e.getTime())).setMinutes(e.getMinutes()+t),e}(e,r*(SD(t,o)-o))}(o,r,!0));let c="";return s.forEach(u=>{const d=function Sk(e){if(mf[e])return mf[e];let t;switch(e){case"G":case"GG":case"GGG":t=le(W.Eras,te.Abbreviated);break;case"GGGG":t=le(W.Eras,te.Wide);break;case"GGGGG":t=le(W.Eras,te.Narrow);break;case"y":t=be(q.FullYear,1,0,!1,!0);break;case"yy":t=be(q.FullYear,2,0,!0,!0);break;case"yyy":t=be(q.FullYear,3,0,!1,!0);break;case"yyyy":t=be(q.FullYear,4,0,!1,!0);break;case"Y":t=ul(1);break;case"YY":t=ul(2,!0);break;case"YYY":t=ul(3);break;case"YYYY":t=ul(4);break;case"M":case"L":t=be(q.Month,1,1);break;case"MM":case"LL":t=be(q.Month,2,1);break;case"MMM":t=le(W.Months,te.Abbreviated);break;case"MMMM":t=le(W.Months,te.Wide);break;case"MMMMM":t=le(W.Months,te.Narrow);break;case"LLL":t=le(W.Months,te.Abbreviated,Qe.Standalone);break;case"LLLL":t=le(W.Months,te.Wide,Qe.Standalone);break;case"LLLLL":t=le(W.Months,te.Narrow,Qe.Standalone);break;case"w":t=gf(1);break;case"ww":t=gf(2);break;case"W":t=gf(1,!0);break;case"d":t=be(q.Date,1);break;case"dd":t=be(q.Date,2);break;case"c":case"cc":t=be(q.Day,1);break;case"ccc":t=le(W.Days,te.Abbreviated,Qe.Standalone);break;case"cccc":t=le(W.Days,te.Wide,Qe.Standalone);break;case"ccccc":t=le(W.Days,te.Narrow,Qe.Standalone);break;case"cccccc":t=le(W.Days,te.Short,Qe.Standalone);break;case"E":case"EE":case"EEE":t=le(W.Days,te.Abbreviated);break;case"EEEE":t=le(W.Days,te.Wide);break;case"EEEEE":t=le(W.Days,te.Narrow);break;case"EEEEEE":t=le(W.Days,te.Short);break;case"a":case"aa":case"aaa":t=le(W.DayPeriods,te.Abbreviated);break;case"aaaa":t=le(W.DayPeriods,te.Wide);break;case"aaaaa":t=le(W.DayPeriods,te.Narrow);break;case"b":case"bb":case"bbb":t=le(W.DayPeriods,te.Abbreviated,Qe.Standalone,!0);break;case"bbbb":t=le(W.DayPeriods,te.Wide,Qe.Standalone,!0);break;case"bbbbb":t=le(W.DayPeriods,te.Narrow,Qe.Standalone,!0);break;case"B":case"BB":case"BBB":t=le(W.DayPeriods,te.Abbreviated,Qe.Format,!0);break;case"BBBB":t=le(W.DayPeriods,te.Wide,Qe.Format,!0);break;case"BBBBB":t=le(W.DayPeriods,te.Narrow,Qe.Format,!0);break;case"h":t=be(q.Hours,1,-12);break;case"hh":t=be(q.Hours,2,-12);break;case"H":t=be(q.Hours,1);break;case"HH":t=be(q.Hours,2);break;case"m":t=be(q.Minutes,1);break;case"mm":t=be(q.Minutes,2);break;case"s":t=be(q.Seconds,1);break;case"ss":t=be(q.Seconds,2);break;case"S":t=be(q.FractionalSeconds,1);break;case"SS":t=be(q.FractionalSeconds,2);break;case"SSS":t=be(q.FractionalSeconds,3);break;case"Z":case"ZZ":case"ZZZ":t=ll(En.Short);break;case"ZZZZZ":t=ll(En.Extended);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":t=ll(En.ShortGMT);break;case"OOOO":case"ZZZZ":case"zzzz":t=ll(En.Long);break;default:return null}return mf[e]=t,t}(u);c+=d?d(o,n,l):"''"===u?"'":u.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),c}function sl(e,t,n){const r=new Date(0);return r.setFullYear(e,t,n),r.setHours(0,0,0),r}function Mn(e,t){const n=function ak(e){return Ze(e)[ae.LocaleId]}(e);if(Ui[n]=Ui[n]||{},Ui[n][t])return Ui[n][t];let r="";switch(t){case"shortDate":r=rl(e,_t.Short);break;case"mediumDate":r=rl(e,_t.Medium);break;case"longDate":r=rl(e,_t.Long);break;case"fullDate":r=rl(e,_t.Full);break;case"shortTime":r=ol(e,_t.Short);break;case"mediumTime":r=ol(e,_t.Medium);break;case"longTime":r=ol(e,_t.Long);break;case"fullTime":r=ol(e,_t.Full);break;case"short":const o=Mn(e,"shortTime"),i=Mn(e,"shortDate");r=al(il(e,_t.Short),[o,i]);break;case"medium":const s=Mn(e,"mediumTime"),a=Mn(e,"mediumDate");r=al(il(e,_t.Medium),[s,a]);break;case"long":const l=Mn(e,"longTime"),c=Mn(e,"longDate");r=al(il(e,_t.Long),[l,c]);break;case"full":const u=Mn(e,"fullTime"),d=Mn(e,"fullDate");r=al(il(e,_t.Full),[u,d])}return r&&(Ui[n][t]=r),r}function al(e,t){return t&&(e=e.replace(/\{([^}]+)}/g,function(n,r){return null!=t&&r in t?t[r]:n})),e}function Ht(e,t,n="-",r,o){let i="";(e<0||o&&e<=0)&&(o?e=1-e:(e=-e,i=n));let s=String(e);for(;s.length<t;)s="0"+s;return r&&(s=s.slice(s.length-t)),i+s}function be(e,t,n=0,r=!1,o=!1){return function(i,s){let a=function bk(e,t){switch(e){case q.FullYear:return t.getFullYear();case q.Month:return t.getMonth();case q.Date:return t.getDate();case q.Hours:return t.getHours();case q.Minutes:return t.getMinutes();case q.Seconds:return t.getSeconds();case q.FractionalSeconds:return t.getMilliseconds();case q.Day:return t.getDay();default:throw new Error(`Unknown DateType value "${e}".`)}}(e,i);if((n>0||a>-n)&&(a+=n),e===q.Hours)0===a&&-12===n&&(a=12);else if(e===q.FractionalSeconds)return function wk(e,t){return Ht(e,3).substring(0,t)}(a,t);const l=St(s,Ce.MinusSign);return Ht(a,t,l,r,o)}}function le(e,t,n=Qe.Format,r=!1){return function(o,i){return function Ek(e,t,n,r,o,i){switch(n){case W.Months:return function uk(e,t,n){const r=Ze(e),i=xt([r[ae.MonthsFormat],r[ae.MonthsStandalone]],t);return xt(i,n)}(t,o,r)[e.getMonth()];case W.Days:return function ck(e,t,n){const r=Ze(e),i=xt([r[ae.DaysFormat],r[ae.DaysStandalone]],t);return xt(i,n)}(t,o,r)[e.getDay()];case W.DayPeriods:const s=e.getHours(),a=e.getMinutes();if(i){const c=function pk(e){const t=Ze(e);return MD(t),(t[ae.ExtraData][2]||[]).map(r=>"string"==typeof r?pf(r):[pf(r[0]),pf(r[1])])}(t),u=function gk(e,t,n){const r=Ze(e);MD(r);const i=xt([r[ae.ExtraData][0],r[ae.ExtraData][1]],t)||[];return xt(i,n)||[]}(t,o,r),d=c.findIndex(f=>{if(Array.isArray(f)){const[h,p]=f,g=s>=h.hours&&a>=h.minutes,y=s<p.hours||s===p.hours&&a<p.minutes;if(h.hours<p.hours){if(g&&y)return!0}else if(g||y)return!0}else if(f.hours===s&&f.minutes===a)return!0;return!1});if(-1!==d)return u[d]}return function lk(e,t,n){const r=Ze(e),i=xt([r[ae.DayPeriodsFormat],r[ae.DayPeriodsStandalone]],t);return xt(i,n)}(t,o,r)[s<12?0:1];case W.Eras:return function dk(e,t){return xt(Ze(e)[ae.Eras],t)}(t,r)[e.getFullYear()<=0?0:1];default:throw new Error(`unexpected translation type ${n}`)}}(o,i,e,t,n,r)}}function ll(e){return function(t,n,r){const o=-1*r,i=St(n,Ce.MinusSign),s=o>0?Math.floor(o/60):Math.ceil(o/60);switch(e){case En.Short:return(o>=0?"+":"")+Ht(s,2,i)+Ht(Math.abs(o%60),2,i);case En.ShortGMT:return"GMT"+(o>=0?"+":"")+Ht(s,1,i);case En.Long:return"GMT"+(o>=0?"+":"")+Ht(s,2,i)+":"+Ht(Math.abs(o%60),2,i);case En.Extended:return 0===r?"Z":(o>=0?"+":"")+Ht(s,2,i)+":"+Ht(Math.abs(o%60),2,i);default:throw new Error(`Unknown zone width "${e}"`)}}}const Mk=0,cl=4;function ID(e){return sl(e.getFullYear(),e.getMonth(),e.getDate()+(cl-e.getDay()))}function gf(e,t=!1){return function(n,r){let o;if(t){const i=new Date(n.getFullYear(),n.getMonth(),1).getDay()-1,s=n.getDate();o=1+Math.floor((s+i)/7)}else{const i=ID(n),s=function Ik(e){const t=sl(e,Mk,1).getDay();return sl(e,0,1+(t<=cl?cl:cl+7)-t)}(i.getFullYear()),a=i.getTime()-s.getTime();o=1+Math.round(a/6048e5)}return Ht(o,e,St(r,Ce.MinusSign))}}function ul(e,t=!1){return function(n,r){return Ht(ID(n).getFullYear(),e,St(r,Ce.MinusSign),t)}}const mf={};function SD(e,t){e=e.replace(/:/g,"");const n=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return isNaN(n)?t:n}function xD(e){return e instanceof Date&&!isNaN(e.valueOf())}function ND(e,t){t=encodeURIComponent(t);for(const n of e.split(";")){const r=n.indexOf("="),[o,i]=-1==r?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}const wf=/\s+/,PD=[];let bf=(()=>{class e{constructor(n,r,o,i){this._iterableDiffers=n,this._keyValueDiffers=r,this._ngEl=o,this._renderer=i,this.initialClasses=PD,this.stateMap=new Map}set klass(n){this.initialClasses=null!=n?n.trim().split(wf):PD}set ngClass(n){this.rawClass="string"==typeof n?n.trim().split(wf):n}ngDoCheck(){for(const r of this.initialClasses)this._updateState(r,!0);const n=this.rawClass;if(Array.isArray(n)||n instanceof Set)for(const r of n)this._updateState(r,!0);else if(null!=n)for(const r of Object.keys(n))this._updateState(r,!!n[r]);this._applyStateDiff()}_updateState(n,r){const o=this.stateMap.get(n);void 0!==o?(o.enabled!==r&&(o.changed=!0,o.enabled=r),o.touched=!0):this.stateMap.set(n,{enabled:r,changed:!0,touched:!0})}_applyStateDiff(){for(const n of this.stateMap){const r=n[0],o=n[1];o.changed?(this._toggleClass(r,o.enabled),o.changed=!1):o.touched||(o.enabled&&this._toggleClass(r,!1),this.stateMap.delete(r)),o.touched=!1}}_toggleClass(n,r){(n=n.trim()).length>0&&n.split(wf).forEach(o=>{r?this._renderer.addClass(this._ngEl.nativeElement,o):this._renderer.removeClass(this._ngEl.nativeElement,o)})}static#e=this.\u0275fac=function(r){return new(r||e)(D(el),D(Hi),D(pt),D(mn))};static#t=this.\u0275dir=P({type:e,selectors:[["","ngClass",""]],inputs:{klass:["class","klass"],ngClass:"ngClass"},standalone:!0})}return e})();class zk{constructor(t,n,r,o){this.$implicit=t,this.ngForOf=n,this.index=r,this.count=o}get first(){return 0===this.index}get last(){return this.index===this.count-1}get even(){return this.index%2==0}get odd(){return!this.even}}let Ef=(()=>{class e{set ngForOf(n){this._ngForOf=n,this._ngForOfDirty=!0}set ngForTrackBy(n){this._trackByFn=n}get ngForTrackBy(){return this._trackByFn}constructor(n,r,o){this._viewContainer=n,this._template=r,this._differs=o,this._ngForOf=null,this._ngForOfDirty=!0,this._differ=null}set ngForTemplate(n){n&&(this._template=n)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;const n=this._ngForOf;!this._differ&&n&&(this._differ=this._differs.find(n).create(this.ngForTrackBy))}if(this._differ){const n=this._differ.diff(this._ngForOf);n&&this._applyChanges(n)}}_applyChanges(n){const r=this._viewContainer;n.forEachOperation((o,i,s)=>{if(null==o.previousIndex)r.createEmbeddedView(this._template,new zk(o.item,this._ngForOf,-1,-1),null===s?void 0:s);else if(null==s)r.remove(null===i?void 0:i);else if(null!==i){const a=r.get(i);r.move(a,s),kD(a,o)}});for(let o=0,i=r.length;o<i;o++){const a=r.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}n.forEachIdentityChange(o=>{kD(r.get(o.currentIndex),o)})}static ngTemplateContextGuard(n,r){return!0}static#e=this.\u0275fac=function(r){return new(r||e)(D(Bt),D(Dn),D(el))};static#t=this.\u0275dir=P({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"},standalone:!0})}return e})();function kD(e,t){e.context.$implicit=t.item}let Mf=(()=>{class e{constructor(n,r){this._viewContainer=n,this._context=new Gk,this._thenTemplateRef=null,this._elseTemplateRef=null,this._thenViewRef=null,this._elseViewRef=null,this._thenTemplateRef=r}set ngIf(n){this._context.$implicit=this._context.ngIf=n,this._updateView()}set ngIfThen(n){FD("ngIfThen",n),this._thenTemplateRef=n,this._thenViewRef=null,this._updateView()}set ngIfElse(n){FD("ngIfElse",n),this._elseTemplateRef=n,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngTemplateContextGuard(n,r){return!0}static#e=this.\u0275fac=function(r){return new(r||e)(D(Bt),D(Dn))};static#t=this.\u0275dir=P({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"},standalone:!0})}return e})();class Gk{constructor(){this.$implicit=null,this.ngIf=null}}function FD(e,t){if(t&&!t.createEmbeddedView)throw new Error(`${e} must be a TemplateRef, but received '${Ie(t)}'.`)}function Ut(e,t){return new C(2100,!1)}const e1=/(?:[0-9A-Za-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16F1-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF40\uDF42-\uDF49\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDD70-\uDD7A\uDD7C-\uDD8A\uDD8C-\uDD92\uDD94\uDD95\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDD00-\uDD23\uDE80-\uDEA9\uDEB0\uDEB1\uDF00-\uDF1C\uDF27\uDF30-\uDF45\uDF70-\uDF81\uDFB0-\uDFC4\uDFE0-\uDFF6]|\uD804[\uDC03-\uDC37\uDC71\uDC72\uDC75\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD44\uDD47\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC5F-\uDC61\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDEB8\uDF00-\uDF1A\uDF40-\uDF46]|\uD806[\uDC00-\uDC2B\uDCA0-\uDCDF\uDCFF-\uDD06\uDD09\uDD0C-\uDD13\uDD15\uDD16\uDD18-\uDD2F\uDD3F\uDD41\uDDA0-\uDDA7\uDDAA-\uDDD0\uDDE1\uDDE3\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE89\uDE9D\uDEB0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD89\uDD98\uDEE0-\uDEF2\uDFB0]|\uD808[\uDC00-\uDF99]|\uD809[\uDC80-\uDD43]|\uD80B[\uDF90-\uDFF0]|[\uD80C\uD81C-\uD820\uD822\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE70-\uDEBE\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE7F\uDF00-\uDF4A\uDF50\uDF93-\uDF9F\uDFE0\uDFE1\uDFE3]|\uD821[\uDC00-\uDFF7]|\uD823[\uDC00-\uDCD5\uDD00-\uDD08]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00-\uDD22\uDD50-\uDD52\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD837[\uDF00-\uDF1E]|\uD838[\uDD00-\uDD2C\uDD37-\uDD3D\uDD4E\uDE90-\uDEAD\uDEC0-\uDEEB]|\uD839[\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43\uDD4B]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF38\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A])\S*/g;let VD=(()=>{class e{transform(n){if(null==n)return null;if("string"!=typeof n)throw Ut();return n.replace(e1,r=>r[0].toUpperCase()+r.slice(1).toLowerCase())}static#e=this.\u0275fac=function(r){return new(r||e)};static#t=this.\u0275pipe=et({name:"titlecase",type:e,pure:!0,standalone:!0})}return e})();const r1=new M("DATE_PIPE_DEFAULT_TIMEZONE"),o1=new M("DATE_PIPE_DEFAULT_OPTIONS");let xf=(()=>{class e{constructor(n,r,o){this.locale=n,this.defaultTimezone=r,this.defaultOptions=o}transform(n,r,o,i){if(null==n||""===n||n!=n)return null;try{return Dk(n,r??this.defaultOptions?.dateFormat??"mediumDate",i||this.locale,o??this.defaultOptions?.timezone??this.defaultTimezone??void 0)}catch(s){throw Ut()}}static#e=this.\u0275fac=function(r){return new(r||e)(D(wn,16),D(r1,24),D(o1,24))};static#t=this.\u0275pipe=et({name:"date",type:e,pure:!0,standalone:!0})}return e})(),p1=(()=>{class e{static#e=this.\u0275fac=function(r){return new(r||e)};static#t=this.\u0275mod=wt({type:e});static#n=this.\u0275inj=ct({})}return e})();function BD(e){return"server"===e}let v1=(()=>{class e{static#e=this.\u0275prov=x({token:e,providedIn:"root",factory:()=>new _1(I(vt),window)})}return e})();class _1{constructor(t,n){this.document=t,this.window=n,this.offset=()=>[0,0]}setOffset(t){this.offset=Array.isArray(t)?()=>t:t}getScrollPosition(){return this.supportsScrolling()?[this.window.pageXOffset,this.window.pageYOffset]:[0,0]}scrollToPosition(t){this.supportsScrolling()&&this.window.scrollTo(t[0],t[1])}scrollToAnchor(t){if(!this.supportsScrolling())return;const n=function C1(e,t){const n=e.getElementById(t)||e.getElementsByName(t)[0];if(n)return n;if("function"==typeof e.createTreeWalker&&e.body&&"function"==typeof e.body.attachShadow){const r=e.createTreeWalker(e.body,NodeFilter.SHOW_ELEMENT);let o=r.currentNode;for(;o;){const i=o.shadowRoot;if(i){const s=i.getElementById(t)||i.querySelector(`[name="${t}"]`);if(s)return s}o=r.nextNode()}}return null}(this.document,t);n&&(this.scrollToElement(n),n.focus())}setHistoryScrollRestoration(t){this.supportsScrolling()&&(this.window.history.scrollRestoration=t)}scrollToElement(t){const n=t.getBoundingClientRect(),r=n.left+this.window.pageXOffset,o=n.top+this.window.pageYOffset,i=this.offset();this.window.scrollTo(r-i[0],o-i[1])}supportsScrolling(){try{return!!this.window&&!!this.window.scrollTo&&"pageXOffset"in this.window}catch{return!1}}}class HD{}class U1 extends KR{constructor(){super(...arguments),this.supportsDOMEvents=!0}}class Pf extends U1{static makeCurrent(){!function JR(e){cf||(cf=e)}(new Pf)}onAndCancel(t,n,r){return t.addEventListener(n,r),()=>{t.removeEventListener(n,r)}}dispatchEvent(t,n){t.dispatchEvent(n)}remove(t){t.parentNode&&t.parentNode.removeChild(t)}createElement(t,n){return(n=n||this.getDefaultDocument()).createElement(t)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(t){return t.nodeType===Node.ELEMENT_NODE}isShadowRoot(t){return t instanceof DocumentFragment}getGlobalEventTarget(t,n){return"window"===n?window:"document"===n?t:"body"===n?t.body:null}getBaseHref(t){const n=function z1(){return qi=qi||document.querySelector("base"),qi?qi.getAttribute("href"):null}();return null==n?null:function G1(e){pl=pl||document.createElement("a"),pl.setAttribute("href",e);const t=pl.pathname;return"/"===t.charAt(0)?t:`/${t}`}(n)}resetBaseElement(){qi=null}getUserAgent(){return window.navigator.userAgent}getCookie(t){return ND(document.cookie,t)}}let pl,qi=null,W1=(()=>{class e{build(){return new XMLHttpRequest}static#e=this.\u0275fac=function(r){return new(r||e)};static#t=this.\u0275prov=x({token:e,factory:e.\u0275fac})}return e})();const Rf=new M("EventManagerPlugins");let WD=(()=>{class e{constructor(n,r){this._zone=r,this._eventNameToPlugin=new Map,n.forEach(o=>{o.manager=this}),this._plugins=n.slice().reverse()}addEventListener(n,r,o){return this._findPluginFor(r).addEventListener(n,r,o)}getZone(){return this._zone}_findPluginFor(n){let r=this._eventNameToPlugin.get(n);if(r)return r;if(r=this._plugins.find(i=>i.supports(n)),!r)throw new C(5101,!1);return this._eventNameToPlugin.set(n,r),r}static#e=this.\u0275fac=function(r){return new(r||e)(I(Rf),I(ie))};static#t=this.\u0275prov=x({token:e,factory:e.\u0275fac})}return e})();class ZD{constructor(t){this._doc=t}}const kf="ng-app-id";let YD=(()=>{class e{constructor(n,r,o,i={}){this.doc=n,this.appId=r,this.nonce=o,this.platformId=i,this.styleRef=new Map,this.hostNodes=new Set,this.styleNodesInDOM=this.collectServerRenderedStyles(),this.platformIsServer=BD(i),this.resetHostNodes()}addStyles(n){for(const r of n)1===this.changeUsageCount(r,1)&&this.onStyleAdded(r)}removeStyles(n){for(const r of n)this.changeUsageCount(r,-1)<=0&&this.onStyleRemoved(r)}ngOnDestroy(){const n=this.styleNodesInDOM;n&&(n.forEach(r=>r.remove()),n.clear());for(const r of this.getAllStyles())this.onStyleRemoved(r);this.resetHostNodes()}addHost(n){this.hostNodes.add(n);for(const r of this.getAllStyles())this.addStyleToHost(n,r)}removeHost(n){this.hostNodes.delete(n)}getAllStyles(){return this.styleRef.keys()}onStyleAdded(n){for(const r of this.hostNodes)this.addStyleToHost(r,n)}onStyleRemoved(n){const r=this.styleRef;r.get(n)?.elements?.forEach(o=>o.remove()),r.delete(n)}collectServerRenderedStyles(){const n=this.doc.head?.querySelectorAll(`style[${kf}="${this.appId}"]`);if(n?.length){const r=new Map;return n.forEach(o=>{null!=o.textContent&&r.set(o.textContent,o)}),r}return null}changeUsageCount(n,r){const o=this.styleRef;if(o.has(n)){const i=o.get(n);return i.usage+=r,i.usage}return o.set(n,{usage:r,elements:[]}),r}getStyleElement(n,r){const o=this.styleNodesInDOM,i=o?.get(r);if(i?.parentNode===n)return o.delete(r),i.removeAttribute(kf),i;{const s=this.doc.createElement("style");return this.nonce&&s.setAttribute("nonce",this.nonce),s.textContent=r,this.platformIsServer&&s.setAttribute(kf,this.appId),s}}addStyleToHost(n,r){const o=this.getStyleElement(n,r);n.appendChild(o);const i=this.styleRef,s=i.get(r)?.elements;s?s.push(o):i.set(r,{elements:[o],usage:1})}resetHostNodes(){const n=this.hostNodes;n.clear(),n.add(this.doc.head)}static#e=this.\u0275fac=function(r){return new(r||e)(I(vt),I(ya),I(Km,8),I(ur))};static#t=this.\u0275prov=x({token:e,factory:e.\u0275fac})}return e})();const Ff={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/MathML/"},Lf=/%COMP%/g,X1=new M("RemoveStylesOnCompDestroy",{providedIn:"root",factory:()=>!1});function XD(e,t){return t.map(n=>n.replace(Lf,e))}let JD=(()=>{class e{constructor(n,r,o,i,s,a,l,c=null){this.eventManager=n,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=l,this.nonce=c,this.rendererByCompId=new Map,this.platformIsServer=BD(a),this.defaultRenderer=new Vf(n,s,l,this.platformIsServer)}createRenderer(n,r){if(!n||!r)return this.defaultRenderer;this.platformIsServer&&r.encapsulation===Pt.ShadowDom&&(r={...r,encapsulation:Pt.Emulated});const o=this.getOrCreateRenderer(n,r);return o instanceof ew?o.applyToHost(n):o instanceof jf&&o.applyStyles(),o}getOrCreateRenderer(n,r){const o=this.rendererByCompId;let i=o.get(r.id);if(!i){const s=this.doc,a=this.ngZone,l=this.eventManager,c=this.sharedStylesHost,u=this.removeStylesOnCompDestroy,d=this.platformIsServer;switch(r.encapsulation){case Pt.Emulated:i=new ew(l,c,r,this.appId,u,s,a,d);break;case Pt.ShadowDom:return new tF(l,c,n,r,s,a,this.nonce,d);default:i=new jf(l,c,r,u,s,a,d)}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}static#e=this.\u0275fac=function(r){return new(r||e)(I(WD),I(YD),I(ya),I(X1),I(vt),I(ur),I(ie),I(Km))};static#t=this.\u0275prov=x({token:e,factory:e.\u0275fac})}return e})();class Vf{constructor(t,n,r,o){this.eventManager=t,this.doc=n,this.ngZone=r,this.platformIsServer=o,this.data=Object.create(null),this.destroyNode=null}destroy(){}createElement(t,n){return n?this.doc.createElementNS(Ff[n]||n,t):this.doc.createElement(t)}createComment(t){return this.doc.createComment(t)}createText(t){return this.doc.createTextNode(t)}appendChild(t,n){(KD(t)?t.content:t).appendChild(n)}insertBefore(t,n,r){t&&(KD(t)?t.content:t).insertBefore(n,r)}removeChild(t,n){t&&t.removeChild(n)}selectRootElement(t,n){let r="string"==typeof t?this.doc.querySelector(t):t;if(!r)throw new C(-5104,!1);return n||(r.textContent=""),r}parentNode(t){return t.parentNode}nextSibling(t){return t.nextSibling}setAttribute(t,n,r,o){if(o){n=o+":"+n;const i=Ff[o];i?t.setAttributeNS(i,n,r):t.setAttribute(n,r)}else t.setAttribute(n,r)}removeAttribute(t,n,r){if(r){const o=Ff[r];o?t.removeAttributeNS(o,n):t.removeAttribute(`${r}:${n}`)}else t.removeAttribute(n)}addClass(t,n){t.classList.add(n)}removeClass(t,n){t.classList.remove(n)}setStyle(t,n,r,o){o&(jn.DashCase|jn.Important)?t.style.setProperty(n,r,o&jn.Important?"important":""):t.style[n]=r}removeStyle(t,n,r){r&jn.DashCase?t.style.removeProperty(n):t.style[n]=""}setProperty(t,n,r){t[n]=r}setValue(t,n){t.nodeValue=n}listen(t,n,r){if("string"==typeof t&&!(t=Un().getGlobalEventTarget(this.doc,t)))throw new Error(`Unsupported event target ${t} for event ${n}`);return this.eventManager.addEventListener(t,n,this.decoratePreventDefault(r))}decoratePreventDefault(t){return n=>{if("__ngUnwrap__"===n)return t;!1===(this.platformIsServer?this.ngZone.runGuarded(()=>t(n)):t(n))&&n.preventDefault()}}}function KD(e){return"TEMPLATE"===e.tagName&&void 0!==e.content}class tF extends Vf{constructor(t,n,r,o,i,s,a,l){super(t,i,s,l),this.sharedStylesHost=n,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);const c=XD(o.id,o.styles);for(const u of c){const d=document.createElement("style");a&&d.setAttribute("nonce",a),d.textContent=u,this.shadowRoot.appendChild(d)}}nodeOrShadowRoot(t){return t===this.hostEl?this.shadowRoot:t}appendChild(t,n){return super.appendChild(this.nodeOrShadowRoot(t),n)}insertBefore(t,n,r){return super.insertBefore(this.nodeOrShadowRoot(t),n,r)}removeChild(t,n){return super.removeChild(this.nodeOrShadowRoot(t),n)}parentNode(t){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(t)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}}class jf extends Vf{constructor(t,n,r,o,i,s,a,l){super(t,i,s,a),this.sharedStylesHost=n,this.removeStylesOnCompDestroy=o,this.styles=l?XD(l,r.styles):r.styles}applyStyles(){this.sharedStylesHost.addStyles(this.styles)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles)}}class ew extends jf{constructor(t,n,r,o,i,s,a,l){const c=o+"-"+r.id;super(t,n,r,i,s,a,l,c),this.contentAttr=function J1(e){return"_ngcontent-%COMP%".replace(Lf,e)}(c),this.hostAttr=function K1(e){return"_nghost-%COMP%".replace(Lf,e)}(c)}applyToHost(t){this.applyStyles(),this.setAttribute(t,this.hostAttr,"")}createElement(t,n){const r=super.createElement(t,n);return super.setAttribute(r,this.contentAttr,""),r}}let nF=(()=>{class e extends ZD{constructor(n){super(n)}supports(n){return!0}addEventListener(n,r,o){return n.addEventListener(r,o,!1),()=>this.removeEventListener(n,r,o)}removeEventListener(n,r,o){return n.removeEventListener(r,o)}static#e=this.\u0275fac=function(r){return new(r||e)(I(vt))};static#t=this.\u0275prov=x({token:e,factory:e.\u0275fac})}return e})();const tw=["alt","control","meta","shift"],rF={"\b":"Backspace","\t":"Tab","\x7f":"Delete","\x1b":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},oF={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey};let iF=(()=>{class e extends ZD{constructor(n){super(n)}supports(n){return null!=e.parseEventName(n)}addEventListener(n,r,o){const i=e.parseEventName(r),s=e.eventCallback(i.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>Un().onAndCancel(n,i.domEventName,s))}static parseEventName(n){const r=n.toLowerCase().split("."),o=r.shift();if(0===r.length||"keydown"!==o&&"keyup"!==o)return null;const i=e._normalizeKey(r.pop());let s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),tw.forEach(c=>{const u=r.indexOf(c);u>-1&&(r.splice(u,1),s+=c+".")}),s+=i,0!=r.length||0===i.length)return null;const l={};return l.domEventName=o,l.fullKey=s,l}static matchEventFullKeyCode(n,r){let o=rF[n.key]||n.key,i="";return r.indexOf("code.")>-1&&(o=n.code,i="code."),!(null==o||!o)&&(o=o.toLowerCase()," "===o?o="space":"."===o&&(o="dot"),tw.forEach(s=>{s!==o&&(0,oF[s])(n)&&(i+=s+".")}),i+=o,i===r)}static eventCallback(n,r,o){return i=>{e.matchEventFullKeyCode(i,n)&&o.runGuarded(()=>r(i))}}static _normalizeKey(n){return"esc"===n?"escape":n}static#e=this.\u0275fac=function(r){return new(r||e)(I(vt))};static#t=this.\u0275prov=x({token:e,factory:e.\u0275fac})}return e})();const cF=ZC(VR,"browser",[{provide:ur,useValue:"browser"},{provide:Jm,useValue:function sF(){Pf.makeCurrent()},multi:!0},{provide:vt,useFactory:function lF(){return function WS(e){mu=e}(document),document},deps:[]}]),uF=new M(""),ow=[{provide:Ya,useClass:class q1{addToWindow(t){re.getAngularTestability=(r,o=!0)=>{const i=t.findTestabilityInTree(r,o);if(null==i)throw new C(5103,!1);return i},re.getAllAngularTestabilities=()=>t.getAllTestabilities(),re.getAllAngularRootElements=()=>t.getAllRootElements(),re.frameworkStabilizers||(re.frameworkStabilizers=[]),re.frameworkStabilizers.push(r=>{const o=re.getAllAngularTestabilities();let i=o.length,s=!1;const a=function(l){s=s||l,i--,0==i&&r(s)};o.forEach(l=>{l.whenStable(a)})})}findTestabilityInTree(t,n,r){return null==n?null:t.getTestability(n)??(r?Un().isShadowRoot(n)?this.findTestabilityInTree(t,n.host,!0):this.findTestabilityInTree(t,n.parentElement,!0):null)}},deps:[]},{provide:UC,useClass:Qd,deps:[ie,Xd,Ya]},{provide:Qd,useClass:Qd,deps:[ie,Xd,Ya]}],iw=[{provide:Su,useValue:"root"},{provide:yn,useFactory:function aF(){return new yn},deps:[]},{provide:Rf,useClass:nF,multi:!0,deps:[vt,ie,ur]},{provide:Rf,useClass:iF,multi:!0,deps:[vt]},JD,YD,WD,{provide:iy,useExisting:JD},{provide:HD,useClass:W1,deps:[]},[]];let dF=(()=>{class e{constructor(n){}static withServerTransition(n){return{ngModule:e,providers:[{provide:ya,useValue:n.appId}]}}static#e=this.\u0275fac=function(r){return new(r||e)(I(uF,12))};static#t=this.\u0275mod=wt({type:e});static#n=this.\u0275inj=ct({providers:[...iw,...ow],imports:[p1,jR]})}return e})(),sw=(()=>{class e{constructor(n){this._doc=n}getTitle(){return this._doc.title}setTitle(n){this._doc.title=n||""}static#e=this.\u0275fac=function(r){return new(r||e)(I(vt))};static#t=this.\u0275prov=x({token:e,factory:function(r){let o=null;return o=r?new r:function hF(){return new sw(I(vt))}(),o},providedIn:"root"})}return e})();typeof window<"u"&&window;const{isArray:_F}=Array,{getPrototypeOf:CF,prototype:DF,keys:wF}=Object;function uw(e){if(1===e.length){const t=e[0];if(_F(t))return{args:t,keys:null};if(function bF(e){return e&&"object"==typeof e&&CF(e)===DF}(t)){const n=wF(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}const{isArray:EF}=Array;function dw(e){return z(t=>function MF(e,t){return EF(t)?e(...t):e(t)}(e,t))}function fw(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function Bf(...e){const t=Uo(e),n=_p(e),{args:r,keys:o}=uw(e);if(0===r.length)return Me([],t);const i=new he(function IF(e,t,n=On){return r=>{hw(t,()=>{const{length:o}=e,i=new Array(o);let s=o,a=o;for(let l=0;l<o;l++)hw(t,()=>{const c=Me(e[l],t);let u=!1;c.subscribe(De(r,d=>{i[l]=d,u||(u=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}(r,t,o?s=>fw(o,s):On));return n?i.pipe(dw(n)):i}function hw(e,t,n){e?cn(n,e,t):t()}const gl=$o(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function Hf(...e){return function SF(){return Sr(1)}()(Me(e,Uo(e)))}function pw(e){return new he(t=>{at(e()).subscribe(t)})}function Wi(e,t){const n=ee(e)?e:()=>e,r=o=>o.error(n());return new he(t?o=>t.schedule(r,0,o):r)}function Uf(){return Ee((e,t)=>{let n=null;e._refCount++;const r=De(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount)return void(n=null);const o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}class gw extends he{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,op(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){const t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;const{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new st;const n=this.getSubject();t.add(this.source.subscribe(De(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=st.EMPTY)}return t}refCount(){return Uf()(this)}}function Io(e){return e<=0?()=>Gt:Ee((t,n)=>{let r=0;t.subscribe(De(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function In(e,t){return Ee((n,r)=>{let o=0;n.subscribe(De(r,i=>e.call(t,i,o++)&&r.next(i)))})}function ml(e){return Ee((t,n)=>{let r=!1;t.subscribe(De(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function mw(e=AF){return Ee((t,n)=>{let r=!1;t.subscribe(De(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function AF(){return new gl}function yr(e,t){const n=arguments.length>=2;return r=>r.pipe(e?In((o,i)=>e(o,i,r)):On,Io(1),n?ml(t):mw(()=>new gl))}function So(e,t){return ee(t)?Ae(e,t,1):Ae(e,1)}function $e(e,t,n){const r=ee(e)||t||n?{next:e,error:t,complete:n}:e;return r?Ee((o,i)=>{var s;null===(s=r.subscribe)||void 0===s||s.call(r);let a=!0;o.subscribe(De(i,l=>{var c;null===(c=r.next)||void 0===c||c.call(r,l),i.next(l)},()=>{var l;a=!1,null===(l=r.complete)||void 0===l||l.call(r),i.complete()},l=>{var c;a=!1,null===(c=r.error)||void 0===c||c.call(r,l),i.error(l)},()=>{var l,c;a&&(null===(l=r.unsubscribe)||void 0===l||l.call(r)),null===(c=r.finalize)||void 0===c||c.call(r)}))}):On}function Gn(e){return Ee((t,n)=>{let i,r=null,o=!1;r=t.subscribe(De(n,void 0,void 0,s=>{i=at(e(s,Gn(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function zf(e){return e<=0?()=>Gt:Ee((t,n)=>{let r=[];t.subscribe(De(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(const o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function Zi(e){return Ee((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}const V="primary",Yi=Symbol("RouteTitle");class kF{constructor(t){this.params=t||{}}has(t){return Object.prototype.hasOwnProperty.call(this.params,t)}get(t){if(this.has(t)){const n=this.params[t];return Array.isArray(n)?n[0]:n}return null}getAll(t){if(this.has(t)){const n=this.params[t];return Array.isArray(n)?n:[n]}return[]}get keys(){return Object.keys(this.params)}}function xo(e){return new kF(e)}function FF(e,t,n){const r=n.path.split("/");if(r.length>e.length||"full"===n.pathMatch&&(t.hasChildren()||r.length<e.length))return null;const o={};for(let i=0;i<r.length;i++){const s=r[i],a=e[i];if(s.startsWith(":"))o[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:o}}function on(e,t){const n=e?Object.keys(e):void 0,r=t?Object.keys(t):void 0;if(!n||!r||n.length!=r.length)return!1;let o;for(let i=0;i<n.length;i++)if(o=n[i],!yw(e[o],t[o]))return!1;return!0}function yw(e,t){if(Array.isArray(e)&&Array.isArray(t)){if(e.length!==t.length)return!1;const n=[...e].sort(),r=[...t].sort();return n.every((o,i)=>r[i]===o)}return e===t}function vw(e){return e.length>0?e[e.length-1]:null}function qn(e){return function vF(e){return!!e&&(e instanceof he||ee(e.lift)&&ee(e.subscribe))}(e)?e:Ti(e)?Me(Promise.resolve(e)):A(e)}const VF={exact:function Dw(e,t,n){if(!vr(e.segments,t.segments)||!yl(e.segments,t.segments,n)||e.numberOfChildren!==t.numberOfChildren)return!1;for(const r in t.children)if(!e.children[r]||!Dw(e.children[r],t.children[r],n))return!1;return!0},subset:ww},_w={exact:function jF(e,t){return on(e,t)},subset:function $F(e,t){return Object.keys(t).length<=Object.keys(e).length&&Object.keys(t).every(n=>yw(e[n],t[n]))},ignored:()=>!0};function Cw(e,t,n){return VF[n.paths](e.root,t.root,n.matrixParams)&&_w[n.queryParams](e.queryParams,t.queryParams)&&!("exact"===n.fragment&&e.fragment!==t.fragment)}function ww(e,t,n){return bw(e,t,t.segments,n)}function bw(e,t,n,r){if(e.segments.length>n.length){const o=e.segments.slice(0,n.length);return!(!vr(o,n)||t.hasChildren()||!yl(o,n,r))}if(e.segments.length===n.length){if(!vr(e.segments,n)||!yl(e.segments,n,r))return!1;for(const o in t.children)if(!e.children[o]||!ww(e.children[o],t.children[o],r))return!1;return!0}{const o=n.slice(0,e.segments.length),i=n.slice(e.segments.length);return!!(vr(e.segments,o)&&yl(e.segments,o,r)&&e.children[V])&&bw(e.children[V],t,i,r)}}function yl(e,t,n){return t.every((r,o)=>_w[n](e[o].parameters,r.parameters))}class Ao{constructor(t=new K([],{}),n={},r=null){this.root=t,this.queryParams=n,this.fragment=r}get queryParamMap(){return this._queryParamMap||(this._queryParamMap=xo(this.queryParams)),this._queryParamMap}toString(){return UF.serialize(this)}}class K{constructor(t,n){this.segments=t,this.children=n,this.parent=null,Object.values(n).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return vl(this)}}class Qi{constructor(t,n){this.path=t,this.parameters=n}get parameterMap(){return this._parameterMap||(this._parameterMap=xo(this.parameters)),this._parameterMap}toString(){return Iw(this)}}function vr(e,t){return e.length===t.length&&e.every((n,r)=>n.path===t[r].path)}let Xi=(()=>{class e{static#e=this.\u0275fac=function(r){return new(r||e)};static#t=this.\u0275prov=x({token:e,factory:function(){return new Gf},providedIn:"root"})}return e})();class Gf{parse(t){const n=new eL(t);return new Ao(n.parseRootSegment(),n.parseQueryParams(),n.parseFragment())}serialize(t){const n=`/${Ji(t.root,!0)}`,r=function qF(e){const t=Object.keys(e).map(n=>{const r=e[n];return Array.isArray(r)?r.map(o=>`${_l(n)}=${_l(o)}`).join("&"):`${_l(n)}=${_l(r)}`}).filter(n=>!!n);return t.length?`?${t.join("&")}`:""}(t.queryParams);return`${n}${r}${"string"==typeof t.fragment?`#${function zF(e){return encodeURI(e)}(t.fragment)}`:""}`}}const UF=new Gf;function vl(e){return e.segments.map(t=>Iw(t)).join("/")}function Ji(e,t){if(!e.hasChildren())return vl(e);if(t){const n=e.children[V]?Ji(e.children[V],!1):"",r=[];return Object.entries(e.children).forEach(([o,i])=>{o!==V&&r.push(`${o}:${Ji(i,!1)}`)}),r.length>0?`${n}(${r.join("//")})`:n}{const n=function HF(e,t){let n=[];return Object.entries(e.children).forEach(([r,o])=>{r===V&&(n=n.concat(t(o,r)))}),Object.entries(e.children).forEach(([r,o])=>{r!==V&&(n=n.concat(t(o,r)))}),n}(e,(r,o)=>o===V?[Ji(e.children[V],!1)]:[`${o}:${Ji(r,!1)}`]);return 1===Object.keys(e.children).length&&null!=e.children[V]?`${vl(e)}/${n[0]}`:`${vl(e)}/(${n.join("//")})`}}function Ew(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function _l(e){return Ew(e).replace(/%3B/gi,";")}function qf(e){return Ew(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function Cl(e){return decodeURIComponent(e)}function Mw(e){return Cl(e.replace(/\+/g,"%20"))}function Iw(e){return`${qf(e.path)}${function GF(e){return Object.keys(e).map(t=>`;${qf(t)}=${qf(e[t])}`).join("")}(e.parameters)}`}const WF=/^[^\/()?;#]+/;function Wf(e){const t=e.match(WF);return t?t[0]:""}const ZF=/^[^\/()?;=#]+/,QF=/^[^=?&#]+/,JF=/^[^&#]+/;class eL{constructor(t){this.url=t,this.remaining=t}parseRootSegment(){return this.consumeOptional("/"),""===this.remaining||this.peekStartsWith("?")||this.peekStartsWith("#")?new K([],{}):new K([],this.parseChildren())}parseQueryParams(){const t={};if(this.consumeOptional("?"))do{this.parseQueryParam(t)}while(this.consumeOptional("&"));return t}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(""===this.remaining)return{};this.consumeOptional("/");const t=[];for(this.peekStartsWith("(")||t.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),t.push(this.parseSegment());let n={};this.peekStartsWith("/(")&&(this.capture("/"),n=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(t.length>0||Object.keys(n).length>0)&&(r[V]=new K(t,n)),r}parseSegment(){const t=Wf(this.remaining);if(""===t&&this.peekStartsWith(";"))throw new C(4009,!1);return this.capture(t),new Qi(Cl(t),this.parseMatrixParams())}parseMatrixParams(){const t={};for(;this.consumeOptional(";");)this.parseParam(t);return t}parseParam(t){const n=function YF(e){const t=e.match(ZF);return t?t[0]:""}(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){const o=Wf(this.remaining);o&&(r=o,this.capture(r))}t[Cl(n)]=Cl(r)}parseQueryParam(t){const n=function XF(e){const t=e.match(QF);return t?t[0]:""}(this.remaining);if(!n)return;this.capture(n);let r="";if(this.consumeOptional("=")){const s=function KF(e){const t=e.match(JF);return t?t[0]:""}(this.remaining);s&&(r=s,this.capture(r))}const o=Mw(n),i=Mw(r);if(t.hasOwnProperty(o)){let s=t[o];Array.isArray(s)||(s=[s],t[o]=s),s.push(i)}else t[o]=i}parseParens(t){const n={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){const r=Wf(this.remaining),o=this.remaining[r.length];if("/"!==o&&")"!==o&&";"!==o)throw new C(4010,!1);let i;r.indexOf(":")>-1?(i=r.slice(0,r.indexOf(":")),this.capture(i),this.capture(":")):t&&(i=V);const s=this.parseChildren();n[i]=1===Object.keys(s).length?s[V]:new K([],s),this.consumeOptional("//")}return n}peekStartsWith(t){return this.remaining.startsWith(t)}consumeOptional(t){return!!this.peekStartsWith(t)&&(this.remaining=this.remaining.substring(t.length),!0)}capture(t){if(!this.consumeOptional(t))throw new C(4011,!1)}}function Sw(e){return e.segments.length>0?new K([],{[V]:e}):e}function xw(e){const t={};for(const r of Object.keys(e.children)){const i=xw(e.children[r]);if(r===V&&0===i.segments.length&&i.hasChildren())for(const[s,a]of Object.entries(i.children))t[s]=a;else(i.segments.length>0||i.hasChildren())&&(t[r]=i)}return function tL(e){if(1===e.numberOfChildren&&e.children[V]){const t=e.children[V];return new K(e.segments.concat(t.segments),t.children)}return e}(new K(e.segments,t))}function _r(e){return e instanceof Ao}function Aw(e){let t;const o=Sw(function n(i){const s={};for(const l of i.children){const c=n(l);s[l.outlet]=c}const a=new K(i.url,s);return i===e&&(t=a),a}(e.root));return t??o}function Tw(e,t,n,r){let o=e;for(;o.parent;)o=o.parent;if(0===t.length)return Zf(o,o,o,n,r);const i=function rL(e){if("string"==typeof e[0]&&1===e.length&&"/"===e[0])return new Nw(!0,0,e);let t=0,n=!1;const r=e.reduce((o,i,s)=>{if("object"==typeof i&&null!=i){if(i.outlets){const a={};return Object.entries(i.outlets).forEach(([l,c])=>{a[l]="string"==typeof c?c.split("/"):c}),[...o,{outlets:a}]}if(i.segmentPath)return[...o,i.segmentPath]}return"string"!=typeof i?[...o,i]:0===s?(i.split("/").forEach((a,l)=>{0==l&&"."===a||(0==l&&""===a?n=!0:".."===a?t++:""!=a&&o.push(a))}),o):[...o,i]},[]);return new Nw(n,t,r)}(t);if(i.toRoot())return Zf(o,o,new K([],{}),n,r);const s=function oL(e,t,n){if(e.isAbsolute)return new wl(t,!0,0);if(!n)return new wl(t,!1,NaN);if(null===n.parent)return new wl(n,!0,0);const r=Dl(e.commands[0])?0:1;return function iL(e,t,n){let r=e,o=t,i=n;for(;i>o;){if(i-=o,r=r.parent,!r)throw new C(4005,!1);o=r.segments.length}return new wl(r,!1,o-i)}(n,n.segments.length-1+r,e.numberOfDoubleDots)}(i,o,e),a=s.processChildren?es(s.segmentGroup,s.index,i.commands):Pw(s.segmentGroup,s.index,i.commands);return Zf(o,s.segmentGroup,a,n,r)}function Dl(e){return"object"==typeof e&&null!=e&&!e.outlets&&!e.segmentPath}function Ki(e){return"object"==typeof e&&null!=e&&e.outlets}function Zf(e,t,n,r,o){let s,i={};r&&Object.entries(r).forEach(([l,c])=>{i[l]=Array.isArray(c)?c.map(u=>`${u}`):`${c}`}),s=e===t?n:Ow(e,t,n);const a=Sw(xw(s));return new Ao(a,i,o)}function Ow(e,t,n){const r={};return Object.entries(e.children).forEach(([o,i])=>{r[o]=i===t?n:Ow(i,t,n)}),new K(e.segments,r)}class Nw{constructor(t,n,r){if(this.isAbsolute=t,this.numberOfDoubleDots=n,this.commands=r,t&&r.length>0&&Dl(r[0]))throw new C(4003,!1);const o=r.find(Ki);if(o&&o!==vw(r))throw new C(4004,!1)}toRoot(){return this.isAbsolute&&1===this.commands.length&&"/"==this.commands[0]}}class wl{constructor(t,n,r){this.segmentGroup=t,this.processChildren=n,this.index=r}}function Pw(e,t,n){if(e||(e=new K([],{})),0===e.segments.length&&e.hasChildren())return es(e,t,n);const r=function aL(e,t,n){let r=0,o=t;const i={match:!1,pathIndex:0,commandIndex:0};for(;o<e.segments.length;){if(r>=n.length)return i;const s=e.segments[o],a=n[r];if(Ki(a))break;const l=`${a}`,c=r<n.length-1?n[r+1]:null;if(o>0&&void 0===l)break;if(l&&c&&"object"==typeof c&&void 0===c.outlets){if(!kw(l,c,s))return i;r+=2}else{if(!kw(l,{},s))return i;r++}o++}return{match:!0,pathIndex:o,commandIndex:r}}(e,t,n),o=n.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){const i=new K(e.segments.slice(0,r.pathIndex),{});return i.children[V]=new K(e.segments.slice(r.pathIndex),e.children),es(i,0,o)}return r.match&&0===o.length?new K(e.segments,{}):r.match&&!e.hasChildren()?Yf(e,t,n):r.match?es(e,0,o):Yf(e,t,n)}function es(e,t,n){if(0===n.length)return new K(e.segments,{});{const r=function sL(e){return Ki(e[0])?e[0].outlets:{[V]:e}}(n),o={};if(Object.keys(r).some(i=>i!==V)&&e.children[V]&&1===e.numberOfChildren&&0===e.children[V].segments.length){const i=es(e.children[V],t,n);return new K(e.segments,i.children)}return Object.entries(r).forEach(([i,s])=>{"string"==typeof s&&(s=[s]),null!==s&&(o[i]=Pw(e.children[i],t,s))}),Object.entries(e.children).forEach(([i,s])=>{void 0===r[i]&&(o[i]=s)}),new K(e.segments,o)}}function Yf(e,t,n){const r=e.segments.slice(0,t);let o=0;for(;o<n.length;){const i=n[o];if(Ki(i)){const l=lL(i.outlets);return new K(r,l)}if(0===o&&Dl(n[0])){r.push(new Qi(e.segments[t].path,Rw(n[0]))),o++;continue}const s=Ki(i)?i.outlets[V]:`${i}`,a=o<n.length-1?n[o+1]:null;s&&a&&Dl(a)?(r.push(new Qi(s,Rw(a))),o+=2):(r.push(new Qi(s,{})),o++)}return new K(r,{})}function lL(e){const t={};return Object.entries(e).forEach(([n,r])=>{"string"==typeof r&&(r=[r]),null!==r&&(t[n]=Yf(new K([],{}),0,r))}),t}function Rw(e){const t={};return Object.entries(e).forEach(([n,r])=>t[n]=`${r}`),t}function kw(e,t,n){return e==n.path&&on(t,n.parameters)}const ts="imperative";class sn{constructor(t,n){this.id=t,this.url=n}}class bl extends sn{constructor(t,n,r="imperative",o=null){super(t,n),this.type=0,this.navigationTrigger=r,this.restoredState=o}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}}class Wn extends sn{constructor(t,n,r){super(t,n),this.urlAfterRedirects=r,this.type=1}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}}class ns extends sn{constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o,this.type=2}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}}class To extends sn{constructor(t,n,r,o){super(t,n),this.reason=r,this.code=o,this.type=16}}class El extends sn{constructor(t,n,r,o){super(t,n),this.error=r,this.target=o,this.type=3}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}}class Fw extends sn{constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o,this.type=4}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class cL extends sn{constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o,this.type=7}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class uL extends sn{constructor(t,n,r,o,i){super(t,n),this.urlAfterRedirects=r,this.state=o,this.shouldActivate=i,this.type=8}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}}class dL extends sn{constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o,this.type=5}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class fL extends sn{constructor(t,n,r,o){super(t,n),this.urlAfterRedirects=r,this.state=o,this.type=6}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}}class hL{constructor(t){this.route=t,this.type=9}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}}class pL{constructor(t){this.route=t,this.type=10}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}}class gL{constructor(t){this.snapshot=t,this.type=11}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class mL{constructor(t){this.snapshot=t,this.type=12}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class yL{constructor(t){this.snapshot=t,this.type=13}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class vL{constructor(t){this.snapshot=t,this.type=14}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}}class Lw{constructor(t,n,r){this.routerEvent=t,this.position=n,this.anchor=r,this.type=15}toString(){return`Scroll(anchor: '${this.anchor}', position: '${this.position?`${this.position[0]}, ${this.position[1]}`:null}')`}}class Qf{}class Xf{constructor(t){this.url=t}}class _L{constructor(){this.outlet=null,this.route=null,this.injector=null,this.children=new rs,this.attachRef=null}}let rs=(()=>{class e{constructor(){this.contexts=new Map}onChildOutletCreated(n,r){const o=this.getOrCreateContext(n);o.outlet=r,this.contexts.set(n,o)}onChildOutletDestroyed(n){const r=this.getContext(n);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){const n=this.contexts;return this.contexts=new Map,n}onOutletReAttached(n){this.contexts=n}getOrCreateContext(n){let r=this.getContext(n);return r||(r=new _L,this.contexts.set(n,r)),r}getContext(n){return this.contexts.get(n)||null}static#e=this.\u0275fac=function(r){return new(r||e)};static#t=this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();class Vw{constructor(t){this._root=t}get root(){return this._root.value}parent(t){const n=this.pathFromRoot(t);return n.length>1?n[n.length-2]:null}children(t){const n=Jf(t,this._root);return n?n.children.map(r=>r.value):[]}firstChild(t){const n=Jf(t,this._root);return n&&n.children.length>0?n.children[0].value:null}siblings(t){const n=Kf(t,this._root);return n.length<2?[]:n[n.length-2].children.map(o=>o.value).filter(o=>o!==t)}pathFromRoot(t){return Kf(t,this._root).map(n=>n.value)}}function Jf(e,t){if(e===t.value)return t;for(const n of t.children){const r=Jf(e,n);if(r)return r}return null}function Kf(e,t){if(e===t.value)return[t];for(const n of t.children){const r=Kf(e,n);if(r.length)return r.unshift(t),r}return[]}class Sn{constructor(t,n){this.value=t,this.children=n}toString(){return`TreeNode(${this.value})`}}function Oo(e){const t={};return e&&e.children.forEach(n=>t[n.value.outlet]=n),t}class jw extends Vw{constructor(t,n){super(t),this.snapshot=n,eh(this,t)}toString(){return this.snapshot.toString()}}function $w(e,t){const n=function CL(e,t){const s=new Ml([],{},{},"",{},V,t,null,{});return new Hw("",new Sn(s,[]))}(0,t),r=new Ct([new Qi("",{})]),o=new Ct({}),i=new Ct({}),s=new Ct({}),a=new Ct(""),l=new No(r,o,s,a,i,V,t,n.root);return l.snapshot=n.root,new jw(new Sn(l,[]),n)}class No{constructor(t,n,r,o,i,s,a,l){this.urlSubject=t,this.paramsSubject=n,this.queryParamsSubject=r,this.fragmentSubject=o,this.dataSubject=i,this.outlet=s,this.component=a,this._futureSnapshot=l,this.title=this.dataSubject?.pipe(z(c=>c[Yi]))??A(void 0),this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap||(this._paramMap=this.params.pipe(z(t=>xo(t)))),this._paramMap}get queryParamMap(){return this._queryParamMap||(this._queryParamMap=this.queryParams.pipe(z(t=>xo(t)))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}}function Bw(e,t="emptyOnly"){const n=e.pathFromRoot;let r=0;if("always"!==t)for(r=n.length-1;r>=1;){const o=n[r],i=n[r-1];if(o.routeConfig&&""===o.routeConfig.path)r--;else{if(i.component)break;r--}}return function DL(e){return e.reduce((t,n)=>({params:{...t.params,...n.params},data:{...t.data,...n.data},resolve:{...n.data,...t.resolve,...n.routeConfig?.data,...n._resolvedData}}),{params:{},data:{},resolve:{}})}(n.slice(r))}class Ml{get title(){return this.data?.[Yi]}constructor(t,n,r,o,i,s,a,l,c){this.url=t,this.params=n,this.queryParams=r,this.fragment=o,this.data=i,this.outlet=s,this.component=a,this.routeConfig=l,this._resolve=c}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap||(this._paramMap=xo(this.params)),this._paramMap}get queryParamMap(){return this._queryParamMap||(this._queryParamMap=xo(this.queryParams)),this._queryParamMap}toString(){return`Route(url:'${this.url.map(r=>r.toString()).join("/")}', path:'${this.routeConfig?this.routeConfig.path:""}')`}}class Hw extends Vw{constructor(t,n){super(n),this.url=t,eh(this,n)}toString(){return Uw(this._root)}}function eh(e,t){t.value._routerState=e,t.children.forEach(n=>eh(e,n))}function Uw(e){const t=e.children.length>0?` { ${e.children.map(Uw).join(", ")} } `:"";return`${e.value}${t}`}function th(e){if(e.snapshot){const t=e.snapshot,n=e._futureSnapshot;e.snapshot=n,on(t.queryParams,n.queryParams)||e.queryParamsSubject.next(n.queryParams),t.fragment!==n.fragment&&e.fragmentSubject.next(n.fragment),on(t.params,n.params)||e.paramsSubject.next(n.params),function LF(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;++n)if(!on(e[n],t[n]))return!1;return!0}(t.url,n.url)||e.urlSubject.next(n.url),on(t.data,n.data)||e.dataSubject.next(n.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function nh(e,t){const n=on(e.params,t.params)&&function BF(e,t){return vr(e,t)&&e.every((n,r)=>on(n.parameters,t[r].parameters))}(e.url,t.url);return n&&!(!e.parent!=!t.parent)&&(!e.parent||nh(e.parent,t.parent))}let zw=(()=>{class e{constructor(){this.activated=null,this._activatedRoute=null,this.name=V,this.activateEvents=new ge,this.deactivateEvents=new ge,this.attachEvents=new ge,this.detachEvents=new ge,this.parentContexts=E(rs),this.location=E(Bt),this.changeDetector=E(Xa),this.environmentInjector=E(ht),this.inputBinder=E(Il,{optional:!0}),this.supportsBindingToComponentInputs=!0}get activatedComponentRef(){return this.activated}ngOnChanges(n){if(n.name){const{firstChange:r,previousValue:o}=n.name;if(r)return;this.isTrackedInParentContexts(o)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(o)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(n){return this.parentContexts.getContext(n)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;const n=this.parentContexts.getContext(this.name);n?.route&&(n.attachRef?this.attach(n.attachRef,n.route):this.activateWith(n.route,n.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new C(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new C(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new C(4012,!1);this.location.detach();const n=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(n.instance),n}attach(n,r){this.activated=n,this._activatedRoute=r,this.location.insert(n.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(n.instance)}deactivate(){if(this.activated){const n=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(n)}}activateWith(n,r){if(this.isActivated)throw new C(4013,!1);this._activatedRoute=n;const o=this.location,s=n.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,l=new wL(n,a,o.injector);this.activated=o.createComponent(s,{index:o.length,injector:l,environmentInjector:r??this.environmentInjector}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static#e=this.\u0275fac=function(r){return new(r||e)};static#t=this.\u0275dir=P({type:e,selectors:[["router-outlet"]],inputs:{name:"name"},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],standalone:!0,features:[Et]})}return e})();class wL{constructor(t,n,r){this.route=t,this.childContexts=n,this.parent=r}get(t,n){return t===No?this.route:t===rs?this.childContexts:this.parent.get(t,n)}}const Il=new M("");let Gw=(()=>{class e{constructor(){this.outletDataSubscriptions=new Map}bindActivatedRouteToOutletComponent(n){this.unsubscribeFromRouteData(n),this.subscribeToRouteData(n)}unsubscribeFromRouteData(n){this.outletDataSubscriptions.get(n)?.unsubscribe(),this.outletDataSubscriptions.delete(n)}subscribeToRouteData(n){const{activatedRoute:r}=n,o=Bf([r.queryParams,r.params,r.data]).pipe(Nt(([i,s,a],l)=>(a={...i,...s,...a},0===l?A(a):Promise.resolve(a)))).subscribe(i=>{if(!n.isActivated||!n.activatedComponentRef||n.activatedRoute!==r||null===r.component)return void this.unsubscribeFromRouteData(n);const s=function XR(e){const t=H(e);if(!t)return null;const n=new Mi(t);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return t.standalone},get isSignal(){return t.signals}}}(r.component);if(s)for(const{templateName:a}of s.inputs)n.activatedComponentRef.setInput(a,i[a]);else this.unsubscribeFromRouteData(n)});this.outletDataSubscriptions.set(n,o)}static#e=this.\u0275fac=function(r){return new(r||e)};static#t=this.\u0275prov=x({token:e,factory:e.\u0275fac})}return e})();function os(e,t,n){if(n&&e.shouldReuseRoute(t.value,n.value.snapshot)){const r=n.value;r._futureSnapshot=t.value;const o=function EL(e,t,n){return t.children.map(r=>{for(const o of n.children)if(e.shouldReuseRoute(r.value,o.value.snapshot))return os(e,r,o);return os(e,r)})}(e,t,n);return new Sn(r,o)}{if(e.shouldAttach(t.value)){const i=e.retrieve(t.value);if(null!==i){const s=i.route;return s.value._futureSnapshot=t.value,s.children=t.children.map(a=>os(e,a)),s}}const r=function ML(e){return new No(new Ct(e.url),new Ct(e.params),new Ct(e.queryParams),new Ct(e.fragment),new Ct(e.data),e.outlet,e.component,e)}(t.value),o=t.children.map(i=>os(e,i));return new Sn(r,o)}}const rh="ngNavigationCancelingError";function qw(e,t){const{redirectTo:n,navigationBehaviorOptions:r}=_r(t)?{redirectTo:t,navigationBehaviorOptions:void 0}:t,o=Ww(!1,0,t);return o.url=n,o.navigationBehaviorOptions=r,o}function Ww(e,t,n){const r=new Error("NavigationCancelingError: "+(e||""));return r[rh]=!0,r.cancellationCode=t,n&&(r.url=n),r}function Zw(e){return e&&e[rh]}let Yw=(()=>{class e{static#e=this.\u0275fac=function(r){return new(r||e)};static#t=this.\u0275cmp=Ar({type:e,selectors:[["ng-component"]],standalone:!0,features:[G_],decls:1,vars:0,template:function(r,o){1&r&&Lt(0,"router-outlet")},dependencies:[zw],encapsulation:2})}return e})();function oh(e){const t=e.children&&e.children.map(oh),n=t?{...e,children:t}:{...e};return!n.component&&!n.loadComponent&&(t||n.loadChildren)&&n.outlet&&n.outlet!==V&&(n.component=Yw),n}function zt(e){return e.outlet||V}function is(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let t=e.parent;t;t=t.parent){const n=t.routeConfig;if(n?._loadedInjector)return n._loadedInjector;if(n?._injector)return n._injector}return null}class PL{constructor(t,n,r,o,i){this.routeReuseStrategy=t,this.futureState=n,this.currState=r,this.forwardEvent=o,this.inputBindingEnabled=i}activate(t){const n=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(n,r,t),th(this.futureState.root),this.activateChildRoutes(n,r,t)}deactivateChildRoutes(t,n,r){const o=Oo(n);t.children.forEach(i=>{const s=i.value.outlet;this.deactivateRoutes(i,o[s],r),delete o[s]}),Object.values(o).forEach(i=>{this.deactivateRouteAndItsChildren(i,r)})}deactivateRoutes(t,n,r){const o=t.value,i=n?n.value:null;if(o===i)if(o.component){const s=r.getContext(o.outlet);s&&this.deactivateChildRoutes(t,n,s.children)}else this.deactivateChildRoutes(t,n,r);else i&&this.deactivateRouteAndItsChildren(n,r)}deactivateRouteAndItsChildren(t,n){t.value.component&&this.routeReuseStrategy.shouldDetach(t.value.snapshot)?this.detachAndStoreRouteSubtree(t,n):this.deactivateRouteAndOutlet(t,n)}detachAndStoreRouteSubtree(t,n){const r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=Oo(t);for(const s of Object.keys(i))this.deactivateRouteAndItsChildren(i[s],o);if(r&&r.outlet){const s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(t.value.snapshot,{componentRef:s,route:t,contexts:a})}}deactivateRouteAndOutlet(t,n){const r=n.getContext(t.value.outlet),o=r&&t.value.component?r.children:n,i=Oo(t);for(const s of Object.keys(i))this.deactivateRouteAndItsChildren(i[s],o);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(t,n,r){const o=Oo(n);t.children.forEach(i=>{this.activateRoutes(i,o[i.value.outlet],r),this.forwardEvent(new vL(i.value.snapshot))}),t.children.length&&this.forwardEvent(new mL(t.value.snapshot))}activateRoutes(t,n,r){const o=t.value,i=n?n.value:null;if(th(o),o===i)if(o.component){const s=r.getOrCreateContext(o.outlet);this.activateChildRoutes(t,n,s.children)}else this.activateChildRoutes(t,n,r);else if(o.component){const s=r.getOrCreateContext(o.outlet);if(this.routeReuseStrategy.shouldAttach(o.snapshot)){const a=this.routeReuseStrategy.retrieve(o.snapshot);this.routeReuseStrategy.store(o.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),th(a.route.value),this.activateChildRoutes(t,null,s.children)}else{const a=is(o.snapshot);s.attachRef=null,s.route=o,s.injector=a,s.outlet&&s.outlet.activateWith(o,s.injector),this.activateChildRoutes(t,null,s.children)}}else this.activateChildRoutes(t,null,r)}}class Qw{constructor(t){this.path=t,this.route=this.path[this.path.length-1]}}class Sl{constructor(t,n){this.component=t,this.route=n}}function RL(e,t,n){const r=e._root;return ss(r,t?t._root:null,n,[r.value])}function Po(e,t){const n=Symbol(),r=t.get(e,n);return r===n?"function"!=typeof e||function uM(e){return null!==Ss(e)}(e)?t.get(e):e:r}function ss(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){const i=Oo(t);return e.children.forEach(s=>{(function FL(e,t,n,r,o={canDeactivateChecks:[],canActivateChecks:[]}){const i=e.value,s=t?t.value:null,a=n?n.getContext(e.value.outlet):null;if(s&&i.routeConfig===s.routeConfig){const l=function LL(e,t,n){if("function"==typeof n)return n(e,t);switch(n){case"pathParamsChange":return!vr(e.url,t.url);case"pathParamsOrQueryParamsChange":return!vr(e.url,t.url)||!on(e.queryParams,t.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!nh(e,t)||!on(e.queryParams,t.queryParams);default:return!nh(e,t)}}(s,i,i.routeConfig.runGuardsAndResolvers);l?o.canActivateChecks.push(new Qw(r)):(i.data=s.data,i._resolvedData=s._resolvedData),ss(e,t,i.component?a?a.children:null:n,r,o),l&&a&&a.outlet&&a.outlet.isActivated&&o.canDeactivateChecks.push(new Sl(a.outlet.component,s))}else s&&as(t,a,o),o.canActivateChecks.push(new Qw(r)),ss(e,null,i.component?a?a.children:null:n,r,o)})(s,i[s.value.outlet],n,r.concat([s.value]),o),delete i[s.value.outlet]}),Object.entries(i).forEach(([s,a])=>as(a,n.getContext(s),o)),o}function as(e,t,n){const r=Oo(e),o=e.value;Object.entries(r).forEach(([i,s])=>{as(s,o.component?t?t.children.getContext(i):null:t,n)}),n.canDeactivateChecks.push(new Sl(o.component&&t&&t.outlet&&t.outlet.isActivated?t.outlet.component:null,o))}function ls(e){return"function"==typeof e}function Xw(e){return e instanceof gl||"EmptyError"===e?.name}const xl=Symbol("INITIAL_VALUE");function Ro(){return Nt(e=>Bf(e.map(t=>t.pipe(Io(1),function xF(...e){const t=Uo(e);return Ee((n,r)=>{(t?Hf(e,n,t):Hf(e,n)).subscribe(r)})}(xl)))).pipe(z(t=>{for(const n of t)if(!0!==n){if(n===xl)return xl;if(!1===n||n instanceof Ao)return n}return!0}),In(t=>t!==xl),Io(1)))}function Jw(e){return function f0(...e){return tp(e)}($e(t=>{if(_r(t))throw qw(0,t)}),z(t=>!0===t))}class Al{constructor(t){this.segmentGroup=t||null}}class Kw{constructor(t){this.urlTree=t}}function ko(e){return Wi(new Al(e))}function eb(e){return Wi(new Kw(e))}class rV{constructor(t,n){this.urlSerializer=t,this.urlTree=n}noMatchError(t){return new C(4002,!1)}lineralizeSegments(t,n){let r=[],o=n.root;for(;;){if(r=r.concat(o.segments),0===o.numberOfChildren)return A(r);if(o.numberOfChildren>1||!o.children[V])return Wi(new C(4e3,!1));o=o.children[V]}}applyRedirectCommands(t,n,r){return this.applyRedirectCreateUrlTree(n,this.urlSerializer.parse(n),t,r)}applyRedirectCreateUrlTree(t,n,r,o){const i=this.createSegmentGroup(t,n.root,r,o);return new Ao(i,this.createQueryParams(n.queryParams,this.urlTree.queryParams),n.fragment)}createQueryParams(t,n){const r={};return Object.entries(t).forEach(([o,i])=>{if("string"==typeof i&&i.startsWith(":")){const a=i.substring(1);r[o]=n[a]}else r[o]=i}),r}createSegmentGroup(t,n,r,o){const i=this.createSegments(t,n.segments,r,o);let s={};return Object.entries(n.children).forEach(([a,l])=>{s[a]=this.createSegmentGroup(t,l,r,o)}),new K(i,s)}createSegments(t,n,r,o){return n.map(i=>i.path.startsWith(":")?this.findPosParam(t,i,o):this.findOrReturn(i,r))}findPosParam(t,n,r){const o=r[n.path.substring(1)];if(!o)throw new C(4001,!1);return o}findOrReturn(t,n){let r=0;for(const o of n){if(o.path===t.path)return n.splice(r),o;r++}return t}}const ih={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function oV(e,t,n,r,o){const i=sh(e,t,n);return i.matched?(r=function SL(e,t){return e.providers&&!e._injector&&(e._injector=Ad(e.providers,t,`Route: ${e.path}`)),e._injector??t}(t,r),function eV(e,t,n,r){const o=t.canMatch;return o&&0!==o.length?A(o.map(s=>{const a=Po(s,e);return qn(function UL(e){return e&&ls(e.canMatch)}(a)?a.canMatch(t,n):e.runInContext(()=>a(t,n)))})).pipe(Ro(),Jw()):A(!0)}(r,t,n).pipe(z(s=>!0===s?i:{...ih}))):A(i)}function sh(e,t,n){if(""===t.path)return"full"===t.pathMatch&&(e.hasChildren()||n.length>0)?{...ih}:{matched:!0,consumedSegments:[],remainingSegments:n,parameters:{},positionalParamSegments:{}};const o=(t.matcher||FF)(n,e,t);if(!o)return{...ih};const i={};Object.entries(o.posParams??{}).forEach(([a,l])=>{i[a]=l.path});const s=o.consumed.length>0?{...i,...o.consumed[o.consumed.length-1].parameters}:i;return{matched:!0,consumedSegments:o.consumed,remainingSegments:n.slice(o.consumed.length),parameters:s,positionalParamSegments:o.posParams??{}}}function tb(e,t,n,r){return n.length>0&&function aV(e,t,n){return n.some(r=>Tl(e,t,r)&&zt(r)!==V)}(e,n,r)?{segmentGroup:new K(t,sV(r,new K(n,e.children))),slicedSegments:[]}:0===n.length&&function lV(e,t,n){return n.some(r=>Tl(e,t,r))}(e,n,r)?{segmentGroup:new K(e.segments,iV(e,0,n,r,e.children)),slicedSegments:n}:{segmentGroup:new K(e.segments,e.children),slicedSegments:n}}function iV(e,t,n,r,o){const i={};for(const s of r)if(Tl(e,n,s)&&!o[zt(s)]){const a=new K([],{});i[zt(s)]=a}return{...o,...i}}function sV(e,t){const n={};n[V]=t;for(const r of e)if(""===r.path&&zt(r)!==V){const o=new K([],{});n[zt(r)]=o}return n}function Tl(e,t,n){return(!(e.hasChildren()||t.length>0)||"full"!==n.pathMatch)&&""===n.path}class fV{constructor(t,n,r,o,i,s,a){this.injector=t,this.configLoader=n,this.rootComponentType=r,this.config=o,this.urlTree=i,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.allowRedirects=!0,this.applyRedirects=new rV(this.urlSerializer,this.urlTree)}noMatchError(t){return new C(4002,!1)}recognize(){const t=tb(this.urlTree.root,[],[],this.config).segmentGroup;return this.processSegmentGroup(this.injector,this.config,t,V).pipe(Gn(n=>{if(n instanceof Kw)return this.allowRedirects=!1,this.urlTree=n.urlTree,this.match(n.urlTree);throw n instanceof Al?this.noMatchError(n):n}),z(n=>{const r=new Ml([],Object.freeze({}),Object.freeze({...this.urlTree.queryParams}),this.urlTree.fragment,{},V,this.rootComponentType,null,{}),o=new Sn(r,n),i=new Hw("",o),s=function nL(e,t,n=null,r=null){return Tw(Aw(e),t,n,r)}(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,i.url=this.urlSerializer.serialize(s),this.inheritParamsAndData(i._root),{state:i,tree:s}}))}match(t){return this.processSegmentGroup(this.injector,this.config,t.root,V).pipe(Gn(r=>{throw r instanceof Al?this.noMatchError(r):r}))}inheritParamsAndData(t){const n=t.value,r=Bw(n,this.paramsInheritanceStrategy);n.params=Object.freeze(r.params),n.data=Object.freeze(r.data),t.children.forEach(o=>this.inheritParamsAndData(o))}processSegmentGroup(t,n,r,o){return 0===r.segments.length&&r.hasChildren()?this.processChildren(t,n,r):this.processSegment(t,n,r,r.segments,o,!0)}processChildren(t,n,r){const o=[];for(const i of Object.keys(r.children))"primary"===i?o.unshift(i):o.push(i);return Me(o).pipe(So(i=>{const s=r.children[i],a=function OL(e,t){const n=e.filter(r=>zt(r)===t);return n.push(...e.filter(r=>zt(r)!==t)),n}(n,i);return this.processSegmentGroup(t,a,s,i)}),function OF(e,t){return Ee(function TF(e,t,n,r,o){return(i,s)=>{let a=n,l=t,c=0;i.subscribe(De(s,u=>{const d=c++;l=a?e(l,u,d):(a=!0,u),r&&s.next(l)},o&&(()=>{a&&s.next(l),s.complete()})))}}(e,t,arguments.length>=2,!0))}((i,s)=>(i.push(...s),i)),ml(null),function NF(e,t){const n=arguments.length>=2;return r=>r.pipe(e?In((o,i)=>e(o,i,r)):On,zf(1),n?ml(t):mw(()=>new gl))}(),Ae(i=>{if(null===i)return ko(r);const s=nb(i);return function hV(e){e.sort((t,n)=>t.value.outlet===V?-1:n.value.outlet===V?1:t.value.outlet.localeCompare(n.value.outlet))}(s),A(s)}))}processSegment(t,n,r,o,i,s){return Me(n).pipe(So(a=>this.processSegmentAgainstRoute(a._injector??t,n,a,r,o,i,s).pipe(Gn(l=>{if(l instanceof Al)return A(null);throw l}))),yr(a=>!!a),Gn(a=>{if(Xw(a))return function uV(e,t,n){return 0===t.length&&!e.children[n]}(r,o,i)?A([]):ko(r);throw a}))}processSegmentAgainstRoute(t,n,r,o,i,s,a){return function cV(e,t,n,r){return!!(zt(e)===r||r!==V&&Tl(t,n,e))&&("**"===e.path||sh(t,e,n).matched)}(r,o,i,s)?void 0===r.redirectTo?this.matchSegmentAgainstRoute(t,o,r,i,s,a):a&&this.allowRedirects?this.expandSegmentAgainstRouteUsingRedirect(t,o,n,r,i,s):ko(o):ko(o)}expandSegmentAgainstRouteUsingRedirect(t,n,r,o,i,s){return"**"===o.path?this.expandWildCardWithParamsAgainstRouteUsingRedirect(t,r,o,s):this.expandRegularSegmentAgainstRouteUsingRedirect(t,n,r,o,i,s)}expandWildCardWithParamsAgainstRouteUsingRedirect(t,n,r,o){const i=this.applyRedirects.applyRedirectCommands([],r.redirectTo,{});return r.redirectTo.startsWith("/")?eb(i):this.applyRedirects.lineralizeSegments(r,i).pipe(Ae(s=>{const a=new K(s,{});return this.processSegment(t,n,a,s,o,!1)}))}expandRegularSegmentAgainstRouteUsingRedirect(t,n,r,o,i,s){const{matched:a,consumedSegments:l,remainingSegments:c,positionalParamSegments:u}=sh(n,o,i);if(!a)return ko(n);const d=this.applyRedirects.applyRedirectCommands(l,o.redirectTo,u);return o.redirectTo.startsWith("/")?eb(d):this.applyRedirects.lineralizeSegments(o,d).pipe(Ae(f=>this.processSegment(t,r,n,f.concat(c),s,!1)))}matchSegmentAgainstRoute(t,n,r,o,i,s){let a;if("**"===r.path){const l=o.length>0?vw(o).parameters:{};a=A({snapshot:new Ml(o,l,Object.freeze({...this.urlTree.queryParams}),this.urlTree.fragment,rb(r),zt(r),r.component??r._loadedComponent??null,r,ob(r)),consumedSegments:[],remainingSegments:[]}),n.children={}}else a=oV(n,r,o,t).pipe(z(({matched:l,consumedSegments:c,remainingSegments:u,parameters:d})=>l?{snapshot:new Ml(c,d,Object.freeze({...this.urlTree.queryParams}),this.urlTree.fragment,rb(r),zt(r),r.component??r._loadedComponent??null,r,ob(r)),consumedSegments:c,remainingSegments:u}:null));return a.pipe(Nt(l=>null===l?ko(n):this.getChildConfig(t=r._injector??t,r,o).pipe(Nt(({routes:c})=>{const u=r._loadedInjector??t,{snapshot:d,consumedSegments:f,remainingSegments:h}=l,{segmentGroup:p,slicedSegments:g}=tb(n,f,h,c);if(0===g.length&&p.hasChildren())return this.processChildren(u,c,p).pipe(z(_=>null===_?null:[new Sn(d,_)]));if(0===c.length&&0===g.length)return A([new Sn(d,[])]);const y=zt(r)===i;return this.processSegment(u,c,p,g,y?V:i,!0).pipe(z(_=>[new Sn(d,_)]))}))))}getChildConfig(t,n,r){return n.children?A({routes:n.children,injector:t}):n.loadChildren?void 0!==n._loadedRoutes?A({routes:n._loadedRoutes,injector:n._loadedInjector}):function KL(e,t,n,r){const o=t.canLoad;return void 0===o||0===o.length?A(!0):A(o.map(s=>{const a=Po(s,e);return qn(function jL(e){return e&&ls(e.canLoad)}(a)?a.canLoad(t,n):e.runInContext(()=>a(t,n)))})).pipe(Ro(),Jw())}(t,n,r).pipe(Ae(o=>o?this.configLoader.loadChildren(t,n).pipe($e(i=>{n._loadedRoutes=i.routes,n._loadedInjector=i.injector})):function nV(e){return Wi(Ww(!1,3))}())):A({routes:[],injector:t})}}function pV(e){const t=e.value.routeConfig;return t&&""===t.path}function nb(e){const t=[],n=new Set;for(const r of e){if(!pV(r)){t.push(r);continue}const o=t.find(i=>r.value.routeConfig===i.value.routeConfig);void 0!==o?(o.children.push(...r.children),n.add(o)):t.push(r)}for(const r of n){const o=nb(r.children);t.push(new Sn(r.value,o))}return t.filter(r=>!n.has(r))}function rb(e){return e.data||{}}function ob(e){return e.resolve||{}}function ib(e){return"string"==typeof e.title||null===e.title}function ah(e){return Nt(t=>{const n=e(t);return n?Me(n).pipe(z(()=>t)):A(t)})}const Fo=new M("ROUTES");let lh=(()=>{class e{constructor(){this.componentLoaders=new WeakMap,this.childrenLoaders=new WeakMap,this.compiler=E(jC)}loadComponent(n){if(this.componentLoaders.get(n))return this.componentLoaders.get(n);if(n._loadedComponent)return A(n._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(n);const r=qn(n.loadComponent()).pipe(z(sb),$e(i=>{this.onLoadEndListener&&this.onLoadEndListener(n),n._loadedComponent=i}),Zi(()=>{this.componentLoaders.delete(n)})),o=new gw(r,()=>new Ot).pipe(Uf());return this.componentLoaders.set(n,o),o}loadChildren(n,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return A({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);const i=function DV(e,t,n,r){return qn(e.loadChildren()).pipe(z(sb),Ae(o=>o instanceof U_||Array.isArray(o)?A(o):Me(t.compileModuleAsync(o))),z(o=>{r&&r(e);let i,s,a=!1;return Array.isArray(o)?(s=o,!0):(i=o.create(n).injector,s=i.get(Fo,[],{optional:!0,self:!0}).flat()),{routes:s.map(oh),injector:i}}))}(r,this.compiler,n,this.onLoadEndListener).pipe(Zi(()=>{this.childrenLoaders.delete(r)})),s=new gw(i,()=>new Ot).pipe(Uf());return this.childrenLoaders.set(r,s),s}static#e=this.\u0275fac=function(r){return new(r||e)};static#t=this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function sb(e){return function wV(e){return e&&"object"==typeof e&&"default"in e}(e)?e.default:e}let Ol=(()=>{class e{get hasRequestedNavigation(){return 0!==this.navigationId}constructor(){this.currentNavigation=null,this.currentTransition=null,this.lastSuccessfulNavigation=null,this.events=new Ot,this.transitionAbortSubject=new Ot,this.configLoader=E(lh),this.environmentInjector=E(ht),this.urlSerializer=E(Xi),this.rootContexts=E(rs),this.inputBindingEnabled=null!==E(Il,{optional:!0}),this.navigationId=0,this.afterPreactivation=()=>A(void 0),this.rootComponentType=null,this.configLoader.onLoadEndListener=o=>this.events.next(new pL(o)),this.configLoader.onLoadStartListener=o=>this.events.next(new hL(o))}complete(){this.transitions?.complete()}handleNavigationRequest(n){const r=++this.navigationId;this.transitions?.next({...this.transitions.value,...n,id:r})}setupNavigations(n,r,o){return this.transitions=new Ct({id:0,currentUrlTree:r,currentRawUrl:r,currentBrowserUrl:r,extractedUrl:n.urlHandlingStrategy.extract(r),urlAfterRedirects:n.urlHandlingStrategy.extract(r),rawUrl:r,extras:{},resolve:null,reject:null,promise:Promise.resolve(!0),source:ts,restoredState:null,currentSnapshot:o.snapshot,targetSnapshot:null,currentRouterState:o,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null}),this.transitions.pipe(In(i=>0!==i.id),z(i=>({...i,extractedUrl:n.urlHandlingStrategy.extract(i.rawUrl)})),Nt(i=>{this.currentTransition=i;let s=!1,a=!1;return A(i).pipe($e(l=>{this.currentNavigation={id:l.id,initialUrl:l.rawUrl,extractedUrl:l.extractedUrl,trigger:l.source,extras:l.extras,previousNavigation:this.lastSuccessfulNavigation?{...this.lastSuccessfulNavigation,previousNavigation:null}:null}}),Nt(l=>{const c=l.currentBrowserUrl.toString(),u=!n.navigated||l.extractedUrl.toString()!==c||c!==l.currentUrlTree.toString();if(!u&&"reload"!==(l.extras.onSameUrlNavigation??n.onSameUrlNavigation)){const f="";return this.events.next(new To(l.id,this.urlSerializer.serialize(l.rawUrl),f,0)),l.resolve(null),Gt}if(n.urlHandlingStrategy.shouldProcessUrl(l.rawUrl))return A(l).pipe(Nt(f=>{const h=this.transitions?.getValue();return this.events.next(new bl(f.id,this.urlSerializer.serialize(f.extractedUrl),f.source,f.restoredState)),h!==this.transitions?.getValue()?Gt:Promise.resolve(f)}),function gV(e,t,n,r,o,i){return Ae(s=>function dV(e,t,n,r,o,i,s="emptyOnly"){return new fV(e,t,n,r,o,s,i).recognize()}(e,t,n,r,s.extractedUrl,o,i).pipe(z(({state:a,tree:l})=>({...s,targetSnapshot:a,urlAfterRedirects:l}))))}(this.environmentInjector,this.configLoader,this.rootComponentType,n.config,this.urlSerializer,n.paramsInheritanceStrategy),$e(f=>{i.targetSnapshot=f.targetSnapshot,i.urlAfterRedirects=f.urlAfterRedirects,this.currentNavigation={...this.currentNavigation,finalUrl:f.urlAfterRedirects};const h=new Fw(f.id,this.urlSerializer.serialize(f.extractedUrl),this.urlSerializer.serialize(f.urlAfterRedirects),f.targetSnapshot);this.events.next(h)}));if(u&&n.urlHandlingStrategy.shouldProcessUrl(l.currentRawUrl)){const{id:f,extractedUrl:h,source:p,restoredState:g,extras:y}=l,_=new bl(f,this.urlSerializer.serialize(h),p,g);this.events.next(_);const m=$w(0,this.rootComponentType).snapshot;return this.currentTransition=i={...l,targetSnapshot:m,urlAfterRedirects:h,extras:{...y,skipLocationChange:!1,replaceUrl:!1}},A(i)}{const f="";return this.events.next(new To(l.id,this.urlSerializer.serialize(l.extractedUrl),f,1)),l.resolve(null),Gt}}),$e(l=>{const c=new cL(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects),l.targetSnapshot);this.events.next(c)}),z(l=>(this.currentTransition=i={...l,guards:RL(l.targetSnapshot,l.currentSnapshot,this.rootContexts)},i)),function GL(e,t){return Ae(n=>{const{targetSnapshot:r,currentSnapshot:o,guards:{canActivateChecks:i,canDeactivateChecks:s}}=n;return 0===s.length&&0===i.length?A({...n,guardsResult:!0}):function qL(e,t,n,r){return Me(e).pipe(Ae(o=>function JL(e,t,n,r,o){const i=t&&t.routeConfig?t.routeConfig.canDeactivate:null;return i&&0!==i.length?A(i.map(a=>{const l=is(t)??o,c=Po(a,l);return qn(function HL(e){return e&&ls(e.canDeactivate)}(c)?c.canDeactivate(e,t,n,r):l.runInContext(()=>c(e,t,n,r))).pipe(yr())})).pipe(Ro()):A(!0)}(o.component,o.route,n,t,r)),yr(o=>!0!==o,!0))}(s,r,o,e).pipe(Ae(a=>a&&function VL(e){return"boolean"==typeof e}(a)?function WL(e,t,n,r){return Me(t).pipe(So(o=>Hf(function YL(e,t){return null!==e&&t&&t(new gL(e)),A(!0)}(o.route.parent,r),function ZL(e,t){return null!==e&&t&&t(new yL(e)),A(!0)}(o.route,r),function XL(e,t,n){const r=t[t.length-1],i=t.slice(0,t.length-1).reverse().map(s=>function kL(e){const t=e.routeConfig?e.routeConfig.canActivateChild:null;return t&&0!==t.length?{node:e,guards:t}:null}(s)).filter(s=>null!==s).map(s=>pw(()=>A(s.guards.map(l=>{const c=is(s.node)??n,u=Po(l,c);return qn(function BL(e){return e&&ls(e.canActivateChild)}(u)?u.canActivateChild(r,e):c.runInContext(()=>u(r,e))).pipe(yr())})).pipe(Ro())));return A(i).pipe(Ro())}(e,o.path,n),function QL(e,t,n){const r=t.routeConfig?t.routeConfig.canActivate:null;if(!r||0===r.length)return A(!0);const o=r.map(i=>pw(()=>{const s=is(t)??n,a=Po(i,s);return qn(function $L(e){return e&&ls(e.canActivate)}(a)?a.canActivate(t,e):s.runInContext(()=>a(t,e))).pipe(yr())}));return A(o).pipe(Ro())}(e,o.route,n))),yr(o=>!0!==o,!0))}(r,i,e,t):A(a)),z(a=>({...n,guardsResult:a})))})}(this.environmentInjector,l=>this.events.next(l)),$e(l=>{if(i.guardsResult=l.guardsResult,_r(l.guardsResult))throw qw(0,l.guardsResult);const c=new uL(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects),l.targetSnapshot,!!l.guardsResult);this.events.next(c)}),In(l=>!!l.guardsResult||(this.cancelNavigationTransition(l,"",3),!1)),ah(l=>{if(l.guards.canActivateChecks.length)return A(l).pipe($e(c=>{const u=new dL(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(u)}),Nt(c=>{let u=!1;return A(c).pipe(function mV(e,t){return Ae(n=>{const{targetSnapshot:r,guards:{canActivateChecks:o}}=n;if(!o.length)return A(n);let i=0;return Me(o).pipe(So(s=>function yV(e,t,n,r){const o=e.routeConfig,i=e._resolve;return void 0!==o?.title&&!ib(o)&&(i[Yi]=o.title),function vV(e,t,n,r){const o=function _V(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}(e);if(0===o.length)return A({});const i={};return Me(o).pipe(Ae(s=>function CV(e,t,n,r){const o=is(t)??r,i=Po(e,o);return qn(i.resolve?i.resolve(t,n):o.runInContext(()=>i(t,n)))}(e[s],t,n,r).pipe(yr(),$e(a=>{i[s]=a}))),zf(1),function PF(e){return z(()=>e)}(i),Gn(s=>Xw(s)?Gt:Wi(s)))}(i,e,t,r).pipe(z(s=>(e._resolvedData=s,e.data=Bw(e,n).resolve,o&&ib(o)&&(e.data[Yi]=o.title),null)))}(s.route,r,e,t)),$e(()=>i++),zf(1),Ae(s=>i===o.length?A(n):Gt))})}(n.paramsInheritanceStrategy,this.environmentInjector),$e({next:()=>u=!0,complete:()=>{u||this.cancelNavigationTransition(c,"",2)}}))}),$e(c=>{const u=new fL(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(u)}))}),ah(l=>{const c=u=>{const d=[];u.routeConfig?.loadComponent&&!u.routeConfig._loadedComponent&&d.push(this.configLoader.loadComponent(u.routeConfig).pipe($e(f=>{u.component=f}),z(()=>{})));for(const f of u.children)d.push(...c(f));return d};return Bf(c(l.targetSnapshot.root)).pipe(ml(),Io(1))}),ah(()=>this.afterPreactivation()),z(l=>{const c=function bL(e,t,n){const r=os(e,t._root,n?n._root:void 0);return new jw(r,t)}(n.routeReuseStrategy,l.targetSnapshot,l.currentRouterState);return this.currentTransition=i={...l,targetRouterState:c},i}),$e(()=>{this.events.next(new Qf)}),((e,t,n,r)=>z(o=>(new PL(t,o.targetRouterState,o.currentRouterState,n,r).activate(e),o)))(this.rootContexts,n.routeReuseStrategy,l=>this.events.next(l),this.inputBindingEnabled),Io(1),$e({next:l=>{s=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new Wn(l.id,this.urlSerializer.serialize(l.extractedUrl),this.urlSerializer.serialize(l.urlAfterRedirects))),n.titleStrategy?.updateTitle(l.targetRouterState.snapshot),l.resolve(!0)},complete:()=>{s=!0}}),function RF(e){return Ee((t,n)=>{at(e).subscribe(De(n,()=>n.complete(),ec)),!n.closed&&t.subscribe(n)})}(this.transitionAbortSubject.pipe($e(l=>{throw l}))),Zi(()=>{s||a||this.cancelNavigationTransition(i,"",1),this.currentNavigation?.id===i.id&&(this.currentNavigation=null)}),Gn(l=>{if(a=!0,Zw(l))this.events.next(new ns(i.id,this.urlSerializer.serialize(i.extractedUrl),l.message,l.cancellationCode)),function IL(e){return Zw(e)&&_r(e.url)}(l)?this.events.next(new Xf(l.url)):i.resolve(!1);else{this.events.next(new El(i.id,this.urlSerializer.serialize(i.extractedUrl),l,i.targetSnapshot??void 0));try{i.resolve(n.errorHandler(l))}catch(c){i.reject(c)}}return Gt}))}))}cancelNavigationTransition(n,r,o){const i=new ns(n.id,this.urlSerializer.serialize(n.extractedUrl),r,o);this.events.next(i),n.resolve(!1)}static#e=this.\u0275fac=function(r){return new(r||e)};static#t=this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function ab(e){return e!==ts}let lb=(()=>{class e{buildTitle(n){let r,o=n.root;for(;void 0!==o;)r=this.getResolvedTitleForRoute(o)??r,o=o.children.find(i=>i.outlet===V);return r}getResolvedTitleForRoute(n){return n.data[Yi]}static#e=this.\u0275fac=function(r){return new(r||e)};static#t=this.\u0275prov=x({token:e,factory:function(){return E(bV)},providedIn:"root"})}return e})(),bV=(()=>{class e extends lb{constructor(n){super(),this.title=n}updateTitle(n){const r=this.buildTitle(n);void 0!==r&&this.title.setTitle(r)}static#e=this.\u0275fac=function(r){return new(r||e)(I(sw))};static#t=this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),EV=(()=>{class e{static#e=this.\u0275fac=function(r){return new(r||e)};static#t=this.\u0275prov=x({token:e,factory:function(){return E(IV)},providedIn:"root"})}return e})();class MV{shouldDetach(t){return!1}store(t,n){}shouldAttach(t){return!1}retrieve(t){return null}shouldReuseRoute(t,n){return t.routeConfig===n.routeConfig}}let IV=(()=>{class e extends MV{static#e=this.\u0275fac=function(){let n;return function(o){return(n||(n=Re(e)))(o||e)}}();static#t=this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();const Nl=new M("",{providedIn:"root",factory:()=>({})});let SV=(()=>{class e{static#e=this.\u0275fac=function(r){return new(r||e)};static#t=this.\u0275prov=x({token:e,factory:function(){return E(xV)},providedIn:"root"})}return e})(),xV=(()=>{class e{shouldProcessUrl(n){return!0}extract(n){return n}merge(n,r){return n}static#e=this.\u0275fac=function(r){return new(r||e)};static#t=this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var cs=function(e){return e[e.COMPLETE=0]="COMPLETE",e[e.FAILED=1]="FAILED",e[e.REDIRECTING=2]="REDIRECTING",e}(cs||{});function cb(e,t){e.events.pipe(In(n=>n instanceof Wn||n instanceof ns||n instanceof El||n instanceof To),z(n=>n instanceof Wn||n instanceof To?cs.COMPLETE:n instanceof ns&&(0===n.code||1===n.code)?cs.REDIRECTING:cs.FAILED),In(n=>n!==cs.REDIRECTING),Io(1)).subscribe(()=>{t()})}function AV(e){throw e}function TV(e,t,n){return t.parse("/")}const OV={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},NV={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"};let At=(()=>{class e{get navigationId(){return this.navigationTransitions.navigationId}get browserPageId(){return"computed"!==this.canceledNavigationResolution?this.currentPageId:this.location.getState()?.\u0275routerPageId??this.currentPageId}get events(){return this._events}constructor(){this.disposed=!1,this.currentPageId=0,this.console=E(VC),this.isNgZoneEnabled=!1,this._events=new Ot,this.options=E(Nl,{optional:!0})||{},this.pendingTasks=E(Wa),this.errorHandler=this.options.errorHandler||AV,this.malformedUriErrorHandler=this.options.malformedUriErrorHandler||TV,this.navigated=!1,this.lastSuccessfulId=-1,this.urlHandlingStrategy=E(SV),this.routeReuseStrategy=E(EV),this.titleStrategy=E(lb),this.onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore",this.paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly",this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.canceledNavigationResolution=this.options.canceledNavigationResolution||"replace",this.config=E(Fo,{optional:!0})?.flat()??[],this.navigationTransitions=E(Ol),this.urlSerializer=E(Xi),this.location=E(ff),this.componentInputBindingEnabled=!!E(Il,{optional:!0}),this.eventsSubscription=new st,this.isNgZoneEnabled=E(ie)instanceof ie&&ie.isInAngularZone(),this.resetConfig(this.config),this.currentUrlTree=new Ao,this.rawUrlTree=this.currentUrlTree,this.browserUrlTree=this.currentUrlTree,this.routerState=$w(0,null),this.navigationTransitions.setupNavigations(this,this.currentUrlTree,this.routerState).subscribe(n=>{this.lastSuccessfulId=n.id,this.currentPageId=this.browserPageId},n=>{this.console.warn(`Unhandled Navigation Error: ${n}`)}),this.subscribeToNavigationEvents()}subscribeToNavigationEvents(){const n=this.navigationTransitions.events.subscribe(r=>{try{const{currentTransition:o}=this.navigationTransitions;if(null===o)return void(ub(r)&&this._events.next(r));if(r instanceof bl)ab(o.source)&&(this.browserUrlTree=o.extractedUrl);else if(r instanceof To)this.rawUrlTree=o.rawUrl;else if(r instanceof Fw){if("eager"===this.urlUpdateStrategy){if(!o.extras.skipLocationChange){const i=this.urlHandlingStrategy.merge(o.urlAfterRedirects,o.rawUrl);this.setBrowserUrl(i,o)}this.browserUrlTree=o.urlAfterRedirects}}else if(r instanceof Qf)this.currentUrlTree=o.urlAfterRedirects,this.rawUrlTree=this.urlHandlingStrategy.merge(o.urlAfterRedirects,o.rawUrl),this.routerState=o.targetRouterState,"deferred"===this.urlUpdateStrategy&&(o.extras.skipLocationChange||this.setBrowserUrl(this.rawUrlTree,o),this.browserUrlTree=o.urlAfterRedirects);else if(r instanceof ns)0!==r.code&&1!==r.code&&(this.navigated=!0),(3===r.code||2===r.code)&&this.restoreHistory(o);else if(r instanceof Xf){const i=this.urlHandlingStrategy.merge(r.url,o.currentRawUrl),s={skipLocationChange:o.extras.skipLocationChange,replaceUrl:"eager"===this.urlUpdateStrategy||ab(o.source)};this.scheduleNavigation(i,ts,null,s,{resolve:o.resolve,reject:o.reject,promise:o.promise})}r instanceof El&&this.restoreHistory(o,!0),r instanceof Wn&&(this.navigated=!0),ub(r)&&this._events.next(r)}catch(o){this.navigationTransitions.transitionAbortSubject.next(o)}});this.eventsSubscription.add(n)}resetRootComponentType(n){this.routerState.root.component=n,this.navigationTransitions.rootComponentType=n}initialNavigation(){if(this.setUpLocationChangeListener(),!this.navigationTransitions.hasRequestedNavigation){const n=this.location.getState();this.navigateToSyncWithBrowser(this.location.path(!0),ts,n)}}setUpLocationChangeListener(){this.locationSubscription||(this.locationSubscription=this.location.subscribe(n=>{const r="popstate"===n.type?"popstate":"hashchange";"popstate"===r&&setTimeout(()=>{this.navigateToSyncWithBrowser(n.url,r,n.state)},0)}))}navigateToSyncWithBrowser(n,r,o){const i={replaceUrl:!0},s=o?.navigationId?o:null;if(o){const l={...o};delete l.navigationId,delete l.\u0275routerPageId,0!==Object.keys(l).length&&(i.state=l)}const a=this.parseUrl(n);this.scheduleNavigation(a,r,s,i)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(n){this.config=n.map(oh),this.navigated=!1,this.lastSuccessfulId=-1}ngOnDestroy(){this.dispose()}dispose(){this.navigationTransitions.complete(),this.locationSubscription&&(this.locationSubscription.unsubscribe(),this.locationSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(n,r={}){const{relativeTo:o,queryParams:i,fragment:s,queryParamsHandling:a,preserveFragment:l}=r,c=l?this.currentUrlTree.fragment:s;let d,u=null;switch(a){case"merge":u={...this.currentUrlTree.queryParams,...i};break;case"preserve":u=this.currentUrlTree.queryParams;break;default:u=i||null}null!==u&&(u=this.removeEmptyProps(u));try{d=Aw(o?o.snapshot:this.routerState.snapshot.root)}catch{("string"!=typeof n[0]||!n[0].startsWith("/"))&&(n=[]),d=this.currentUrlTree.root}return Tw(d,n,u,c??null)}navigateByUrl(n,r={skipLocationChange:!1}){const o=_r(n)?n:this.parseUrl(n),i=this.urlHandlingStrategy.merge(o,this.rawUrlTree);return this.scheduleNavigation(i,ts,null,r)}navigate(n,r={skipLocationChange:!1}){return function PV(e){for(let t=0;t<e.length;t++)if(null==e[t])throw new C(4008,!1)}(n),this.navigateByUrl(this.createUrlTree(n,r),r)}serializeUrl(n){return this.urlSerializer.serialize(n)}parseUrl(n){let r;try{r=this.urlSerializer.parse(n)}catch(o){r=this.malformedUriErrorHandler(o,this.urlSerializer,n)}return r}isActive(n,r){let o;if(o=!0===r?{...OV}:!1===r?{...NV}:r,_r(n))return Cw(this.currentUrlTree,n,o);const i=this.parseUrl(n);return Cw(this.currentUrlTree,i,o)}removeEmptyProps(n){return Object.keys(n).reduce((r,o)=>{const i=n[o];return null!=i&&(r[o]=i),r},{})}scheduleNavigation(n,r,o,i,s){if(this.disposed)return Promise.resolve(!1);let a,l,c;s?(a=s.resolve,l=s.reject,c=s.promise):c=new Promise((d,f)=>{a=d,l=f});const u=this.pendingTasks.add();return cb(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(u))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:o,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,currentBrowserUrl:this.browserUrlTree,rawUrl:n,extras:i,resolve:a,reject:l,promise:c,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),c.catch(d=>Promise.reject(d))}setBrowserUrl(n,r){const o=this.urlSerializer.serialize(n);if(this.location.isCurrentPathEqualTo(o)||r.extras.replaceUrl){const s={...r.extras.state,...this.generateNgRouterState(r.id,this.browserPageId)};this.location.replaceState(o,"",s)}else{const i={...r.extras.state,...this.generateNgRouterState(r.id,this.browserPageId+1)};this.location.go(o,"",i)}}restoreHistory(n,r=!1){if("computed"===this.canceledNavigationResolution){const i=this.currentPageId-this.browserPageId;0!==i?this.location.historyGo(i):this.currentUrlTree===this.getCurrentNavigation()?.finalUrl&&0===i&&(this.resetState(n),this.browserUrlTree=n.currentUrlTree,this.resetUrlToCurrentUrlTree())}else"replace"===this.canceledNavigationResolution&&(r&&this.resetState(n),this.resetUrlToCurrentUrlTree())}resetState(n){this.routerState=n.currentRouterState,this.currentUrlTree=n.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,n.rawUrl)}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.rawUrlTree),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(n,r){return"computed"===this.canceledNavigationResolution?{navigationId:n,\u0275routerPageId:r}:{navigationId:n}}static#e=this.\u0275fac=function(r){return new(r||e)};static#t=this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function ub(e){return!(e instanceof Qf||e instanceof Xf)}class db{}let FV=(()=>{class e{constructor(n,r,o,i,s){this.router=n,this.injector=o,this.preloadingStrategy=i,this.loader=s}setUpPreloading(){this.subscription=this.router.events.pipe(In(n=>n instanceof Wn),So(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(n,r){const o=[];for(const i of r){i.providers&&!i._injector&&(i._injector=Ad(i.providers,n,`Route: ${i.path}`));const s=i._injector??n,a=i._loadedInjector??s;(i.loadChildren&&!i._loadedRoutes&&void 0===i.canLoad||i.loadComponent&&!i._loadedComponent)&&o.push(this.preloadConfig(s,i)),(i.children||i._loadedRoutes)&&o.push(this.processRoutes(a,i.children??i._loadedRoutes))}return Me(o).pipe(Sr())}preloadConfig(n,r){return this.preloadingStrategy.preload(r,()=>{let o;o=r.loadChildren&&void 0===r.canLoad?this.loader.loadChildren(n,r):A(null);const i=o.pipe(Ae(s=>null===s?A(void 0):(r._loadedRoutes=s.routes,r._loadedInjector=s.injector,this.processRoutes(s.injector??n,s.routes))));return r.loadComponent&&!r._loadedComponent?Me([i,this.loader.loadComponent(r)]).pipe(Sr()):i})}static#e=this.\u0275fac=function(r){return new(r||e)(I(At),I(jC),I(ht),I(db),I(lh))};static#t=this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();const uh=new M("");let fb=(()=>{class e{constructor(n,r,o,i,s={}){this.urlSerializer=n,this.transitions=r,this.viewportScroller=o,this.zone=i,this.options=s,this.lastId=0,this.lastSource="imperative",this.restoredId=0,this.store={},s.scrollPositionRestoration=s.scrollPositionRestoration||"disabled",s.anchorScrolling=s.anchorScrolling||"disabled"}init(){"disabled"!==this.options.scrollPositionRestoration&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof bl?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=n.navigationTrigger,this.restoredId=n.restoredState?n.restoredState.navigationId:0):n instanceof Wn?(this.lastId=n.id,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.urlAfterRedirects).fragment)):n instanceof To&&0===n.code&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(n,this.urlSerializer.parse(n.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(n=>{n instanceof Lw&&(n.position?"top"===this.options.scrollPositionRestoration?this.viewportScroller.scrollToPosition([0,0]):"enabled"===this.options.scrollPositionRestoration&&this.viewportScroller.scrollToPosition(n.position):n.anchor&&"enabled"===this.options.anchorScrolling?this.viewportScroller.scrollToAnchor(n.anchor):"disabled"!==this.options.scrollPositionRestoration&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(n,r){this.zone.runOutsideAngular(()=>{setTimeout(()=>{this.zone.run(()=>{this.transitions.events.next(new Lw(n,"popstate"===this.lastSource?this.store[this.restoredId]:null,r))})},0)})}ngOnDestroy(){this.routerEventsSubscription?.unsubscribe(),this.scrollEventsSubscription?.unsubscribe()}static#e=this.\u0275fac=function(r){!function Ty(){throw new Error("invalid")}()};static#t=this.\u0275prov=x({token:e,factory:e.\u0275fac})}return e})();function xn(e,t){return{\u0275kind:e,\u0275providers:t}}function pb(){const e=E(gt);return t=>{const n=e.get(bo);if(t!==n.components[0])return;const r=e.get(At),o=e.get(gb);1===e.get(dh)&&r.initialNavigation(),e.get(mb,null,$.Optional)?.setUpPreloading(),e.get(uh,null,$.Optional)?.init(),r.resetRootComponentType(n.componentTypes[0]),o.closed||(o.next(),o.complete(),o.unsubscribe())}}const gb=new M("",{factory:()=>new Ot}),dh=new M("",{providedIn:"root",factory:()=>1}),mb=new M("");function $V(e){return xn(0,[{provide:mb,useExisting:FV},{provide:db,useExisting:e}])}const yb=new M("ROUTER_FORROOT_GUARD"),HV=[ff,{provide:Xi,useClass:Gf},At,rs,{provide:No,useFactory:function hb(e){return e.routerState.root},deps:[At]},lh,[]];function UV(){return new qC("Router",At)}let vb=(()=>{class e{constructor(n){}static forRoot(n,r){return{ngModule:e,providers:[HV,[],{provide:Fo,multi:!0,useValue:n},{provide:yb,useFactory:WV,deps:[[At,new Xs,new Js]]},{provide:Nl,useValue:r||{}},r?.useHash?{provide:mr,useClass:nk}:{provide:mr,useClass:wD},{provide:uh,useFactory:()=>{const e=E(v1),t=E(ie),n=E(Nl),r=E(Ol),o=E(Xi);return n.scrollOffset&&e.setOffset(n.scrollOffset),new fb(o,r,e,t,n)}},r?.preloadingStrategy?$V(r.preloadingStrategy).\u0275providers:[],{provide:qC,multi:!0,useFactory:UV},r?.initialNavigation?ZV(r):[],r?.bindToComponentInputs?xn(8,[Gw,{provide:Il,useExisting:Gw}]).\u0275providers:[],[{provide:_b,useFactory:pb},{provide:ef,multi:!0,useExisting:_b}]]}}static forChild(n){return{ngModule:e,providers:[{provide:Fo,multi:!0,useValue:n}]}}static#e=this.\u0275fac=function(r){return new(r||e)(I(yb,8))};static#t=this.\u0275mod=wt({type:e});static#n=this.\u0275inj=ct({})}return e})();function WV(e){return"guarded"}function ZV(e){return["disabled"===e.initialNavigation?xn(3,[{provide:qd,multi:!0,useFactory:()=>{const t=E(At);return()=>{t.setUpLocationChangeListener()}}},{provide:dh,useValue:2}]).\u0275providers:[],"enabledBlocking"===e.initialNavigation?xn(2,[{provide:dh,useValue:0},{provide:qd,multi:!0,deps:[gt],useFactory:t=>{const n=t.get(ek,Promise.resolve());return()=>n.then(()=>new Promise(r=>{const o=t.get(At),i=t.get(gb);cb(o,()=>{r(!0)}),t.get(Ol).afterPreactivation=()=>(r(!0),i.closed?A(void 0):i),o.initialNavigation()}))}}]).\u0275providers:[]]}const _b=new M(""),QV=[{path:"",redirectTo:"/news"}];let XV=(()=>{class e{static#e=this.\u0275fac=function(r){return new(r||e)};static#t=this.\u0275mod=wt({type:e});static#n=this.\u0275inj=ct({imports:[vb.forRoot(QV),vb]})}return e})();var Cr={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};function Cb(e){Cr=e}var us={exec:()=>null};function Y(e,t=""){let n="string"==typeof e?e:e.source,r={replace:(o,i)=>{let s="string"==typeof i?i:i.source;return s=s.replace(Xe.caret,"$1"),n=n.replace(o,s),r},getRegex:()=>new RegExp(n,t)};return r}var Xe={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:e=>new RegExp(`^( {0,3}${e})((?:[\t ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ \t][^\\n]*)?(?:\\n|$))`),hrRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}#`),htmlBeginRegex:e=>new RegExp(`^ {0,${Math.min(3,e-1)}}<(?:[a-z].*>|!--)`,"i")},ds=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,hh=/(?:[*+-]|\d{1,9}[.)])/,Db=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,wb=Y(Db).replace(/bull/g,hh).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),n2=Y(Db).replace(/bull/g,hh).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),ph=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,gh=/(?!\s*\])(?:\\.|[^\[\]\\])+/,o2=Y(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",gh).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),i2=Y(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,hh).getRegex(),Pl="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",mh=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,s2=Y("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ \t]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ \t]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ \t]*)+\\n|$))","i").replace("comment",mh).replace("tag",Pl).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),bb=Y(ph).replace("hr",ds).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Pl).getRegex(),yh={blockquote:Y(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",bb).getRegex(),code:/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,def:o2,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,hr:ds,html:s2,lheading:wb,list:i2,newline:/^(?:[ \t]*(?:\n|$))+/,paragraph:bb,table:us,text:/^[^\n]+/},Eb=Y("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",ds).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}\t)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Pl).getRegex(),l2={...yh,lheading:n2,table:Eb,paragraph:Y(ph).replace("hr",ds).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",Eb).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",Pl).getRegex()},c2={...yh,html:Y("^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\s[^'\"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))").replace("comment",mh).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:us,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:Y(ph).replace("hr",ds).replace("heading"," *#{1,6} *[^\n]").replace("lheading",wb).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},Mb=/^( {2,}|\\)\n(?!\s*$)/,Rl=/[\p{P}\p{S}]/u,vh=/[\s\p{P}\p{S}]/u,Ib=/[^\s\p{P}\p{S}]/u,h2=Y(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,vh).getRegex(),Sb=/(?!~)[\p{P}\p{S}]/u,xb=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,y2=Y(xb,"u").replace(/punct/g,Rl).getRegex(),v2=Y(xb,"u").replace(/punct/g,Sb).getRegex(),Ab="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",_2=Y(Ab,"gu").replace(/notPunctSpace/g,Ib).replace(/punctSpace/g,vh).replace(/punct/g,Rl).getRegex(),C2=Y(Ab,"gu").replace(/notPunctSpace/g,/(?:[^\s\p{P}\p{S}]|~)/u).replace(/punctSpace/g,/(?!~)[\s\p{P}\p{S}]/u).replace(/punct/g,Sb).getRegex(),D2=Y("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,Ib).replace(/punctSpace/g,vh).replace(/punct/g,Rl).getRegex(),w2=Y(/\\(punct)/,"gu").replace(/punct/g,Rl).getRegex(),b2=Y(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),E2=Y(mh).replace("(?:--\x3e|$)","--\x3e").getRegex(),M2=Y("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",E2).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),kl=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,I2=Y(/^!?\[(label)\]\(\s*(href)(?:(?:[ \t]*(?:\n[ \t]*)?)(title))?\s*\)/).replace("label",kl).replace("href",/<(?:\\.|[^\n<>\\])+>|[^ \t\n\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),Tb=Y(/^!?\[(label)\]\[(ref)\]/).replace("label",kl).replace("ref",gh).getRegex(),Ob=Y(/^!?\[(ref)\](?:\[\])?/).replace("ref",gh).getRegex(),_h={_backpedal:us,anyPunctuation:w2,autolink:b2,blockSkip:/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<(?! )[^<>]*?>/g,br:Mb,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,del:us,emStrongLDelim:y2,emStrongRDelimAst:_2,emStrongRDelimUnd:D2,escape:/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,link:I2,nolink:Ob,punctuation:h2,reflink:Tb,reflinkSearch:Y("reflink|nolink(?!\\()","g").replace("reflink",Tb).replace("nolink",Ob).getRegex(),tag:M2,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,url:us},x2={..._h,link:Y(/^!?\[(label)\]\((.*?)\)/).replace("label",kl).getRegex(),reflink:Y(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",kl).getRegex()},Ch={..._h,emStrongRDelimAst:C2,emStrongLDelim:v2,url:Y(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},A2={...Ch,br:Y(Mb).replace("{2,}","*").getRegex(),text:Y(Ch.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},Fl={normal:yh,gfm:l2,pedantic:c2},fs={normal:_h,gfm:Ch,breaks:A2,pedantic:x2},T2={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Nb=e=>T2[e];function an(e,t){if(t){if(Xe.escapeTest.test(e))return e.replace(Xe.escapeReplace,Nb)}else if(Xe.escapeTestNoEncode.test(e))return e.replace(Xe.escapeReplaceNoEncode,Nb);return e}function Pb(e){try{e=encodeURI(e).replace(Xe.percentDecode,"%")}catch{return null}return e}function Rb(e,t){let r=e.replace(Xe.findPipe,(i,s,a)=>{let l=!1,c=s;for(;--c>=0&&"\\"===a[c];)l=!l;return l?"|":" |"}).split(Xe.splitPipe),o=0;if(r[0].trim()||r.shift(),r.length>0&&!r.at(-1)?.trim()&&r.pop(),t)if(r.length>t)r.splice(t);else for(;r.length<t;)r.push("");for(;o<r.length;o++)r[o]=r[o].trim().replace(Xe.slashPipe,"|");return r}function hs(e,t,n){let r=e.length;if(0===r)return"";let o=0;for(;o<r;){let i=e.charAt(r-o-1);if(i!==t||n){if(i===t||!n)break;o++}else o++}return e.slice(0,r-o)}function kb(e,t,n,r,o){let i=t.href,s=t.title||null,a=e[1].replace(o.other.outputLinkReplace,"$1");r.state.inLink=!0;let l={type:"!"===e[0].charAt(0)?"image":"link",raw:n,href:i,title:s,text:a,tokens:r.inlineTokens(a)};return r.state.inLink=!1,l}var Ll=class{options;rules;lexer;constructor(e){this.options=e||Cr}space(e){let t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){let t=this.rules.block.code.exec(e);if(t){let n=t[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?n:hs(n,"\n")}}}fences(e){let t=this.rules.block.fences.exec(e);if(t){let n=t[0],r=function N2(e,t,n){let r=e.match(n.other.indentCodeCompensation);if(null===r)return t;let o=r[1];return t.split("\n").map(i=>{let s=i.match(n.other.beginningSpace);if(null===s)return i;let[a]=s;return a.length>=o.length?i.slice(o.length):i}).join("\n")}(n,t[3]||"",this.rules);return{type:"code",raw:n,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:r}}}heading(e){let t=this.rules.block.heading.exec(e);if(t){let n=t[2].trim();if(this.rules.other.endingHash.test(n)){let r=hs(n,"#");(this.options.pedantic||!r||this.rules.other.endingSpaceChar.test(r))&&(n=r.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:n,tokens:this.lexer.inline(n)}}}hr(e){let t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:hs(t[0],"\n")}}blockquote(e){let t=this.rules.block.blockquote.exec(e);if(t){let n=hs(t[0],"\n").split("\n"),r="",o="",i=[];for(;n.length>0;){let l,s=!1,a=[];for(l=0;l<n.length;l++)if(this.rules.other.blockquoteStart.test(n[l]))a.push(n[l]),s=!0;else{if(s)break;a.push(n[l])}n=n.slice(l);let c=a.join("\n"),u=c.replace(this.rules.other.blockquoteSetextReplace,"\n    $1").replace(this.rules.other.blockquoteSetextReplace2,"");r=r?`${r}\n${c}`:c,o=o?`${o}\n${u}`:u;let d=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(u,i,!0),this.lexer.state.top=d,0===n.length)break;let f=i.at(-1);if("code"===f?.type)break;if("blockquote"===f?.type){let h=f,p=h.raw+"\n"+n.join("\n"),g=this.blockquote(p);i[i.length-1]=g,r=r.substring(0,r.length-h.raw.length)+g.raw,o=o.substring(0,o.length-h.text.length)+g.text;break}if("list"!==f?.type);else{let h=f,p=h.raw+"\n"+n.join("\n"),g=this.list(p);i[i.length-1]=g,r=r.substring(0,r.length-f.raw.length)+g.raw,o=o.substring(0,o.length-h.raw.length)+g.raw,n=p.substring(i.at(-1).raw.length).split("\n")}}return{type:"blockquote",raw:r,tokens:i,text:o}}}list(e){let t=this.rules.block.list.exec(e);if(t){let n=t[1].trim(),r=n.length>1,o={type:"list",raw:"",ordered:r,start:r?+n.slice(0,-1):"",loose:!1,items:[]};n=r?`\\d{1,9}\\${n.slice(-1)}`:`\\${n}`,this.options.pedantic&&(n=r?n:"[*+-]");let i=this.rules.other.listItemRegex(n),s=!1;for(;e;){let l=!1,c="",u="";if(!(t=i.exec(e))||this.rules.block.hr.test(e))break;c=t[0],e=e.substring(c.length);let d=t[2].split("\n",1)[0].replace(this.rules.other.listReplaceTabs,_=>" ".repeat(3*_.length)),f=e.split("\n",1)[0],h=!d.trim(),p=0;if(this.options.pedantic?(p=2,u=d.trimStart()):h?p=t[1].length+1:(p=t[2].search(this.rules.other.nonSpaceChar),p=p>4?1:p,u=d.slice(p),p+=t[1].length),h&&this.rules.other.blankLine.test(f)&&(c+=f+"\n",e=e.substring(f.length+1),l=!0),!l){let _=this.rules.other.nextBulletRegex(p),m=this.rules.other.hrRegex(p),w=this.rules.other.fencesBeginRegex(p),S=this.rules.other.headingBeginRegex(p),j=this.rules.other.htmlBeginRegex(p);for(;e;){let He,ye=e.split("\n",1)[0];if(f=ye,this.options.pedantic?(f=f.replace(this.rules.other.listReplaceNesting,"  "),He=f):He=f.replace(this.rules.other.tabCharGlobal,"    "),w.test(f)||S.test(f)||j.test(f)||_.test(f)||m.test(f))break;if(He.search(this.rules.other.nonSpaceChar)>=p||!f.trim())u+="\n"+He.slice(p);else{if(h||d.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||w.test(d)||S.test(d)||m.test(d))break;u+="\n"+f}!h&&!f.trim()&&(h=!0),c+=ye+"\n",e=e.substring(ye.length+1),d=He.slice(p)}}o.loose||(s?o.loose=!0:this.rules.other.doubleBlankLine.test(c)&&(s=!0));let y,g=null;this.options.gfm&&(g=this.rules.other.listIsTask.exec(u),g&&(y="[ ] "!==g[0],u=u.replace(this.rules.other.listReplaceTask,""))),o.items.push({type:"list_item",raw:c,task:!!g,checked:y,loose:!1,text:u,tokens:[]}),o.raw+=c}let a=o.items.at(-1);if(!a)return;a.raw=a.raw.trimEnd(),a.text=a.text.trimEnd(),o.raw=o.raw.trimEnd();for(let l=0;l<o.items.length;l++)if(this.lexer.state.top=!1,o.items[l].tokens=this.lexer.blockTokens(o.items[l].text,[]),!o.loose){let c=o.items[l].tokens.filter(d=>"space"===d.type),u=c.length>0&&c.some(d=>this.rules.other.anyLine.test(d.raw));o.loose=u}if(o.loose)for(let l=0;l<o.items.length;l++)o.items[l].loose=!0;return o}}html(e){let t=this.rules.block.html.exec(e);if(t)return{type:"html",block:!0,raw:t[0],pre:"pre"===t[1]||"script"===t[1]||"style"===t[1],text:t[0]}}def(e){let t=this.rules.block.def.exec(e);if(t){let n=t[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),r=t[2]?t[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",o=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:n,raw:t[0],href:r,title:o}}}table(e){let t=this.rules.block.table.exec(e);if(!t||!this.rules.other.tableDelimiter.test(t[2]))return;let n=Rb(t[1]),r=t[2].replace(this.rules.other.tableAlignChars,"").split("|"),o=t[3]?.trim()?t[3].replace(this.rules.other.tableRowBlankLine,"").split("\n"):[],i={type:"table",raw:t[0],header:[],align:[],rows:[]};if(n.length===r.length){for(let s of r)this.rules.other.tableAlignRight.test(s)?i.align.push("right"):this.rules.other.tableAlignCenter.test(s)?i.align.push("center"):this.rules.other.tableAlignLeft.test(s)?i.align.push("left"):i.align.push(null);for(let s=0;s<n.length;s++)i.header.push({text:n[s],tokens:this.lexer.inline(n[s]),header:!0,align:i.align[s]});for(let s of o)i.rows.push(Rb(s,i.header.length).map((a,l)=>({text:a,tokens:this.lexer.inline(a),header:!1,align:i.align[l]})));return i}}lheading(e){let t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:"="===t[2].charAt(0)?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){let t=this.rules.block.paragraph.exec(e);if(t){let n="\n"===t[1].charAt(t[1].length-1)?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:n,tokens:this.lexer.inline(n)}}}text(e){let t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){let t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:t[1]}}tag(e){let t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&this.rules.other.startATag.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(e){let t=this.rules.inline.link.exec(e);if(t){let n=t[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(n)){if(!this.rules.other.endAngleBracket.test(n))return;let i=hs(n.slice(0,-1),"\\");if((n.length-i.length)%2==0)return}else{let i=function O2(e,t){if(-1===e.indexOf(t[1]))return-1;let n=0;for(let r=0;r<e.length;r++)if("\\"===e[r])r++;else if(e[r]===t[0])n++;else if(e[r]===t[1]&&(n--,n<0))return r;return n>0?-2:-1}(t[2],"()");if(-2===i)return;if(i>-1){let s=(0===t[0].indexOf("!")?5:4)+t[1].length+i;t[2]=t[2].substring(0,i),t[0]=t[0].substring(0,s).trim(),t[3]=""}}let r=t[2],o="";if(this.options.pedantic){let i=this.rules.other.pedanticHrefTitle.exec(r);i&&(r=i[1],o=i[3])}else o=t[3]?t[3].slice(1,-1):"";return r=r.trim(),this.rules.other.startAngleBracket.test(r)&&(r=this.options.pedantic&&!this.rules.other.endAngleBracket.test(n)?r.slice(1):r.slice(1,-1)),kb(t,{href:r&&r.replace(this.rules.inline.anyPunctuation,"$1"),title:o&&o.replace(this.rules.inline.anyPunctuation,"$1")},t[0],this.lexer,this.rules)}}reflink(e,t){let n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){let o=t[(n[2]||n[1]).replace(this.rules.other.multipleSpaceGlobal," ").toLowerCase()];if(!o){let i=n[0].charAt(0);return{type:"text",raw:i,text:i}}return kb(n,o,n[0],this.lexer,this.rules)}}emStrong(e,t,n=""){let r=this.rules.inline.emStrongLDelim.exec(e);if(!(!r||r[3]&&n.match(this.rules.other.unicodeAlphaNumeric))&&(!r[1]&&!r[2]||!n||this.rules.inline.punctuation.exec(n))){let i,s,o=[...r[0]].length-1,a=o,l=0,c="*"===r[0][0]?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(c.lastIndex=0,t=t.slice(-1*e.length+o);null!=(r=c.exec(t));){if(i=r[1]||r[2]||r[3]||r[4]||r[5]||r[6],!i)continue;if(s=[...i].length,r[3]||r[4]){a+=s;continue}if((r[5]||r[6])&&o%3&&!((o+s)%3)){l+=s;continue}if(a-=s,a>0)continue;s=Math.min(s,s+a+l);let u=[...r[0]][0].length,d=e.slice(0,o+r.index+u+s);if(Math.min(o,s)%2){let h=d.slice(1,-1);return{type:"em",raw:d,text:h,tokens:this.lexer.inlineTokens(h)}}let f=d.slice(2,-2);return{type:"strong",raw:d,text:f,tokens:this.lexer.inlineTokens(f)}}}}codespan(e){let t=this.rules.inline.code.exec(e);if(t){let n=t[2].replace(this.rules.other.newLineCharGlobal," "),r=this.rules.other.nonSpaceChar.test(n),o=this.rules.other.startingSpaceChar.test(n)&&this.rules.other.endingSpaceChar.test(n);return r&&o&&(n=n.substring(1,n.length-1)),{type:"codespan",raw:t[0],text:n}}}br(e){let t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){let t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e){let t=this.rules.inline.autolink.exec(e);if(t){let n,r;return"@"===t[2]?(n=t[1],r="mailto:"+n):(n=t[1],r=n),{type:"link",raw:t[0],text:n,href:r,tokens:[{type:"text",raw:n,text:n}]}}}url(e){let t;if(t=this.rules.inline.url.exec(e)){let n,r;if("@"===t[2])n=t[0],r="mailto:"+n;else{let o;do{o=t[0],t[0]=this.rules.inline._backpedal.exec(t[0])?.[0]??""}while(o!==t[0]);n=t[0],r="www."===t[1]?"http://"+t[0]:t[0]}return{type:"link",raw:t[0],text:n,href:r,tokens:[{type:"text",raw:n,text:n}]}}}inlineText(e){let t=this.rules.inline.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],escaped:this.lexer.state.inRawBlock}}},An=class Yh{tokens;options;state;tokenizer;inlineQueue;constructor(t){this.tokens=[],this.tokens.links=Object.create(null),this.options=t||Cr,this.options.tokenizer=this.options.tokenizer||new Ll,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};let n={other:Xe,block:Fl.normal,inline:fs.normal};this.options.pedantic?(n.block=Fl.pedantic,n.inline=fs.pedantic):this.options.gfm&&(n.block=Fl.gfm,n.inline=this.options.breaks?fs.breaks:fs.gfm),this.tokenizer.rules=n}static get rules(){return{block:Fl,inline:fs}}static lex(t,n){return new Yh(n).lex(t)}static lexInline(t,n){return new Yh(n).inlineTokens(t)}lex(t){t=t.replace(Xe.carriageReturn,"\n"),this.blockTokens(t,this.tokens);for(let n=0;n<this.inlineQueue.length;n++){let r=this.inlineQueue[n];this.inlineTokens(r.src,r.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(t,n=[],r=!1){for(this.options.pedantic&&(t=t.replace(Xe.tabCharGlobal,"    ").replace(Xe.spaceLine,""));t;){let o;if(this.options.extensions?.block?.some(s=>!!(o=s.call({lexer:this},t,n))&&(t=t.substring(o.raw.length),n.push(o),!0)))continue;if(o=this.tokenizer.space(t)){t=t.substring(o.raw.length);let s=n.at(-1);1===o.raw.length&&void 0!==s?s.raw+="\n":n.push(o);continue}if(o=this.tokenizer.code(t)){t=t.substring(o.raw.length);let s=n.at(-1);"paragraph"===s?.type||"text"===s?.type?(s.raw+="\n"+o.raw,s.text+="\n"+o.text,this.inlineQueue.at(-1).src=s.text):n.push(o);continue}if(o=this.tokenizer.fences(t)){t=t.substring(o.raw.length),n.push(o);continue}if(o=this.tokenizer.heading(t)){t=t.substring(o.raw.length),n.push(o);continue}if(o=this.tokenizer.hr(t)){t=t.substring(o.raw.length),n.push(o);continue}if(o=this.tokenizer.blockquote(t)){t=t.substring(o.raw.length),n.push(o);continue}if(o=this.tokenizer.list(t)){t=t.substring(o.raw.length),n.push(o);continue}if(o=this.tokenizer.html(t)){t=t.substring(o.raw.length),n.push(o);continue}if(o=this.tokenizer.def(t)){t=t.substring(o.raw.length);let s=n.at(-1);"paragraph"===s?.type||"text"===s?.type?(s.raw+="\n"+o.raw,s.text+="\n"+o.raw,this.inlineQueue.at(-1).src=s.text):this.tokens.links[o.tag]||(this.tokens.links[o.tag]={href:o.href,title:o.title});continue}if(o=this.tokenizer.table(t)){t=t.substring(o.raw.length),n.push(o);continue}if(o=this.tokenizer.lheading(t)){t=t.substring(o.raw.length),n.push(o);continue}let i=t;if(this.options.extensions?.startBlock){let l,s=1/0,a=t.slice(1);this.options.extensions.startBlock.forEach(c=>{l=c.call({lexer:this},a),"number"==typeof l&&l>=0&&(s=Math.min(s,l))}),s<1/0&&s>=0&&(i=t.substring(0,s+1))}if(this.state.top&&(o=this.tokenizer.paragraph(i))){let s=n.at(-1);r&&"paragraph"===s?.type?(s.raw+="\n"+o.raw,s.text+="\n"+o.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=s.text):n.push(o),r=i.length!==t.length,t=t.substring(o.raw.length)}else if(o=this.tokenizer.text(t)){t=t.substring(o.raw.length);let s=n.at(-1);"text"===s?.type?(s.raw+="\n"+o.raw,s.text+="\n"+o.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=s.text):n.push(o)}else if(t){let s="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(s);break}throw new Error(s)}}return this.state.top=!0,n}inline(t,n=[]){return this.inlineQueue.push({src:t,tokens:n}),n}inlineTokens(t,n=[]){let r=t,o=null;if(this.tokens.links){let a=Object.keys(this.tokens.links);if(a.length>0)for(;null!=(o=this.tokenizer.rules.inline.reflinkSearch.exec(r));)a.includes(o[0].slice(o[0].lastIndexOf("[")+1,-1))&&(r=r.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+r.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;null!=(o=this.tokenizer.rules.inline.anyPunctuation.exec(r));)r=r.slice(0,o.index)+"++"+r.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;null!=(o=this.tokenizer.rules.inline.blockSkip.exec(r));)r=r.slice(0,o.index)+"["+"a".repeat(o[0].length-2)+"]"+r.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);let i=!1,s="";for(;t;){let a;if(i||(s=""),i=!1,this.options.extensions?.inline?.some(c=>!!(a=c.call({lexer:this},t,n))&&(t=t.substring(a.raw.length),n.push(a),!0)))continue;if(a=this.tokenizer.escape(t)){t=t.substring(a.raw.length),n.push(a);continue}if(a=this.tokenizer.tag(t)){t=t.substring(a.raw.length),n.push(a);continue}if(a=this.tokenizer.link(t)){t=t.substring(a.raw.length),n.push(a);continue}if(a=this.tokenizer.reflink(t,this.tokens.links)){t=t.substring(a.raw.length);let c=n.at(-1);"text"===a.type&&"text"===c?.type?(c.raw+=a.raw,c.text+=a.text):n.push(a);continue}if(a=this.tokenizer.emStrong(t,r,s)){t=t.substring(a.raw.length),n.push(a);continue}if(a=this.tokenizer.codespan(t)){t=t.substring(a.raw.length),n.push(a);continue}if(a=this.tokenizer.br(t)){t=t.substring(a.raw.length),n.push(a);continue}if(a=this.tokenizer.del(t)){t=t.substring(a.raw.length),n.push(a);continue}if(a=this.tokenizer.autolink(t)){t=t.substring(a.raw.length),n.push(a);continue}if(!this.state.inLink&&(a=this.tokenizer.url(t))){t=t.substring(a.raw.length),n.push(a);continue}let l=t;if(this.options.extensions?.startInline){let d,c=1/0,u=t.slice(1);this.options.extensions.startInline.forEach(f=>{d=f.call({lexer:this},u),"number"==typeof d&&d>=0&&(c=Math.min(c,d))}),c<1/0&&c>=0&&(l=t.substring(0,c+1))}if(a=this.tokenizer.inlineText(l)){t=t.substring(a.raw.length),"_"!==a.raw.slice(-1)&&(s=a.raw.slice(-1)),i=!0;let c=n.at(-1);"text"===c?.type?(c.raw+=a.raw,c.text+=a.text):n.push(a)}else if(t){let c="Infinite loop on byte: "+t.charCodeAt(0);if(this.options.silent){console.error(c);break}throw new Error(c)}}return n}},Vl=class{options;parser;constructor(e){this.options=e||Cr}space(e){return""}code({text:e,lang:t,escaped:n}){let r=(t||"").match(Xe.notSpaceStart)?.[0],o=e.replace(Xe.endingNewline,"")+"\n";return r?'<pre><code class="language-'+an(r)+'">'+(n?o:an(o,!0))+"</code></pre>\n":"<pre><code>"+(n?o:an(o,!0))+"</code></pre>\n"}blockquote({tokens:e}){return`<blockquote>\n${this.parser.parse(e)}</blockquote>\n`}html({text:e}){return e}heading({tokens:e,depth:t}){return`<h${t}>${this.parser.parseInline(e)}</h${t}>\n`}hr(e){return"<hr>\n"}list(e){let t=e.ordered,n=e.start,r="";for(let s=0;s<e.items.length;s++)r+=this.listitem(e.items[s]);let o=t?"ol":"ul";return"<"+o+(t&&1!==n?' start="'+n+'"':"")+">\n"+r+"</"+o+">\n"}listitem(e){let t="";if(e.task){let n=this.checkbox({checked:!!e.checked});e.loose?"paragraph"===e.tokens[0]?.type?(e.tokens[0].text=n+" "+e.tokens[0].text,e.tokens[0].tokens&&e.tokens[0].tokens.length>0&&"text"===e.tokens[0].tokens[0].type&&(e.tokens[0].tokens[0].text=n+" "+an(e.tokens[0].tokens[0].text),e.tokens[0].tokens[0].escaped=!0)):e.tokens.unshift({type:"text",raw:n+" ",text:n+" ",escaped:!0}):t+=n+" "}return t+=this.parser.parse(e.tokens,!!e.loose),`<li>${t}</li>\n`}checkbox({checked:e}){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:e}){return`<p>${this.parser.parseInline(e)}</p>\n`}table(e){let t="",n="";for(let o=0;o<e.header.length;o++)n+=this.tablecell(e.header[o]);t+=this.tablerow({text:n});let r="";for(let o=0;o<e.rows.length;o++){let i=e.rows[o];n="";for(let s=0;s<i.length;s++)n+=this.tablecell(i[s]);r+=this.tablerow({text:n})}return r&&(r=`<tbody>${r}</tbody>`),"<table>\n<thead>\n"+t+"</thead>\n"+r+"</table>\n"}tablerow({text:e}){return`<tr>\n${e}</tr>\n`}tablecell(e){let t=this.parser.parseInline(e.tokens),n=e.header?"th":"td";return(e.align?`<${n} align="${e.align}">`:`<${n}>`)+t+`</${n}>\n`}strong({tokens:e}){return`<strong>${this.parser.parseInline(e)}</strong>`}em({tokens:e}){return`<em>${this.parser.parseInline(e)}</em>`}codespan({text:e}){return`<code>${an(e,!0)}</code>`}br(e){return"<br>"}del({tokens:e}){return`<del>${this.parser.parseInline(e)}</del>`}link({href:e,title:t,tokens:n}){let r=this.parser.parseInline(n),o=Pb(e);if(null===o)return r;let i='<a href="'+(e=o)+'"';return t&&(i+=' title="'+an(t)+'"'),i+=">"+r+"</a>",i}image({href:e,title:t,text:n,tokens:r}){r&&(n=this.parser.parseInline(r,this.parser.textRenderer));let o=Pb(e);if(null===o)return an(n);let i=`<img src="${e=o}" alt="${n}"`;return t&&(i+=` title="${an(t)}"`),i+=">",i}text(e){return"tokens"in e&&e.tokens?this.parser.parseInline(e.tokens):"escaped"in e&&e.escaped?e.text:an(e.text)}},Dh=class{strong({text:e}){return e}em({text:e}){return e}codespan({text:e}){return e}del({text:e}){return e}html({text:e}){return e}text({text:e}){return e}link({text:e}){return""+e}image({text:e}){return""+e}br(){return""}},Tn=class Qh{options;renderer;textRenderer;constructor(t){this.options=t||Cr,this.options.renderer=this.options.renderer||new Vl,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new Dh}static parse(t,n){return new Qh(n).parse(t)}static parseInline(t,n){return new Qh(n).parseInline(t)}parse(t,n=!0){let r="";for(let o=0;o<t.length;o++){let i=t[o];if(this.options.extensions?.renderers?.[i.type]){let a=i,l=this.options.extensions.renderers[a.type].call({parser:this},a);if(!1!==l||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(a.type)){r+=l||"";continue}}let s=i;switch(s.type){case"space":r+=this.renderer.space(s);continue;case"hr":r+=this.renderer.hr(s);continue;case"heading":r+=this.renderer.heading(s);continue;case"code":r+=this.renderer.code(s);continue;case"table":r+=this.renderer.table(s);continue;case"blockquote":r+=this.renderer.blockquote(s);continue;case"list":r+=this.renderer.list(s);continue;case"html":r+=this.renderer.html(s);continue;case"paragraph":r+=this.renderer.paragraph(s);continue;case"text":{let a=s,l=this.renderer.text(a);for(;o+1<t.length&&"text"===t[o+1].type;)a=t[++o],l+="\n"+this.renderer.text(a);r+=n?this.renderer.paragraph({type:"paragraph",raw:l,text:l,tokens:[{type:"text",raw:l,text:l,escaped:!0}]}):l;continue}default:{let a='Token with "'+s.type+'" type was not found.';if(this.options.silent)return console.error(a),"";throw new Error(a)}}}return r}parseInline(t,n=this.renderer){let r="";for(let o=0;o<t.length;o++){let i=t[o];if(this.options.extensions?.renderers?.[i.type]){let a=this.options.extensions.renderers[i.type].call({parser:this},i);if(!1!==a||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(i.type)){r+=a||"";continue}}let s=i;switch(s.type){case"escape":case"text":r+=n.text(s);break;case"html":r+=n.html(s);break;case"link":r+=n.link(s);break;case"image":r+=n.image(s);break;case"strong":r+=n.strong(s);break;case"em":r+=n.em(s);break;case"codespan":r+=n.codespan(s);break;case"br":r+=n.br(s);break;case"del":r+=n.del(s);break;default:{let a='Token with "'+s.type+'" type was not found.';if(this.options.silent)return console.error(a),"";throw new Error(a)}}}return r}},jl=class{options;block;constructor(e){this.options=e||Cr}static passThroughHooks=new Set(["preprocess","postprocess","processAllTokens"]);preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}provideLexer(){return this.block?An.lex:An.lexInline}provideParser(){return this.block?Tn.parse:Tn.parseInline}},Dr=new class{defaults={async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null};options=this.setOptions;parse=this.parseMarkdown(!0);parseInline=this.parseMarkdown(!1);Parser=Tn;Renderer=Vl;TextRenderer=Dh;Lexer=An;Tokenizer=Ll;Hooks=jl;constructor(...e){this.use(...e)}walkTokens(e,t){let n=[];for(let r of e)switch(n=n.concat(t.call(this,r)),r.type){case"table":{let o=r;for(let i of o.header)n=n.concat(this.walkTokens(i.tokens,t));for(let i of o.rows)for(let s of i)n=n.concat(this.walkTokens(s.tokens,t));break}case"list":n=n.concat(this.walkTokens(r.items,t));break;default:{let o=r;this.defaults.extensions?.childTokens?.[o.type]?this.defaults.extensions.childTokens[o.type].forEach(i=>{let s=o[i].flat(1/0);n=n.concat(this.walkTokens(s,t))}):o.tokens&&(n=n.concat(this.walkTokens(o.tokens,t)))}}return n}use(...e){let t=this.defaults.extensions||{renderers:{},childTokens:{}};return e.forEach(n=>{let r={...n};if(r.async=this.defaults.async||r.async||!1,n.extensions&&(n.extensions.forEach(o=>{if(!o.name)throw new Error("extension name required");if("renderer"in o){let i=t.renderers[o.name];t.renderers[o.name]=i?function(...s){let a=o.renderer.apply(this,s);return!1===a&&(a=i.apply(this,s)),a}:o.renderer}if("tokenizer"in o){if(!o.level||"block"!==o.level&&"inline"!==o.level)throw new Error("extension level must be 'block' or 'inline'");let i=t[o.level];i?i.unshift(o.tokenizer):t[o.level]=[o.tokenizer],o.start&&("block"===o.level?t.startBlock?t.startBlock.push(o.start):t.startBlock=[o.start]:"inline"===o.level&&(t.startInline?t.startInline.push(o.start):t.startInline=[o.start]))}"childTokens"in o&&o.childTokens&&(t.childTokens[o.name]=o.childTokens)}),r.extensions=t),n.renderer){let o=this.defaults.renderer||new Vl(this.defaults);for(let i in n.renderer){if(!(i in o))throw new Error(`renderer '${i}' does not exist`);if(["options","parser"].includes(i))continue;let a=n.renderer[i],l=o[i];o[i]=(...c)=>{let u=a.apply(o,c);return!1===u&&(u=l.apply(o,c)),u||""}}r.renderer=o}if(n.tokenizer){let o=this.defaults.tokenizer||new Ll(this.defaults);for(let i in n.tokenizer){if(!(i in o))throw new Error(`tokenizer '${i}' does not exist`);if(["options","rules","lexer"].includes(i))continue;let a=n.tokenizer[i],l=o[i];o[i]=(...c)=>{let u=a.apply(o,c);return!1===u&&(u=l.apply(o,c)),u}}r.tokenizer=o}if(n.hooks){let o=this.defaults.hooks||new jl;for(let i in n.hooks){if(!(i in o))throw new Error(`hook '${i}' does not exist`);if(["options","block"].includes(i))continue;let a=n.hooks[i],l=o[i];o[i]=jl.passThroughHooks.has(i)?c=>{if(this.defaults.async)return Promise.resolve(a.call(o,c)).then(d=>l.call(o,d));let u=a.call(o,c);return l.call(o,u)}:(...c)=>{let u=a.apply(o,c);return!1===u&&(u=l.apply(o,c)),u}}r.hooks=o}if(n.walkTokens){let o=this.defaults.walkTokens,i=n.walkTokens;r.walkTokens=function(s){let a=[];return a.push(i.call(this,s)),o&&(a=a.concat(o.call(this,s))),a}}this.defaults={...this.defaults,...r}}),this}setOptions(e){return this.defaults={...this.defaults,...e},this}lexer(e,t){return An.lex(e,t??this.defaults)}parser(e,t){return Tn.parse(e,t??this.defaults)}parseMarkdown(e){return(t,n)=>{let r={...n},o={...this.defaults,...r},i=this.onError(!!o.silent,!!o.async);if(!0===this.defaults.async&&!1===r.async)return i(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(typeof t>"u"||null===t)return i(new Error("marked(): input parameter is undefined or null"));if("string"!=typeof t)return i(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"));o.hooks&&(o.hooks.options=o,o.hooks.block=e);let s=o.hooks?o.hooks.provideLexer():e?An.lex:An.lexInline,a=o.hooks?o.hooks.provideParser():e?Tn.parse:Tn.parseInline;if(o.async)return Promise.resolve(o.hooks?o.hooks.preprocess(t):t).then(l=>s(l,o)).then(l=>o.hooks?o.hooks.processAllTokens(l):l).then(l=>o.walkTokens?Promise.all(this.walkTokens(l,o.walkTokens)).then(()=>l):l).then(l=>a(l,o)).then(l=>o.hooks?o.hooks.postprocess(l):l).catch(i);try{o.hooks&&(t=o.hooks.preprocess(t));let l=s(t,o);o.hooks&&(l=o.hooks.processAllTokens(l)),o.walkTokens&&this.walkTokens(l,o.walkTokens);let c=a(l,o);return o.hooks&&(c=o.hooks.postprocess(c)),c}catch(l){return i(l)}}}onError(e,t){return n=>{if(n.message+="\nPlease report this to https://github.com/markedjs/marked.",e){let r="<p>An error occurred:</p><pre>"+an(n.message+"",!0)+"</pre>";return t?Promise.resolve(r):r}if(t)return Promise.reject(n);throw n}}};function Q(e,t){return Dr.parse(e,t)}Q.options=Q.setOptions=function(e){return Dr.setOptions(e),Cb(Q.defaults=Dr.defaults),Q},Q.getDefaults=function fh(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}},Q.defaults=Cr,Q.use=function(...e){return Dr.use(...e),Cb(Q.defaults=Dr.defaults),Q},Q.walkTokens=function(e,t){return Dr.walkTokens(e,t)},Q.parseInline=Dr.parseInline,Q.Parser=Tn,Q.parser=Tn.parse,Q.Renderer=Vl,Q.TextRenderer=Dh,Q.Lexer=An,Q.lexer=An.lex,Q.Tokenizer=Ll,Q.Hooks=jl,Q.parse=Q;class $l{}class Bl{}class Tt{constructor(t){this.normalizedNames=new Map,this.lazyUpdate=null,t?"string"==typeof t?this.lazyInit=()=>{this.headers=new Map,t.split("\n").forEach(n=>{const r=n.indexOf(":");if(r>0){const o=n.slice(0,r),i=o.toLowerCase(),s=n.slice(r+1).trim();this.maybeSetNormalizedName(o,i),this.headers.has(i)?this.headers.get(i).push(s):this.headers.set(i,[s])}})}:typeof Headers<"u"&&t instanceof Headers?(this.headers=new Map,t.forEach((n,r)=>{this.setHeaderEntries(r,n)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(t).forEach(([n,r])=>{this.setHeaderEntries(n,r)})}:this.headers=new Map}has(t){return this.init(),this.headers.has(t.toLowerCase())}get(t){this.init();const n=this.headers.get(t.toLowerCase());return n&&n.length>0?n[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(t){return this.init(),this.headers.get(t.toLowerCase())||null}append(t,n){return this.clone({name:t,value:n,op:"a"})}set(t,n){return this.clone({name:t,value:n,op:"s"})}delete(t,n){return this.clone({name:t,value:n,op:"d"})}maybeSetNormalizedName(t,n){this.normalizedNames.has(n)||this.normalizedNames.set(n,t)}init(){this.lazyInit&&(this.lazyInit instanceof Tt?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(t=>this.applyUpdate(t)),this.lazyUpdate=null))}copyFrom(t){t.init(),Array.from(t.headers.keys()).forEach(n=>{this.headers.set(n,t.headers.get(n)),this.normalizedNames.set(n,t.normalizedNames.get(n))})}clone(t){const n=new Tt;return n.lazyInit=this.lazyInit&&this.lazyInit instanceof Tt?this.lazyInit:this,n.lazyUpdate=(this.lazyUpdate||[]).concat([t]),n}applyUpdate(t){const n=t.name.toLowerCase();switch(t.op){case"a":case"s":let r=t.value;if("string"==typeof r&&(r=[r]),0===r.length)return;this.maybeSetNormalizedName(t.name,n);const o=("a"===t.op?this.headers.get(n):void 0)||[];o.push(...r),this.headers.set(n,o);break;case"d":const i=t.value;if(i){let s=this.headers.get(n);if(!s)return;s=s.filter(a=>-1===i.indexOf(a)),0===s.length?(this.headers.delete(n),this.normalizedNames.delete(n)):this.headers.set(n,s)}else this.headers.delete(n),this.normalizedNames.delete(n)}}setHeaderEntries(t,n){const r=(Array.isArray(n)?n:[n]).map(i=>i.toString()),o=t.toLowerCase();this.headers.set(o,r),this.maybeSetNormalizedName(t,o)}forEach(t){this.init(),Array.from(this.normalizedNames.keys()).forEach(n=>t(this.normalizedNames.get(n),this.headers.get(n)))}}class R2{encodeKey(t){return Fb(t)}encodeValue(t){return Fb(t)}decodeKey(t){return decodeURIComponent(t)}decodeValue(t){return decodeURIComponent(t)}}const F2=/%(\d[a-f0-9])/gi,L2={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function Fb(e){return encodeURIComponent(e).replace(F2,(t,n)=>L2[n]??t)}function Hl(e){return`${e}`}class Zn{constructor(t={}){if(this.updates=null,this.cloneFrom=null,this.encoder=t.encoder||new R2,t.fromString){if(t.fromObject)throw new Error("Cannot specify both fromString and fromObject.");this.map=function k2(e,t){const n=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(o=>{const i=o.indexOf("="),[s,a]=-1==i?[t.decodeKey(o),""]:[t.decodeKey(o.slice(0,i)),t.decodeValue(o.slice(i+1))],l=n.get(s)||[];l.push(a),n.set(s,l)}),n}(t.fromString,this.encoder)}else t.fromObject?(this.map=new Map,Object.keys(t.fromObject).forEach(n=>{const r=t.fromObject[n],o=Array.isArray(r)?r.map(Hl):[Hl(r)];this.map.set(n,o)})):this.map=null}has(t){return this.init(),this.map.has(t)}get(t){this.init();const n=this.map.get(t);return n?n[0]:null}getAll(t){return this.init(),this.map.get(t)||null}keys(){return this.init(),Array.from(this.map.keys())}append(t,n){return this.clone({param:t,value:n,op:"a"})}appendAll(t){const n=[];return Object.keys(t).forEach(r=>{const o=t[r];Array.isArray(o)?o.forEach(i=>{n.push({param:r,value:i,op:"a"})}):n.push({param:r,value:o,op:"a"})}),this.clone(n)}set(t,n){return this.clone({param:t,value:n,op:"s"})}delete(t,n){return this.clone({param:t,value:n,op:"d"})}toString(){return this.init(),this.keys().map(t=>{const n=this.encoder.encodeKey(t);return this.map.get(t).map(r=>n+"="+this.encoder.encodeValue(r)).join("&")}).filter(t=>""!==t).join("&")}clone(t){const n=new Zn({encoder:this.encoder});return n.cloneFrom=this.cloneFrom||this,n.updates=(this.updates||[]).concat(t),n}init(){null===this.map&&(this.map=new Map),null!==this.cloneFrom&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(t=>this.map.set(t,this.cloneFrom.map.get(t))),this.updates.forEach(t=>{switch(t.op){case"a":case"s":const n=("a"===t.op?this.map.get(t.param):void 0)||[];n.push(Hl(t.value)),this.map.set(t.param,n);break;case"d":if(void 0===t.value){this.map.delete(t.param);break}{let r=this.map.get(t.param)||[];const o=r.indexOf(Hl(t.value));-1!==o&&r.splice(o,1),r.length>0?this.map.set(t.param,r):this.map.delete(t.param)}}}),this.cloneFrom=this.updates=null)}}class V2{constructor(){this.map=new Map}set(t,n){return this.map.set(t,n),this}get(t){return this.map.has(t)||this.map.set(t,t.defaultValue()),this.map.get(t)}delete(t){return this.map.delete(t),this}has(t){return this.map.has(t)}keys(){return this.map.keys()}}function Lb(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function Vb(e){return typeof Blob<"u"&&e instanceof Blob}function jb(e){return typeof FormData<"u"&&e instanceof FormData}class ps{constructor(t,n,r,o){let i;if(this.url=n,this.body=null,this.reportProgress=!1,this.withCredentials=!1,this.responseType="json",this.method=t.toUpperCase(),function j2(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}(this.method)||o?(this.body=void 0!==r?r:null,i=o):i=r,i&&(this.reportProgress=!!i.reportProgress,this.withCredentials=!!i.withCredentials,i.responseType&&(this.responseType=i.responseType),i.headers&&(this.headers=i.headers),i.context&&(this.context=i.context),i.params&&(this.params=i.params)),this.headers||(this.headers=new Tt),this.context||(this.context=new V2),this.params){const s=this.params.toString();if(0===s.length)this.urlWithParams=n;else{const a=n.indexOf("?");this.urlWithParams=n+(-1===a?"?":a<n.length-1?"&":"")+s}}else this.params=new Zn,this.urlWithParams=n}serializeBody(){return null===this.body?null:Lb(this.body)||Vb(this.body)||jb(this.body)||function $2(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}(this.body)||"string"==typeof this.body?this.body:this.body instanceof Zn?this.body.toString():"object"==typeof this.body||"boolean"==typeof this.body||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return null===this.body||jb(this.body)?null:Vb(this.body)?this.body.type||null:Lb(this.body)?null:"string"==typeof this.body?"text/plain":this.body instanceof Zn?"application/x-www-form-urlencoded;charset=UTF-8":"object"==typeof this.body||"number"==typeof this.body||"boolean"==typeof this.body?"application/json":null}clone(t={}){const n=t.method||this.method,r=t.url||this.url,o=t.responseType||this.responseType,i=void 0!==t.body?t.body:this.body,s=void 0!==t.withCredentials?t.withCredentials:this.withCredentials,a=void 0!==t.reportProgress?t.reportProgress:this.reportProgress;let l=t.headers||this.headers,c=t.params||this.params;const u=t.context??this.context;return void 0!==t.setHeaders&&(l=Object.keys(t.setHeaders).reduce((d,f)=>d.set(f,t.setHeaders[f]),l)),t.setParams&&(c=Object.keys(t.setParams).reduce((d,f)=>d.set(f,t.setParams[f]),c)),new ps(n,r,i,{params:c,headers:l,context:u,reportProgress:a,responseType:o,withCredentials:s})}}var Lo=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(Lo||{});class wh{constructor(t,n=200,r="OK"){this.headers=t.headers||new Tt,this.status=void 0!==t.status?t.status:n,this.statusText=t.statusText||r,this.url=t.url||null,this.ok=this.status>=200&&this.status<300}}class bh extends wh{constructor(t={}){super(t),this.type=Lo.ResponseHeader}clone(t={}){return new bh({headers:t.headers||this.headers,status:void 0!==t.status?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}}class Vo extends wh{constructor(t={}){super(t),this.type=Lo.Response,this.body=void 0!==t.body?t.body:null}clone(t={}){return new Vo({body:void 0!==t.body?t.body:this.body,headers:t.headers||this.headers,status:void 0!==t.status?t.status:this.status,statusText:t.statusText||this.statusText,url:t.url||this.url||void 0})}}class $b extends wh{constructor(t){super(t,0,"Unknown Error"),this.name="HttpErrorResponse",this.ok=!1,this.message=this.status>=200&&this.status<300?`Http failure during parsing for ${t.url||"(unknown url)"}`:`Http failure response for ${t.url||"(unknown url)"}: ${t.status} ${t.statusText}`,this.error=t.error||null}}function Eh(e,t){return{body:t,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials}}let Bb=(()=>{class e{constructor(n){this.handler=n}request(n,r,o={}){let i;if(n instanceof ps)i=n;else{let l,c;l=o.headers instanceof Tt?o.headers:new Tt(o.headers),o.params&&(c=o.params instanceof Zn?o.params:new Zn({fromObject:o.params})),i=new ps(n,r,void 0!==o.body?o.body:null,{headers:l,context:o.context,params:c,reportProgress:o.reportProgress,responseType:o.responseType||"json",withCredentials:o.withCredentials})}const s=A(i).pipe(So(l=>this.handler.handle(l)));if(n instanceof ps||"events"===o.observe)return s;const a=s.pipe(In(l=>l instanceof Vo));switch(o.observe||"body"){case"body":switch(i.responseType){case"arraybuffer":return a.pipe(z(l=>{if(null!==l.body&&!(l.body instanceof ArrayBuffer))throw new Error("Response is not an ArrayBuffer.");return l.body}));case"blob":return a.pipe(z(l=>{if(null!==l.body&&!(l.body instanceof Blob))throw new Error("Response is not a Blob.");return l.body}));case"text":return a.pipe(z(l=>{if(null!==l.body&&"string"!=typeof l.body)throw new Error("Response is not a string.");return l.body}));default:return a.pipe(z(l=>l.body))}case"response":return a;default:throw new Error(`Unreachable: unhandled observe type ${o.observe}}`)}}delete(n,r={}){return this.request("DELETE",n,r)}get(n,r={}){return this.request("GET",n,r)}head(n,r={}){return this.request("HEAD",n,r)}jsonp(n,r){return this.request("JSONP",n,{params:(new Zn).append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(n,r={}){return this.request("OPTIONS",n,r)}patch(n,r,o={}){return this.request("PATCH",n,Eh(o,r))}post(n,r,o={}){return this.request("POST",n,Eh(o,r))}put(n,r,o={}){return this.request("PUT",n,Eh(o,r))}static#e=this.\u0275fac=function(r){return new(r||e)(I($l))};static#t=this.\u0275prov=x({token:e,factory:e.\u0275fac})}return e})();function zb(e,t){return t(e)}function H2(e,t){return(n,r)=>t.intercept(n,{handle:o=>e(o,r)})}const z2=new M(""),gs=new M(""),Gb=new M("");function G2(){let e=null;return(t,n)=>{null===e&&(e=(E(z2,{optional:!0})??[]).reduceRight(H2,zb));const r=E(Wa),o=r.add();return e(t,n).pipe(Zi(()=>r.remove(o)))}}let qb=(()=>{class e extends $l{constructor(n,r){super(),this.backend=n,this.injector=r,this.chain=null,this.pendingTasks=E(Wa)}handle(n){if(null===this.chain){const o=Array.from(new Set([...this.injector.get(gs),...this.injector.get(Gb,[])]));this.chain=o.reduceRight((i,s)=>function U2(e,t,n){return(r,o)=>n.runInContext(()=>t(r,i=>e(i,o)))}(i,s,this.injector),zb)}const r=this.pendingTasks.add();return this.chain(n,o=>this.backend.handle(o)).pipe(Zi(()=>this.pendingTasks.remove(r)))}static#e=this.\u0275fac=function(r){return new(r||e)(I(Bl),I(ht))};static#t=this.\u0275prov=x({token:e,factory:e.\u0275fac})}return e})();const Y2=/^\)\]\}',?\n/;let Zb=(()=>{class e{constructor(n){this.xhrFactory=n}handle(n){if("JSONP"===n.method)throw new C(-2800,!1);const r=this.xhrFactory;return(r.\u0275loadImpl?Me(r.\u0275loadImpl()):A(null)).pipe(Nt(()=>new he(i=>{const s=r.build();if(s.open(n.method,n.urlWithParams),n.withCredentials&&(s.withCredentials=!0),n.headers.forEach((g,y)=>s.setRequestHeader(g,y.join(","))),n.headers.has("Accept")||s.setRequestHeader("Accept","application/json, text/plain, */*"),!n.headers.has("Content-Type")){const g=n.detectContentTypeHeader();null!==g&&s.setRequestHeader("Content-Type",g)}if(n.responseType){const g=n.responseType.toLowerCase();s.responseType="json"!==g?g:"text"}const a=n.serializeBody();let l=null;const c=()=>{if(null!==l)return l;const g=s.statusText||"OK",y=new Tt(s.getAllResponseHeaders()),_=function Q2(e){return"responseURL"in e&&e.responseURL?e.responseURL:/^X-Request-URL:/m.test(e.getAllResponseHeaders())?e.getResponseHeader("X-Request-URL"):null}(s)||n.url;return l=new bh({headers:y,status:s.status,statusText:g,url:_}),l},u=()=>{let{headers:g,status:y,statusText:_,url:m}=c(),w=null;204!==y&&(w=typeof s.response>"u"?s.responseText:s.response),0===y&&(y=w?200:0);let S=y>=200&&y<300;if("json"===n.responseType&&"string"==typeof w){const j=w;w=w.replace(Y2,"");try{w=""!==w?JSON.parse(w):null}catch(ye){w=j,S&&(S=!1,w={error:ye,text:w})}}S?(i.next(new Vo({body:w,headers:g,status:y,statusText:_,url:m||void 0})),i.complete()):i.error(new $b({error:w,headers:g,status:y,statusText:_,url:m||void 0}))},d=g=>{const{url:y}=c(),_=new $b({error:g,status:s.status||0,statusText:s.statusText||"Unknown Error",url:y||void 0});i.error(_)};let f=!1;const h=g=>{f||(i.next(c()),f=!0);let y={type:Lo.DownloadProgress,loaded:g.loaded};g.lengthComputable&&(y.total=g.total),"text"===n.responseType&&s.responseText&&(y.partialText=s.responseText),i.next(y)},p=g=>{let y={type:Lo.UploadProgress,loaded:g.loaded};g.lengthComputable&&(y.total=g.total),i.next(y)};return s.addEventListener("load",u),s.addEventListener("error",d),s.addEventListener("timeout",d),s.addEventListener("abort",d),n.reportProgress&&(s.addEventListener("progress",h),null!==a&&s.upload&&s.upload.addEventListener("progress",p)),s.send(a),i.next({type:Lo.Sent}),()=>{s.removeEventListener("error",d),s.removeEventListener("abort",d),s.removeEventListener("load",u),s.removeEventListener("timeout",d),n.reportProgress&&(s.removeEventListener("progress",h),null!==a&&s.upload&&s.upload.removeEventListener("progress",p)),s.readyState!==s.DONE&&s.abort()}})))}static#e=this.\u0275fac=function(r){return new(r||e)(I(HD))};static#t=this.\u0275prov=x({token:e,factory:e.\u0275fac})}return e})();const Mh=new M("XSRF_ENABLED"),Yb=new M("XSRF_COOKIE_NAME",{providedIn:"root",factory:()=>"XSRF-TOKEN"}),Qb=new M("XSRF_HEADER_NAME",{providedIn:"root",factory:()=>"X-XSRF-TOKEN"});class Xb{}let K2=(()=>{class e{constructor(n,r,o){this.doc=n,this.platform=r,this.cookieName=o,this.lastCookieString="",this.lastToken=null,this.parseCount=0}getToken(){if("server"===this.platform)return null;const n=this.doc.cookie||"";return n!==this.lastCookieString&&(this.parseCount++,this.lastToken=ND(n,this.cookieName),this.lastCookieString=n),this.lastToken}static#e=this.\u0275fac=function(r){return new(r||e)(I(vt),I(ur),I(Yb))};static#t=this.\u0275prov=x({token:e,factory:e.\u0275fac})}return e})();function ej(e,t){const n=e.url.toLowerCase();if(!E(Mh)||"GET"===e.method||"HEAD"===e.method||n.startsWith("http://")||n.startsWith("https://"))return t(e);const r=E(Xb).getToken(),o=E(Qb);return null!=r&&!e.headers.has(o)&&(e=e.clone({headers:e.headers.set(o,r)})),t(e)}var Yn=function(e){return e[e.Interceptors=0]="Interceptors",e[e.LegacyInterceptors=1]="LegacyInterceptors",e[e.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",e[e.NoXsrfProtection=3]="NoXsrfProtection",e[e.JsonpSupport=4]="JsonpSupport",e[e.RequestsMadeViaParent=5]="RequestsMadeViaParent",e[e.Fetch=6]="Fetch",e}(Yn||{});function tj(...e){const t=[Bb,Zb,qb,{provide:$l,useExisting:qb},{provide:Bl,useExisting:Zb},{provide:gs,useValue:ej,multi:!0},{provide:Mh,useValue:!0},{provide:Xb,useClass:K2}];for(const n of e)t.push(...n.\u0275providers);return function Eu(e){return{\u0275providers:e}}(t)}const Jb=new M("LEGACY_INTERCEPTOR_FN");function nj(){return function wr(e,t){return{\u0275kind:e,\u0275providers:t}}(Yn.LegacyInterceptors,[{provide:Jb,useFactory:G2},{provide:gs,useExisting:Jb,multi:!0}])}let rj=(()=>{class e{static#e=this.\u0275fac=function(r){return new(r||e)};static#t=this.\u0275mod=wt({type:e});static#n=this.\u0275inj=ct({providers:[tj(nj())]})}return e})(),Ih=(()=>{class e{constructor(n){this.http=n,this.baseUrl="https://news-agent-backend.duckdns.org/api",this.tokenKey="token"}getToken(){return localStorage.getItem(this.tokenKey)}setToken(n){localStorage.setItem(this.tokenKey,n)}checkAuthStatus(){const n=this.getToken();if(!n)return A(!1);const r=new Tt({Authorization:`Token ${n}`});return this.http.get(`${this.baseUrl}/check-auth/`,{headers:r}).pipe(z(()=>!0),Gn(()=>A(!1)))}createAnonymousUser(){return this.http.post(`${this.baseUrl}/anonymous-user/`,{}).pipe(z(n=>(this.setToken(n.token),n.token)))}ensureAuthenticated(){return new he(n=>{this.checkAuthStatus().subscribe(r=>{r?(n.next(this.getToken()),n.complete()):this.createAnonymousUser().subscribe(o=>{n.next(o),n.complete()},o=>{n.error(o)})})})}getHeaders(){const n=this.getToken();return new Tt({Authorization:`Token ${n}`})}loadChatHistory(n=1,r){let o=`${this.baseUrl}/chat-history/?page=${n}`;return r&&(o+=`&session_id=${r}`),this.http.get(o,{headers:this.getHeaders()})}sendMessageToChatbot(n,r){const o={message:n};return r&&(o.session_id=r),this.http.post(`${this.baseUrl}/chatbot/`,o,{headers:this.getHeaders()})}fetchNews(n="poland"){return this.http.post(`${this.baseUrl}/news/`,{country:n},{headers:this.getHeaders()})}static#e=this.\u0275fac=function(r){return new(r||e)(I(Bb))};static#t=this.\u0275prov=x({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Kb=(()=>{class e{constructor(n,r){this._renderer=n,this._elementRef=r,this.onChange=o=>{},this.onTouched=()=>{}}setProperty(n,r){this._renderer.setProperty(this._elementRef.nativeElement,n,r)}registerOnTouched(n){this.onTouched=n}registerOnChange(n){this.onChange=n}setDisabledState(n){this.setProperty("disabled",n)}static#e=this.\u0275fac=function(r){return new(r||e)(D(mn),D(pt))};static#t=this.\u0275dir=P({type:e})}return e})(),br=(()=>{class e extends Kb{static#e=this.\u0275fac=function(){let n;return function(o){return(n||(n=Re(e)))(o||e)}}();static#t=this.\u0275dir=P({type:e,features:[J]})}return e})();const ln=new M("NgValueAccessor"),dj={provide:ln,useExisting:ne(()=>zl),multi:!0},hj=new M("CompositionEventMode");let zl=(()=>{class e extends Kb{constructor(n,r,o){super(n,r),this._compositionMode=o,this._composing=!1,null==this._compositionMode&&(this._compositionMode=!function fj(){const e=Un()?Un().getUserAgent():"";return/android (\d+)/.test(e.toLowerCase())}())}writeValue(n){this.setProperty("value",n??"")}_handleInput(n){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(n)}_compositionStart(){this._composing=!0}_compositionEnd(n){this._composing=!1,this._compositionMode&&this.onChange(n)}static#e=this.\u0275fac=function(r){return new(r||e)(D(mn),D(pt),D(hj,8))};static#t=this.\u0275dir=P({type:e,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(r,o){1&r&&je("input",function(s){return o._handleInput(s.target.value)})("blur",function(){return o.onTouched()})("compositionstart",function(){return o._compositionStart()})("compositionend",function(s){return o._compositionEnd(s.target.value)})},features:[ue([dj]),J]})}return e})();const Be=new M("NgValidators"),Xn=new M("NgAsyncValidators");function uE(e){return null!=e}function dE(e){return Ti(e)?Me(e):e}function fE(e){let t={};return e.forEach(n=>{t=null!=n?{...t,...n}:t}),0===Object.keys(t).length?null:t}function hE(e,t){return t.map(n=>n(e))}function pE(e){return e.map(t=>function gj(e){return!e.validate}(t)?t:n=>t.validate(n))}function Sh(e){return null!=e?function gE(e){if(!e)return null;const t=e.filter(uE);return 0==t.length?null:function(n){return fE(hE(n,t))}}(pE(e)):null}function xh(e){return null!=e?function mE(e){if(!e)return null;const t=e.filter(uE);return 0==t.length?null:function(n){return function cj(...e){const t=_p(e),{args:n,keys:r}=uw(e),o=new he(i=>{const{length:s}=n;if(!s)return void i.complete();const a=new Array(s);let l=s,c=s;for(let u=0;u<s;u++){let d=!1;at(n[u]).subscribe(De(i,f=>{d||(d=!0,c--),a[u]=f},()=>l--,void 0,()=>{(!l||!d)&&(c||i.next(r?fw(r,a):a),i.complete())}))}});return t?o.pipe(dw(t)):o}(hE(n,t).map(dE)).pipe(z(fE))}}(pE(e)):null}function yE(e,t){return null===e?[t]:Array.isArray(e)?[...e,t]:[e,t]}function Ah(e){return e?Array.isArray(e)?e:[e]:[]}function ql(e,t){return Array.isArray(e)?e.includes(t):e===t}function CE(e,t){const n=Ah(t);return Ah(e).forEach(o=>{ql(n,o)||n.push(o)}),n}function DE(e,t){return Ah(t).filter(n=>!ql(e,n))}class wE{constructor(){this._rawValidators=[],this._rawAsyncValidators=[],this._onDestroyCallbacks=[]}get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_setValidators(t){this._rawValidators=t||[],this._composedValidatorFn=Sh(this._rawValidators)}_setAsyncValidators(t){this._rawAsyncValidators=t||[],this._composedAsyncValidatorFn=xh(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_registerOnDestroy(t){this._onDestroyCallbacks.push(t)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(t=>t()),this._onDestroyCallbacks=[]}reset(t=void 0){this.control&&this.control.reset(t)}hasError(t,n){return!!this.control&&this.control.hasError(t,n)}getError(t,n){return this.control?this.control.getError(t,n):null}}class Je extends wE{get formDirective(){return null}get path(){return null}}class Jn extends wE{constructor(){super(...arguments),this._parent=null,this.name=null,this.valueAccessor=null}}class bE{constructor(t){this._cd=t}get isTouched(){return!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return!!this._cd?.submitted}}let EE=(()=>{class e extends bE{constructor(n){super(n)}static#e=this.\u0275fac=function(r){return new(r||e)(D(Jn,2))};static#t=this.\u0275dir=P({type:e,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(r,o){2&r&&Oi("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)},features:[J]})}return e})();const ms="VALID",Zl="INVALID",jo="PENDING",ys="DISABLED";function Yl(e){return null!=e&&!Array.isArray(e)&&"object"==typeof e}class xE{constructor(t,n){this._pendingDirty=!1,this._hasOwnPendingAsyncValidator=!1,this._pendingTouched=!1,this._onCollectionChange=()=>{},this._parent=null,this.pristine=!0,this.touched=!1,this._onDisabledChange=[],this._assignValidators(t),this._assignAsyncValidators(n)}get validator(){return this._composedValidatorFn}set validator(t){this._rawValidators=this._composedValidatorFn=t}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(t){this._rawAsyncValidators=this._composedAsyncValidatorFn=t}get parent(){return this._parent}get valid(){return this.status===ms}get invalid(){return this.status===Zl}get pending(){return this.status==jo}get disabled(){return this.status===ys}get enabled(){return this.status!==ys}get dirty(){return!this.pristine}get untouched(){return!this.touched}get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(t){this._assignValidators(t)}setAsyncValidators(t){this._assignAsyncValidators(t)}addValidators(t){this.setValidators(CE(t,this._rawValidators))}addAsyncValidators(t){this.setAsyncValidators(CE(t,this._rawAsyncValidators))}removeValidators(t){this.setValidators(DE(t,this._rawValidators))}removeAsyncValidators(t){this.setAsyncValidators(DE(t,this._rawAsyncValidators))}hasValidator(t){return ql(this._rawValidators,t)}hasAsyncValidator(t){return ql(this._rawAsyncValidators,t)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(t={}){this.touched=!0,this._parent&&!t.onlySelf&&this._parent.markAsTouched(t)}markAllAsTouched(){this.markAsTouched({onlySelf:!0}),this._forEachChild(t=>t.markAllAsTouched())}markAsUntouched(t={}){this.touched=!1,this._pendingTouched=!1,this._forEachChild(n=>{n.markAsUntouched({onlySelf:!0})}),this._parent&&!t.onlySelf&&this._parent._updateTouched(t)}markAsDirty(t={}){this.pristine=!1,this._parent&&!t.onlySelf&&this._parent.markAsDirty(t)}markAsPristine(t={}){this.pristine=!0,this._pendingDirty=!1,this._forEachChild(n=>{n.markAsPristine({onlySelf:!0})}),this._parent&&!t.onlySelf&&this._parent._updatePristine(t)}markAsPending(t={}){this.status=jo,!1!==t.emitEvent&&this.statusChanges.emit(this.status),this._parent&&!t.onlySelf&&this._parent.markAsPending(t)}disable(t={}){const n=this._parentMarkedDirty(t.onlySelf);this.status=ys,this.errors=null,this._forEachChild(r=>{r.disable({...t,onlySelf:!0})}),this._updateValue(),!1!==t.emitEvent&&(this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors({...t,skipPristineCheck:n}),this._onDisabledChange.forEach(r=>r(!0))}enable(t={}){const n=this._parentMarkedDirty(t.onlySelf);this.status=ms,this._forEachChild(r=>{r.enable({...t,onlySelf:!0})}),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent}),this._updateAncestors({...t,skipPristineCheck:n}),this._onDisabledChange.forEach(r=>r(!1))}_updateAncestors(t){this._parent&&!t.onlySelf&&(this._parent.updateValueAndValidity(t),t.skipPristineCheck||this._parent._updatePristine(),this._parent._updateTouched())}setParent(t){this._parent=t}getRawValue(){return this.value}updateValueAndValidity(t={}){this._setInitialStatus(),this._updateValue(),this.enabled&&(this._cancelExistingSubscription(),this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===ms||this.status===jo)&&this._runAsyncValidator(t.emitEvent)),!1!==t.emitEvent&&(this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!t.onlySelf&&this._parent.updateValueAndValidity(t)}_updateTreeValidity(t={emitEvent:!0}){this._forEachChild(n=>n._updateTreeValidity(t)),this.updateValueAndValidity({onlySelf:!0,emitEvent:t.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?ys:ms}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(t){if(this.asyncValidator){this.status=jo,this._hasOwnPendingAsyncValidator=!0;const n=dE(this.asyncValidator(this));this._asyncValidationSubscription=n.subscribe(r=>{this._hasOwnPendingAsyncValidator=!1,this.setErrors(r,{emitEvent:t})})}}_cancelExistingSubscription(){this._asyncValidationSubscription&&(this._asyncValidationSubscription.unsubscribe(),this._hasOwnPendingAsyncValidator=!1)}setErrors(t,n={}){this.errors=t,this._updateControlsErrors(!1!==n.emitEvent)}get(t){let n=t;return null==n||(Array.isArray(n)||(n=n.split(".")),0===n.length)?null:n.reduce((r,o)=>r&&r._find(o),this)}getError(t,n){const r=n?this.get(n):this;return r&&r.errors?r.errors[t]:null}hasError(t,n){return!!this.getError(t,n)}get root(){let t=this;for(;t._parent;)t=t._parent;return t}_updateControlsErrors(t){this.status=this._calculateStatus(),t&&this.statusChanges.emit(this.status),this._parent&&this._parent._updateControlsErrors(t)}_initObservables(){this.valueChanges=new ge,this.statusChanges=new ge}_calculateStatus(){return this._allControlsDisabled()?ys:this.errors?Zl:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(jo)?jo:this._anyControlsHaveStatus(Zl)?Zl:ms}_anyControlsHaveStatus(t){return this._anyControls(n=>n.status===t)}_anyControlsDirty(){return this._anyControls(t=>t.dirty)}_anyControlsTouched(){return this._anyControls(t=>t.touched)}_updatePristine(t={}){this.pristine=!this._anyControlsDirty(),this._parent&&!t.onlySelf&&this._parent._updatePristine(t)}_updateTouched(t={}){this.touched=this._anyControlsTouched(),this._parent&&!t.onlySelf&&this._parent._updateTouched(t)}_registerOnCollectionChange(t){this._onCollectionChange=t}_setUpdateStrategy(t){Yl(t)&&null!=t.updateOn&&(this._updateOn=t.updateOn)}_parentMarkedDirty(t){return!t&&!(!this._parent||!this._parent.dirty)&&!this._parent._anyControlsDirty()}_find(t){return null}_assignValidators(t){this._rawValidators=Array.isArray(t)?t.slice():t,this._composedValidatorFn=function Cj(e){return Array.isArray(e)?Sh(e):e||null}(this._rawValidators)}_assignAsyncValidators(t){this._rawAsyncValidators=Array.isArray(t)?t.slice():t,this._composedAsyncValidatorFn=function Dj(e){return Array.isArray(e)?xh(e):e||null}(this._rawAsyncValidators)}}const Er=new M("CallSetDisabledState",{providedIn:"root",factory:()=>vs}),vs="always";function _s(e,t,n=vs){(function kh(e,t){const n=function vE(e){return e._rawValidators}(e);null!==t.validator?e.setValidators(yE(n,t.validator)):"function"==typeof n&&e.setValidators([n]);const r=function _E(e){return e._rawAsyncValidators}(e);null!==t.asyncValidator?e.setAsyncValidators(yE(r,t.asyncValidator)):"function"==typeof r&&e.setAsyncValidators([r]);const o=()=>e.updateValueAndValidity();Jl(t._rawValidators,o),Jl(t._rawAsyncValidators,o)})(e,t),t.valueAccessor.writeValue(e.value),(e.disabled||"always"===n)&&t.valueAccessor.setDisabledState?.(e.disabled),function Ej(e,t){t.valueAccessor.registerOnChange(n=>{e._pendingValue=n,e._pendingChange=!0,e._pendingDirty=!0,"change"===e.updateOn&&AE(e,t)})}(e,t),function Ij(e,t){const n=(r,o)=>{t.valueAccessor.writeValue(r),o&&t.viewToModelUpdate(r)};e.registerOnChange(n),t._registerOnDestroy(()=>{e._unregisterOnChange(n)})}(e,t),function Mj(e,t){t.valueAccessor.registerOnTouched(()=>{e._pendingTouched=!0,"blur"===e.updateOn&&e._pendingChange&&AE(e,t),"submit"!==e.updateOn&&e.markAsTouched()})}(e,t),function bj(e,t){if(t.valueAccessor.setDisabledState){const n=r=>{t.valueAccessor.setDisabledState(r)};e.registerOnDisabledChange(n),t._registerOnDestroy(()=>{e._unregisterOnDisabledChange(n)})}}(e,t)}function Jl(e,t){e.forEach(n=>{n.registerOnValidatorChange&&n.registerOnValidatorChange(t)})}function AE(e,t){e._pendingDirty&&e.markAsDirty(),e.setValue(e._pendingValue,{emitModelToViewChange:!1}),t.viewToModelUpdate(e._pendingValue),e._pendingChange=!1}function NE(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}function PE(e){return"object"==typeof e&&null!==e&&2===Object.keys(e).length&&"value"in e&&"disabled"in e}const RE=class extends xE{constructor(t=null,n,r){super(function Nh(e){return(Yl(e)?e.validators:e)||null}(n),function Ph(e,t){return(Yl(t)?t.asyncValidators:e)||null}(r,n)),this.defaultValue=null,this._onChange=[],this._pendingChange=!1,this._applyFormState(t),this._setUpdateStrategy(n),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),Yl(n)&&(n.nonNullable||n.initialValueIsDefault)&&(this.defaultValue=PE(t)?t.value:t)}setValue(t,n={}){this.value=this._pendingValue=t,this._onChange.length&&!1!==n.emitModelToViewChange&&this._onChange.forEach(r=>r(this.value,!1!==n.emitViewToModelChange)),this.updateValueAndValidity(n)}patchValue(t,n={}){this.setValue(t,n)}reset(t=this.defaultValue,n={}){this._applyFormState(t),this.markAsPristine(n),this.markAsUntouched(n),this.setValue(this.value,n),this._pendingChange=!1}_updateValue(){}_anyControls(t){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(t){this._onChange.push(t)}_unregisterOnChange(t){NE(this._onChange,t)}registerOnDisabledChange(t){this._onDisabledChange.push(t)}_unregisterOnDisabledChange(t){NE(this._onDisabledChange,t)}_forEachChild(t){}_syncPendingControls(){return!("submit"!==this.updateOn||(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),!this._pendingChange)||(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),0))}_applyFormState(t){PE(t)?(this.value=this._pendingValue=t.value,t.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=t}},Rj={provide:Jn,useExisting:ne(()=>$h)},LE=(()=>Promise.resolve())();let $h=(()=>{class e extends Jn{constructor(n,r,o,i,s,a){super(),this._changeDetectorRef=s,this.callSetDisabledState=a,this.control=new RE,this._registered=!1,this.name="",this.update=new ge,this._parent=n,this._setValidators(r),this._setAsyncValidators(o),this.valueAccessor=function Vh(e,t){if(!t)return null;let n,r,o;return Array.isArray(t),t.forEach(i=>{i.constructor===zl?n=i:function Aj(e){return Object.getPrototypeOf(e.constructor)===br}(i)?r=i:o=i}),o||r||n||null}(0,i)}ngOnChanges(n){if(this._checkForErrors(),!this._registered||"name"in n){if(this._registered&&(this._checkName(),this.formDirective)){const r=n.name.previousValue;this.formDirective.removeControl({name:r,path:this._getPath(r)})}this._setUpControl()}"isDisabled"in n&&this._updateDisabled(n),function Lh(e,t){if(!e.hasOwnProperty("model"))return!1;const n=e.model;return!!n.isFirstChange()||!Object.is(t,n.currentValue)}(n,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(n){this.viewModel=n,this.update.emit(n)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&null!=this.options.updateOn&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!(!this.options||!this.options.standalone)}_setUpStandalone(){_s(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._isStandalone()||this._checkParentType(),this._checkName()}_checkParentType(){}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),this._isStandalone()}_updateValue(n){LE.then(()=>{this.control.setValue(n,{emitViewToModelChange:!1}),this._changeDetectorRef?.markForCheck()})}_updateDisabled(n){const r=n.isDisabled.currentValue,o=0!==r&&function Eo(e){return"boolean"==typeof e?e:null!=e&&"false"!==e}(r);LE.then(()=>{o&&!this.control.disabled?this.control.disable():!o&&this.control.disabled&&this.control.enable(),this._changeDetectorRef?.markForCheck()})}_getPath(n){return this._parent?function Ql(e,t){return[...t.path,e]}(n,this._parent):[n]}static#e=this.\u0275fac=function(r){return new(r||e)(D(Je,9),D(Be,10),D(Xn,10),D(ln,10),D(Xa,8),D(Er,8))};static#t=this.\u0275dir=P({type:e,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:["disabled","isDisabled"],model:["ngModel","model"],options:["ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],features:[ue([Rj]),J,Et]})}return e})(),jE=(()=>{class e{static#e=this.\u0275fac=function(r){return new(r||e)};static#t=this.\u0275mod=wt({type:e});static#n=this.\u0275inj=ct({})}return e})();const Bh=new M("NgModelWithFormControlWarning");let n0=(()=>{class e{static#e=this.\u0275fac=function(r){return new(r||e)};static#t=this.\u0275mod=wt({type:e});static#n=this.\u0275inj=ct({imports:[jE]})}return e})(),s$=(()=>{class e{static withConfig(n){return{ngModule:e,providers:[{provide:Er,useValue:n.callSetDisabledState??vs}]}}static#e=this.\u0275fac=function(r){return new(r||e)};static#t=this.\u0275mod=wt({type:e});static#n=this.\u0275inj=ct({imports:[n0]})}return e})(),a$=(()=>{class e{static withConfig(n){return{ngModule:e,providers:[{provide:Bh,useValue:n.warnOnNgModelWithFormControl??"always"},{provide:Er,useValue:n.callSetDisabledState??vs}]}}static#e=this.\u0275fac=function(r){return new(r||e)};static#t=this.\u0275mod=wt({type:e});static#n=this.\u0275inj=ct({imports:[n0]})}return e})(),l$=(()=>{class e{constructor(n){this.authService=n}ngOnInit(){this.initializeUser()}initializeUser(){this.authService.ensureAuthenticated().subscribe(n=>{console.log("User authenticated successfully")},n=>{console.error("Failed to authenticate user:",n)})}static#e=this.\u0275fac=function(r){return new(r||e)(D(Ih))};static#t=this.\u0275cmp=Ar({type:e,selectors:[["app-anonymous-user"]],decls:0,vars:0,template:function(r,o){}})}return e})();const c$=["messagesContainer"];function u$(e,t){1&e&&(se(0,"div",16),Lt(1,"div",17),fe())}const d$=function(e,t){return{"user-message":e,"bot-message":t}};function f$(e,t){if(1&e&&(se(0,"div",18)(1,"div",19),Lt(2,"div",20),se(3,"div",21),yt(4),Ua(5,"date"),fe()()()),2&e){const n=t.$implicit,r=Vt(3);ke("ngClass",function K_(e,t,n,r,o){return tC(v(),Ge(),e,t,n,r,o)}(6,d$,n.isUser,!n.isUser)),we(2),ke("innerHTML",r.formatMessageText(n.text),Hm),we(2),Cn(Od(5,3,n.timestamp,"short"))}}function h$(e,t){1&e&&(se(0,"div",22)(1,"div",19)(2,"div",23),Lt(3,"span")(4,"span")(5,"span"),fe()()())}function p$(e,t){if(1&e){const n=Fa();se(0,"div",11,12),je("scroll",function(o){return or(n),ir(Vt(2).onScroll(o))}),tn(2,u$,2,0,"div",13),tn(3,f$,6,9,"div",14),tn(4,h$,6,0,"div",15),fe()}if(2&e){const n=Vt(2);we(2),ke("ngIf",n.isLoadingHistory),we(1),ke("ngForOf",n.messages),we(1),ke("ngIf",n.isLoading)}}function g$(e,t){if(1&e){const n=Fa();se(0,"div",2),tn(1,p$,5,3,"div",3),se(2,"div",4)(3,"div",5)(4,"textarea",6,7),je("ngModelChange",function(o){return or(n),ir(Vt().currentMessage=o)})("keydown",function(o){return or(n),ir(Vt().onKeyPress(o))})("input",function(o){return or(n),ir(Vt().adjustTextareaHeight(o))}),yt(6,"      "),fe(),se(7,"button",8),je("click",function(){return or(n),ir(Vt().sendMessage())}),function Ng(){O.lFrame.currentNamespace=hg}(),se(8,"svg",9),Lt(9,"path",10),fe()()()()()}if(2&e){const n=Vt();we(1),ke("ngIf",n.messages.length>0),we(1),Oi("centered",0===n.messages.length)("bottom",n.messages.length>0),we(2),ke("ngModel",n.currentMessage),we(3),ke("disabled",!n.currentMessage.trim()||n.isLoading)}}function m$(e,t){1&e&&(se(0,"div",24),Lt(1,"div",17),se(2,"p"),yt(3,"Initializing chat..."),fe()())}let y$=(()=>{class e{constructor(n){this.authService=n,this.messages=[],this.currentMessage="",this.isLoading=!1,this.isAuthenticated=!1,this.currentSessionId=null,this.currentPage=1,this.hasNextPage=!1,this.isLoadingHistory=!1,this.shouldScrollToBottom=!0,this.lastScrollHeight=0,this.initialLoadComplete=!1,this.userHasScrolled=!1}ngOnInit(){this.authService.ensureAuthenticated().subscribe({next:n=>{console.log("User authenticated successfully"),this.isAuthenticated=!0,this.loadChatHistory(1,!1)},error:n=>{console.error("Authentication failed:",n),this.isAuthenticated=!1}})}ngAfterViewChecked(){this.shouldScrollToBottom&&(this.scrollToBottom(),this.shouldScrollToBottom=!1)}onScroll(n){const r=n.target,o=r.scrollTop,i=r.scrollHeight,s=r.clientHeight;this.initialLoadComplete&&(this.userHasScrolled=!0),this.scrollTimeout&&clearTimeout(this.scrollTimeout),this.scrollTimeout=setTimeout(()=>{o<100&&o<i-s-50&&this.hasNextPage&&!this.isLoadingHistory&&this.initialLoadComplete&&this.userHasScrolled&&(this.lastScrollHeight=i,this.loadMoreHistory())},100)}loadChatHistory(n=1,r=!1){this.isAuthenticated&&(this.isLoadingHistory=!0,this.authService.loadChatHistory(n,this.currentSessionId||void 0).subscribe({next:o=>{const i=o.results.map(s=>this.convertChatMessageToMessage(s));if(r){const s=[...i].reverse();this.messages=[...s,...this.messages],this.maintainScrollPosition()}else this.messages=[...i].reverse(),this.shouldScrollToBottom=!0,setTimeout(()=>{this.initialLoadComplete=!0},200);this.currentPage=n,this.hasNextPage=!!o.next,this.isLoadingHistory=!1},error:o=>{console.error("Error loading chat history:",o),this.isLoadingHistory=!1,r||setTimeout(()=>{this.initialLoadComplete=!0},200)}}))}convertChatMessageToMessage(n){return{id:n.id,text:n.message,isUser:"user"===n.sender,timestamp:new Date(n.timestamp),session:n.session,prompt:n.prompt||void 0,model:n.model||void 0,news_articles:n.news_articles||void 0}}loadMoreHistory(){this.hasNextPage&&!this.isLoadingHistory&&this.loadChatHistory(this.currentPage+1,!0)}maintainScrollPosition(){setTimeout(()=>{if(this.messagesContainer){const n=this.messagesContainer.nativeElement;n.scrollTop=n.scrollHeight-this.lastScrollHeight}},50)}sendMessage(){if(!this.currentMessage.trim()||this.isLoading||!this.isAuthenticated)return;const n=this.currentMessage;this.currentMessage="",this.messages.push({text:n,isUser:!0,timestamp:new Date}),this.shouldScrollToBottom=!0,this.isLoading=!0,this.authService.sendMessageToChatbot(n,this.currentSessionId||void 0).subscribe({next:o=>{const i=this.convertChatMessageToMessage(o.user_message),s=this.convertChatMessageToMessage(o.bot_message),a=this.messages.length-1;a>=0&&this.messages[a].isUser&&(this.messages[a]=i),this.messages.push(s),this.currentSessionId||(this.currentSessionId=o.user_message.session),this.isLoading=!1,this.shouldScrollToBottom=!0},error:o=>{console.error("Error sending message:",o),this.messages.push({text:"Sorry, there was an error processing your message. Please try again.",isUser:!1,timestamp:new Date}),this.isLoading=!1,this.shouldScrollToBottom=!0}})}clearHistory(){this.messages=[],this.currentPage=1,this.hasNextPage=!1,this.initialLoadComplete=!1,this.userHasScrolled=!1,this.scrollTimeout&&clearTimeout(this.scrollTimeout)}refreshHistory(){this.currentPage=1,this.initialLoadComplete=!1,this.userHasScrolled=!1,this.scrollTimeout&&clearTimeout(this.scrollTimeout),this.loadChatHistory()}onKeyPress(n){"Enter"===n.key&&!n.shiftKey&&(n.preventDefault(),this.sendMessage())}adjustTextareaHeight(n){const r=n.target;r.style.height="auto",r.style.height=Math.min(r.scrollHeight,120)+"px"}scrollToBottom(){setTimeout(()=>{if(this.messagesContainer){const n=this.messagesContainer.nativeElement;n.scrollTop=n.scrollHeight}},50)}formatMessageText(n){if(!n)return"";try{return Q.setOptions({breaks:!0,gfm:!0}),Q.parse(n).replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,"").replace(/javascript:/gi,"").replace(/on\w+\s*=/gi,"")}catch(r){return console.error("Error formatting message text:",r),n.replace(/\n/g,"<br>")}}ngOnDestroy(){this.scrollTimeout&&clearTimeout(this.scrollTimeout)}static#e=this.\u0275fac=function(r){return new(r||e)(D(Ih))};static#t=this.\u0275cmp=Ar({type:e,selectors:[["app-chat"]],viewQuery:function(r,o){if(1&r&&yC(c$,5),2&r){let i;jd(i=function $d(){return function CP(e,t){return e[Wt].queries[t].queryList}(v(),Ig())}())&&(o.messagesContainer=i.first)}},decls:3,vars:2,consts:[["class","chat-container",4,"ngIf"],["class","auth-loading",4,"ngIf"],[1,"chat-container"],["class","messages-container",3,"scroll",4,"ngIf"],[1,"input-container"],[1,"input-wrapper"],["placeholder","Ask anything","rows","1",1,"message-input",3,"ngModel","ngModelChange","keydown","input"],["messageTextarea",""],[1,"send-button",3,"disabled","click"],["width","24","height","24","viewBox","0 0 24 24","fill","none","xmlns","http://www.w3.org/2000/svg"],["d","M2 21L23 12L2 3V10L17 12L2 14V21Z","fill","currentColor"],[1,"messages-container",3,"scroll"],["messagesContainer",""],["class","pagination-loading",4,"ngIf"],["class","message",3,"ngClass",4,"ngFor","ngForOf"],["class","message bot-message",4,"ngIf"],[1,"pagination-loading"],[1,"loading-spinner"],[1,"message",3,"ngClass"],[1,"message-content"],[1,"message-text",3,"innerHTML"],[1,"message-time"],[1,"message","bot-message"],[1,"typing-indicator"],[1,"auth-loading"]],template:function(r,o){1&r&&(Lt(0,"app-anonymous-user"),tn(1,g$,10,7,"div",0),tn(2,m$,4,0,"div",1)),2&r&&(we(1),ke("ngIf",o.isAuthenticated),we(1),ke("ngIf",!o.isAuthenticated))},dependencies:[bf,Ef,Mf,zl,EE,$h,l$,xf],styles:['.chat-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:100%;max-width:900px;margin:0 auto;background:linear-gradient(to bottom,#ffffff 0%,#fefefe 100%);position:relative;overflow:hidden;font-family:var(--font-family-primary);border-radius:var(--radius-2xl);box-shadow:0 10px 40px #00000014,0 4px 12px #0000000a;border:1px solid rgba(255,255,255,.8)}.messages-container[_ngcontent-%COMP%]{flex:1;overflow-y:auto;padding:var(--space-6) var(--space-8) 120px var(--space-8);background:#f9fafb;max-height:calc(100vh - 160px);scroll-behavior:smooth;position:relative;z-index:10}.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px}.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#f1f5f9;border-radius:var(--radius-md)}.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#cbd5e1;border-radius:var(--radius-md);-webkit-transition:var(--transition-fast);transition:var(--transition-fast)}.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#94a3b8}.pagination-loading[_ngcontent-%COMP%]{display:flex;justify-content:center;padding:var(--space-4) 0;margin-bottom:var(--space-4)}.pagination-loading[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%]{width:24px;height:24px;border:2px solid #e2e8f0;border-top:2px solid #bdf2bd;border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite}.message[_ngcontent-%COMP%]{margin-bottom:var(--space-4);display:flex;animation:_ngcontent-%COMP%_fadeInUp .3s ease-out;position:relative}.message.user-message[_ngcontent-%COMP%]{justify-content:flex-end}.message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]{background:linear-gradient(135deg,#d0ceff 0%,#e6e6ff 100%);color:#fff;max-width:75%;border-radius:20px 20px 6px;box-shadow:0 3px 12px #4f46e540;transition:all var(--transition-fast);position:relative;border:none}.message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 6px 20px #4f46e54d}.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   code[_ngcontent-%COMP%]{background:rgba(0,0,0,.15);color:#059669}.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]{background:rgba(0,0,0,.05);border-color:#0000001a}.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]   code[_ngcontent-%COMP%]{color:#047857}.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#059669}.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:#047857}.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]{border-left-color:#059669;background:rgba(0,0,0,.05)}.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{border-color:#0000001a}.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{background:rgba(0,0,0,.05)}.message.bot-message[_ngcontent-%COMP%]{justify-content:flex-start;flex-direction:column;align-items:flex-start}.message.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f8fafc 0%,#f1f5f9 100%);color:var(--color-gray-800);max-width:75%;border-radius:20px 20px 20px 6px;box-shadow:0 2px 8px #0000000f;transition:all var(--transition-fast);position:relative;border:1px solid var(--color-gray-200)}.message.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 4px 12px #00000014;border-color:var(--color-gray-300)}.message.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]:hover   .message-news[_ngcontent-%COMP%]{margin-top:var(--space-3)}.message.bot-message[_ngcontent-%COMP%]   .message.bot-message[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start}.message.bot-message[_ngcontent-%COMP%]   .message-news[_ngcontent-%COMP%]{max-width:80%;margin-left:0;align-self:flex-start}.message-content[_ngcontent-%COMP%]{padding:var(--space-4) var(--space-5);word-wrap:break-word;position:relative}.message-text[_ngcontent-%COMP%]{font-size:var(--font-size-base);line-height:1.6;margin:0;font-weight:var(--font-weight-normal);word-break:break-word}.message-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 var(--space-3) 0}.message-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child{margin-bottom:0}.message-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{margin:var(--space-4) 0 var(--space-2) 0;font-weight:var(--font-weight-semibold);line-height:1.3}.message-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]:first-child{margin-top:0}.message-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:1.5em}.message-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.3em}.message-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.2em}.message-text[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:1.1em}.message-text[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{font-size:1em}.message-text[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   b[_ngcontent-%COMP%]{font-weight:var(--font-weight-semibold)}.message-text[_ngcontent-%COMP%]   em[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-style:italic}.message-text[_ngcontent-%COMP%]   del[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   strike[_ngcontent-%COMP%]{text-decoration:line-through}.message-text[_ngcontent-%COMP%]   u[_ngcontent-%COMP%]{text-decoration:underline}.message-text[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]{margin:var(--space-3) 0;padding-left:var(--space-6)}.message-text[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]:first-child{margin-top:0}.message-text[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]:last-child, .message-text[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]:last-child{margin-bottom:0}.message-text[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin:var(--space-1) 0;line-height:1.5}.message-text[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]{margin:var(--space-4) 0;padding:var(--space-3) var(--space-4);border-left:4px solid #e5e7eb;background:rgba(0,0,0,.02);border-radius:0 var(--radius-sm) var(--radius-sm) 0;font-style:italic}.message-text[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]:first-child{margin-top:0}.message-text[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]:last-child{margin-bottom:0}.message-text[_ngcontent-%COMP%]   code[_ngcontent-%COMP%]{background:rgba(0,0,0,.08);padding:2px 6px;border-radius:var(--radius-sm);font-family:Monaco,Menlo,Ubuntu Mono,monospace;font-size:.9em;color:#e11d48}.message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]{background:#f8fafc;border:1px solid #e2e8f0;border-radius:var(--radius-md);padding:var(--space-4);margin:var(--space-4) 0;overflow-x:auto}.message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]:first-child{margin-top:0}.message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]:last-child{margin-bottom:0}.message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]   code[_ngcontent-%COMP%]{background:none;padding:0;color:#334155;font-size:.875em}.message-text[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#2563eb;text-decoration:underline;transition:var(--transition-fast)}.message-text[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:#1d4ed8;text-decoration:none}.message-text[_ngcontent-%COMP%]   table[_ngcontent-%COMP%]{width:100%;border-collapse:collapse;margin:var(--space-4) 0}.message-text[_ngcontent-%COMP%]   table[_ngcontent-%COMP%]:first-child{margin-top:0}.message-text[_ngcontent-%COMP%]   table[_ngcontent-%COMP%]:last-child{margin-bottom:0}.message-text[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:var(--space-2) var(--space-3);border:1px solid #e2e8f0;text-align:left}.message-text[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{background:#f8fafc;font-weight:var(--font-weight-semibold)}.message-text[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:100%;height:auto;border-radius:var(--radius-sm);margin:var(--space-2) 0}.message-time[_ngcontent-%COMP%]{font-size:var(--font-size-xs);color:#ffffffb3;margin-top:var(--space-2);text-align:right;opacity:.8;transition:var(--transition-fast);font-weight:var(--font-weight-normal)}.bot-message[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%]{text-align:left;color:var(--color-gray-600)}.user-message[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%]{text-align:right;color:var(--color-gray-600)}.message[_ngcontent-%COMP%]:hover   .message-time[_ngcontent-%COMP%]{opacity:1}.input-container[_ngcontent-%COMP%]{padding:var(--space-6) var(--space-8) var(--space-8) var(--space-8);background:linear-gradient(to top,#ffffff 0%,#fefefe 100%);position:absolute;bottom:0;left:0;right:0;transform:none;width:100%;z-index:50;border-top:1px solid rgba(0,0,0,.06);transition:var(--transition-fast);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.input-container.centered[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);width:calc(100% - var(--space-8));max-width:600px;background:linear-gradient(135deg,#ffffff 0%,#fefefe 100%);z-index:50;padding:var(--space-8);bottom:auto;border-radius:var(--radius-2xl);border:1px solid rgba(0,0,0,.06);box-shadow:0 20px 60px #0000001f,0 8px 24px #00000014}.input-container.bottom[_ngcontent-%COMP%]{position:absolute;bottom:0;left:0;right:0;transform:none;border-top:1px solid rgba(0,0,0,.06);background:linear-gradient(to top,#ffffff 0%,#fefefe 100%);width:100%;z-index:50;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.input-wrapper[_ngcontent-%COMP%]{display:flex;align-items:flex-end;gap:var(--space-4);border:2px solid var(--color-gray-200);border-radius:28px;padding:var(--space-4) var(--space-5);background:var(--color-white);transition:all var(--transition-normal);position:relative;box-shadow:0 2px 8px #0000000a}.input-wrapper[_ngcontent-%COMP%]:focus-within{border-color:var(--color-primary);background:var(--color-white);box-shadow:0 0 0 4px #4f46e514,0 4px 12px #00000014;transform:translateY(-1px)}.input-wrapper[_ngcontent-%COMP%]:hover{border-color:var(--color-gray-300);box-shadow:0 4px 12px #0000000f}.message-input[_ngcontent-%COMP%]{flex:1;border:none;outline:none;background:transparent;resize:none;font-size:var(--font-size-base);line-height:1.6;padding:var(--space-2) 0;min-height:28px;max-height:120px;font-family:var(--font-family-primary);overflow-y:auto;transition:var(--transition-fast);color:var(--color-gray-900);font-weight:var(--font-weight-normal)}.message-input[_ngcontent-%COMP%]::placeholder{color:var(--color-gray-500);opacity:1;font-weight:var(--font-weight-normal)}.message-input[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed}.message-input[_ngcontent-%COMP%]::-webkit-scrollbar{width:4px}.message-input[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:transparent}.message-input[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#cbd5e1;border-radius:var(--radius-sm)}.message-input[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#94a3b8}.send-button[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4f46e5 0%,#6366f1 100%);border:none;border-radius:50%;width:44px;height:44px;display:flex;align-items:center;justify-content:center;color:var(--color-white);cursor:pointer;flex-shrink:0;transition:all var(--transition-normal);box-shadow:0 4px 12px #4f46e54d;position:relative;overflow:hidden}.send-button[_ngcontent-%COMP%]:before{content:"";position:absolute;inset:0;background:linear-gradient(135deg,rgba(255,255,255,.2) 0%,transparent 50%);border-radius:50%;opacity:0;transition:opacity var(--transition-fast)}.send-button[_ngcontent-%COMP%]:hover:not(:disabled){transform:translateY(-2px) scale(1.05);box-shadow:0 8px 20px #4f46e566}.send-button[_ngcontent-%COMP%]:hover:not(:disabled):before{opacity:1}.send-button[_ngcontent-%COMP%]:active:not(:disabled){transform:translateY(-1px) scale(1.02);box-shadow:0 4px 12px #4f46e54d}.send-button[_ngcontent-%COMP%]:disabled{background:var(--color-gray-400);cursor:not-allowed;transform:none;box-shadow:0 2px 4px #0000001a}.send-button[_ngcontent-%COMP%]:disabled:before{display:none}.send-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{width:20px;height:20px;transition:var(--transition-fast);transform:translate(1px)}.send-button[_ngcontent-%COMP%]:hover:not(:disabled)   svg[_ngcontent-%COMP%]{transform:translate(1px) scale(1.1)}.typing-indicator[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--space-1);padding:var(--space-1) 0}.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{width:8px;height:8px;background:#94a3b8;border-radius:50%;animation:_ngcontent-%COMP%_typing 1.4s infinite ease-in-out}.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1){animation-delay:0s}.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2){animation-delay:.2s}.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(3){animation-delay:.4s}.auth-loading[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:center;align-items:center;height:100vh;color:var(--color-gray-600);background:linear-gradient(135deg,var(--color-gray-50) 0%,var(--color-white) 50%,#f0f4f8 100%)}.auth-loading[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%]{width:48px;height:48px;border:4px solid var(--color-gray-200);border-top:4px solid var(--color-accent);border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite;margin-bottom:var(--space-4)}.auth-loading[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:var(--font-size-lg);color:var(--color-gray-600);margin:0;font-weight:var(--font-weight-medium)}@keyframes _ngcontent-%COMP%_typing{0%,60%,to{opacity:.3;transform:scale(.7) translateY(0)}30%{opacity:1;transform:scale(1.3) translateY(-6px)}}@keyframes _ngcontent-%COMP%_slideInUp{0%{opacity:0;transform:translateY(40px) scale(.9)}to{opacity:1;transform:translateY(0) scale(1)}}@media (max-width: 768px){.chat-container[_ngcontent-%COMP%]{max-width:100%;height:100vh;border-radius:0;border:none}.messages-container[_ngcontent-%COMP%]{padding:var(--space-5) var(--space-5) 140px var(--space-5)}.input-container[_ngcontent-%COMP%]{padding:var(--space-5) var(--space-5) var(--space-6) var(--space-5);max-width:100%}.input-container.centered[_ngcontent-%COMP%]{max-width:100%;padding:var(--space-5);margin:0 var(--space-4);width:calc(100% - var(--space-8))}.message[_ngcontent-%COMP%]{margin-bottom:var(--space-5)}.message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%], .message.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]{max-width:88%}.input-wrapper[_ngcontent-%COMP%]{padding:var(--space-1) var(--space-2) var(--space-1) var(--space-4);gap:var(--space-2)}.send-button[_ngcontent-%COMP%]{width:42px;height:42px}.send-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{width:18px;height:18px}}*[_ngcontent-%COMP%]{transition:color var(--transition-fast),background-color var(--transition-fast),border-color var(--transition-fast),transform var(--transition-fast),box-shadow var(--transition-fast)}.send-button[_ngcontent-%COMP%]:focus{outline:2px solid var(--color-primary);outline-offset:3px}.message-input[_ngcontent-%COMP%]:focus{box-shadow:none}.message-content[_ngcontent-%COMP%]::selection{background:rgba(16,185,129,.2)}.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]::selection{background:rgba(255,255,255,.3)}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_typing{0%,60%,to{transform:translateY(0);opacity:.4}30%{transform:translateY(-10px);opacity:1}}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}']})}return e})();function v$(e,t){if(1&e&&(se(0,"a",5)(1,"div",6)(2,"span",7),yt(3),Ua(4,"titlecase"),fe(),se(5,"span",8),yt(6),fe()(),se(7,"h3",9),yt(8),fe(),se(9,"p",10),yt(10),fe(),se(11,"div",11)(12,"span",12),yt(13),Ua(14,"date"),fe(),se(15,"span",13),yt(16,"Read \u2192"),fe()()()),2&e){const n=t.$implicit;ke("href",n.link,wu),we(3),Cn(function iC(e,t,n){const r=e+B,o=v(),i=Lr(o,r);return ji(o,r)?eC(o,Ge(),t,i.transform,n,i):i.transform(n)}(4,6,n.country)),we(3),Cn(n.source),we(2),Cn(n.title),we(2),Cn(n.description),we(3),Cn(Od(14,8,n.published,"medium"))}}function _$(e,t){if(1&e){const n=Fa();se(0,"div",14)(1,"p"),yt(2,"No articles found. Try adjusting your search or filters."),fe(),se(3,"button",15),je("click",function(){return or(n),ir(Vt(2).clearFilters())}),yt(4," Clear All Filters "),fe()()}}const C$=function(e){return{compact:e}};function D$(e,t){if(1&e&&(se(0,"div",1)(1,"div",2),tn(2,v$,17,11,"a",3),fe(),tn(3,_$,5,0,"div",4),fe()),2&e){const n=Vt();ke("ngClass",function J_(e,t,n,r){return eC(v(),Ge(),e,t,n,r)}(4,C$,n.compact)),we(2),ke("ngForOf",n.filteredNews)("ngForTrackBy",n.trackByTitle),we(1),ke("ngIf",0===n.filteredNews.length&&(n.searchTerm||n.selectedCountry||n.selectedSource))}}let w$=(()=>{class e{constructor(n){this.authService=n,this.showTitle=!0,this.compact=!1,this.news=[],this.loading=!1,this.error="",this.filteredNews=[],this.searchTerm="",this.selectedCountry="",this.selectedSource="",this.availableCountries=[],this.availableSources=[]}ngOnInit(){this.loadNews()}loadNews(){this.loading=!0,this.error="",this.authService.ensureAuthenticated().subscribe({next:()=>{this.authService.fetchNews("poland").subscribe({next:n=>{this.news=n.news||[],this.initializeFilters(),this.applyFilters(),this.loading=!1},error:n=>{this.error="Failed to load news",this.loading=!1,console.error("Error loading news:",n)}})},error:n=>{this.error="Authentication failed",this.loading=!1,console.error("Auth error:",n)}})}initializeFilters(){this.availableCountries=[...new Set(this.news.map(n=>n.country))].sort(),this.availableSources=[...new Set(this.news.map(n=>n.source))].sort()}applyFilters(){let n=[...this.news];if(this.searchTerm.trim()){const r=this.searchTerm.toLowerCase();n=n.filter(o=>o.title.toLowerCase().includes(r)||o.description.toLowerCase().includes(r)||o.source.toLowerCase().includes(r))}this.selectedCountry&&(n=n.filter(r=>r.country===this.selectedCountry)),this.selectedSource&&(n=n.filter(r=>r.source===this.selectedSource)),this.filteredNews=n}onSearchChange(n){this.searchTerm=n.target.value,this.applyFilters()}onCountryChange(n){this.selectedCountry=n.target.value,this.applyFilters()}onSourceChange(n){this.selectedSource=n.target.value,this.applyFilters()}clearFilters(){this.searchTerm="",this.selectedCountry="",this.selectedSource="",this.applyFilters()}trackByTitle(n,r){return r.title}refreshNews(){this.loadNews()}static#e=this.\u0275fac=function(r){return new(r||e)(D(Ih))};static#t=this.\u0275cmp=Ar({type:e,selectors:[["app-news"]],inputs:{showTitle:"showTitle",compact:"compact"},decls:1,vars:1,consts:[["class","news-wrapper",3,"ngClass",4,"ngIf"],[1,"news-wrapper",3,"ngClass"],[1,"news-grid"],["class","news-card","target","_blank","rel","noopener noreferrer",3,"href",4,"ngFor","ngForOf","ngForTrackBy"],["class","no-results",4,"ngIf"],["target","_blank","rel","noopener noreferrer",1,"news-card",3,"href"],[1,"news-card-header"],[1,"badge"],[1,"source"],[1,"title"],[1,"description"],[1,"news-card-footer"],[1,"published"],[1,"cta"],[1,"no-results"],["type","button",1,"clear-filters-btn",3,"click"]],template:function(r,o){1&r&&tn(0,D$,4,6,"div",0),2&r&&ke("ngIf",o.news&&o.news.length)},dependencies:[bf,Ef,Mf,VD,xf],styles:["[_nghost-%COMP%]{display:block;height:100%}.news-card[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{line-height:1.3;color:var(--color-gray-900);font-weight:var(--font-weight-semibold)}.news-card[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%]{display:-webkit-box;-webkit-line-clamp:3;-webkit-box-orient:vertical;overflow:hidden;color:var(--color-gray-600);line-height:1.5}.news-wrapper.compact[_ngcontent-%COMP%]   .news-card[_ngcontent-%COMP%]{transition:all var(--transition-fast)}.news-wrapper.compact[_ngcontent-%COMP%]   .news-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:var(--shadow-md)}.news-grid[_ngcontent-%COMP%]{overflow-x:hidden;padding-right:4px;scrollbar-width:thin;scrollbar-color:var(--color-gray-300, #cbd5e1) var(--color-gray-100, #f1f5f9)}.news-grid[_ngcontent-%COMP%]::-webkit-scrollbar{width:8px}.news-grid[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:var(--color-gray-100, #f1f5f9);border-radius:4px}.news-grid[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:var(--color-gray-300, #cbd5e1);border-radius:4px}.news-grid[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:var(--color-gray-400, #94a3b8)}.news-wrapper.compact[_ngcontent-%COMP%]   .news-grid[_ngcontent-%COMP%]{max-height:400px}.news-controls[_ngcontent-%COMP%]{margin-bottom:var(--space-6);padding:var(--space-4);background:var(--color-white);border:1px solid var(--color-gray-200);border-radius:var(--radius-lg);box-shadow:var(--shadow-sm)}.search-container[_ngcontent-%COMP%]{margin-bottom:var(--space-4)}.search-input[_ngcontent-%COMP%]{width:100%;max-width:400px;padding:var(--space-3) var(--space-4);border:2px solid var(--color-gray-200);border-radius:var(--radius-lg);font-size:var(--font-size-base);font-family:var(--font-family-primary);transition:border-color var(--transition-fast)}.search-input[_ngcontent-%COMP%]:focus{outline:none;border-color:var(--color-primary)}.search-input[_ngcontent-%COMP%]::placeholder{color:var(--color-gray-500)}.filter-controls[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:var(--space-3);align-items:center}.filter-select[_ngcontent-%COMP%]{padding:var(--space-2) var(--space-3);border:1px solid var(--color-gray-300);border-radius:var(--radius-md);font-size:var(--font-size-sm);background:var(--color-white);cursor:pointer;min-width:120px}.filter-select[_ngcontent-%COMP%]:focus{outline:none;border-color:var(--color-primary)}.clear-filters-btn[_ngcontent-%COMP%]{padding:var(--space-2) var(--space-4);background:var(--color-gray-100);border:1px solid var(--color-gray-300);border-radius:var(--radius-md);font-size:var(--font-size-sm);color:var(--color-gray-700);cursor:pointer;transition:all var(--transition-fast)}.clear-filters-btn[_ngcontent-%COMP%]:hover{background:var(--color-gray-200)}.no-results[_ngcontent-%COMP%]{text-align:center;padding:var(--space-8);color:var(--color-gray-600)}.no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-bottom:var(--space-4)}@media (max-width: 768px){.filter-controls[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch}.filter-select[_ngcontent-%COMP%]{min-width:auto}.news-wrapper.compact[_ngcontent-%COMP%]   .news-grid[_ngcontent-%COMP%]{max-height:300px}}"]})}return e})(),b$=(()=>{class e{constructor(){this.title="News Agent",this.sampleNews=[{country:"ukraine",source:"Kyiv Independent",title:"Ukraine receives new military aid package",link:"https://example.com/news1",published:"2024-01-15T10:30:00Z",description:"NATO allies announce comprehensive military aid package including advanced defense systems."},{country:"poland",source:"Warsaw Times",title:"Poland strengthens eastern border security",link:"https://example.com/news2",published:"2024-01-14T15:45:00Z",description:"Polish government implements enhanced security protocols along its eastern border."},{country:"russia",source:"Moscow Herald",title:"Russian economic indicators show mixed results",link:"https://example.com/news3",published:"2024-01-13T09:15:00Z",description:"Latest economic data from Russia reveals varying performance across different sectors."},{country:"belarus",source:"Minsk Daily",title:"Belarus announces new agricultural initiatives",link:"https://example.com/news4",published:"2024-01-12T14:20:00Z",description:"Government unveils comprehensive agricultural development program."},{country:"ukraine",source:"Ukrainian Voice",title:"Reconstruction efforts accelerate in liberated territories",link:"https://example.com/news5",published:"2024-01-11T11:00:00Z",description:"International cooperation drives rapid reconstruction of infrastructure."}]}static#e=this.\u0275fac=function(r){return new(r||e)};static#t=this.\u0275cmp=Ar({type:e,selectors:[["app-root"]],decls:9,vars:1,consts:[[1,"app-container"],[1,"app-header"],[1,"main-content"],[1,"chat-section"],[1,"news-section"]],template:function(r,o){1&r&&(se(0,"div",0)(1,"header",1)(2,"h1"),yt(3),fe()(),se(4,"main",2)(5,"div",3),Lt(6,"app-chat"),fe(),se(7,"div",4),Lt(8,"app-news"),fe()()()),2&r&&(we(3),Cn(o.title))},dependencies:[y$,w$],styles:['.app-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:100vh;background:transparent;overflow:hidden}.app-header[_ngcontent-%COMP%]{flex-shrink:0;padding:var(--space-4) 0;background-color:transparent}.app-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{text-align:center;background:linear-gradient(135deg,#4f46e5 0%,#6366f1 50%,#8b5cf6 100%);-webkit-text-fill-color:transparent;-webkit-background-clip:text;background-clip:text;margin:0;padding:var(--space-4) 0 var(--space-2) 0;font-size:var(--font-size-3xl);font-weight:var(--font-weight-bold);letter-spacing:-.025em;position:relative}.app-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]:after{content:"";position:absolute;bottom:0;left:50%;transform:translate(-50%);width:60px;height:3px;background:linear-gradient(90deg,#4f46e5 0%,#8b5cf6 100%);border-radius:2px}.main-content[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:row;overflow:hidden;background-color:transparent;gap:var(--space-4);padding:0 var(--space-4) var(--space-4) var(--space-4)}.chat-section[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;overflow:hidden;min-width:0}.news-section[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;overflow:hidden;min-width:0;height:100%}@media (max-width: 768px){.main-content[_ngcontent-%COMP%]{flex-direction:column;gap:var(--space-2)}.chat-section[_ngcontent-%COMP%], .news-section[_ngcontent-%COMP%]{flex:1;min-height:0}}@media (max-width: 480px){.main-content[_ngcontent-%COMP%]{padding:0 var(--space-2) var(--space-2) var(--space-2)}}.sr-only[_ngcontent-%COMP%]{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border:0}.text-center[_ngcontent-%COMP%]{text-align:center}.text-left[_ngcontent-%COMP%]{text-align:left}.text-right[_ngcontent-%COMP%]{text-align:right}.mb-0[_ngcontent-%COMP%]{margin-bottom:0}.mb-2[_ngcontent-%COMP%]{margin-bottom:var(--space-2)}.mb-4[_ngcontent-%COMP%]{margin-bottom:var(--space-4)}.mb-6[_ngcontent-%COMP%]{margin-bottom:var(--space-6)}.mt-0[_ngcontent-%COMP%]{margin-top:0}.mt-2[_ngcontent-%COMP%]{margin-top:var(--space-2)}.mt-4[_ngcontent-%COMP%]{margin-top:var(--space-4)}.mt-6[_ngcontent-%COMP%]{margin-top:var(--space-6)}']})}return e})(),E$=(()=>{class e{static#e=this.\u0275fac=function(r){return new(r||e)};static#t=this.\u0275mod=wt({type:e,bootstrap:[b$]});static#n=this.\u0275inj=ct({imports:[dF,XV,s$,rj,a$]})}return e})();cF().bootstrapModule(E$).catch(e=>console.error(e))}},ee=>{ee(ee.s=382)}]);