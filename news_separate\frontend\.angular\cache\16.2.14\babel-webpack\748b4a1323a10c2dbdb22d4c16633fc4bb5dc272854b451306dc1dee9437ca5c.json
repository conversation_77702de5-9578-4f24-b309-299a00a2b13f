{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../auth.service\";\nimport * as i2 from \"@angular/common\";\nfunction NewsComponent_div_0_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 5)(1, \"div\", 6)(2, \"span\", 7);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 8);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"h3\", 9);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 10);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 11)(12, \"span\", 12);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 13);\n    i0.ɵɵtext(16, \"Read \\u2192\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"href\", item_r3.link, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 6, item_r3.country));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r3.source);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r3.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r3.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(14, 8, item_r3.published, \"medium\"));\n  }\n}\nfunction NewsComponent_div_0_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"p\");\n    i0.ɵɵtext(2, \"No articles found. Try adjusting your search or filters.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function NewsComponent_div_0_div_3_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.clearFilters());\n    });\n    i0.ɵɵtext(4, \" Clear All Filters \");\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"compact\": a0\n  };\n};\nfunction NewsComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n    i0.ɵɵtemplate(2, NewsComponent_div_0_a_2_Template, 17, 11, \"a\", 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, NewsComponent_div_0_div_3_Template, 5, 0, \"div\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c0, ctx_r0.compact));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.filteredNews)(\"ngForTrackBy\", ctx_r0.trackByTitle);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.filteredNews.length === 0 && (ctx_r0.searchTerm || ctx_r0.selectedCountry || ctx_r0.selectedSource));\n  }\n}\nexport class NewsComponent {\n  constructor(authService) {\n    this.authService = authService;\n    this.showTitle = true;\n    this.compact = false;\n    // News data\n    this.news = [];\n    this.loading = false;\n    this.error = '';\n    // Filter properties\n    this.filteredNews = [];\n    this.searchTerm = '';\n    this.selectedCountry = '';\n    this.selectedSource = '';\n    // Available filter options\n    this.availableCountries = [];\n    this.availableSources = [];\n    this.userCountry = '';\n  }\n  ngOnInit() {\n    this.detectUserCountry();\n  }\n  detectUserCountry() {\n    fetch('https://ipwhois.app/json/').then(response => response.json()).then(data => {\n      this.userCountry = data.country_code.toLowerCase();\n      console.log('User country detected:', data.country, data.country_code);\n      this.userCountry = 'poland';\n      this.loadNews(this.userCountry);\n    }).catch(error => {\n      console.error('IP detection failed:', error);\n    });\n  }\n  loadNews(country = 'poland') {\n    this.loading = true;\n    this.error = '';\n    this.authService.ensureAuthenticated().subscribe({\n      next: () => {\n        this.authService.fetchNews(country).subscribe({\n          next: response => {\n            this.news = response.news || [];\n            this.initializeFilters();\n            this.applyFilters();\n            this.loading = false;\n          },\n          error: error => {\n            this.error = 'Failed to load news';\n            this.loading = false;\n            console.error('Error loading news:', error);\n          }\n        });\n      },\n      error: error => {\n        this.error = 'Authentication failed';\n        this.loading = false;\n        console.error('Auth error:', error);\n      }\n    });\n  }\n  initializeFilters() {\n    this.availableCountries = [...new Set(this.news.map(item => item.country))].sort();\n    this.availableSources = [...new Set(this.news.map(item => item.source))].sort();\n  }\n  applyFilters() {\n    let filtered = [...this.news];\n    // Apply search filter\n    if (this.searchTerm.trim()) {\n      const searchLower = this.searchTerm.toLowerCase();\n      filtered = filtered.filter(item => item.title.toLowerCase().includes(searchLower) || item.description.toLowerCase().includes(searchLower) || item.source.toLowerCase().includes(searchLower));\n    }\n    // Apply country filter\n    if (this.selectedCountry) {\n      filtered = filtered.filter(item => item.country === this.selectedCountry);\n    }\n    // Apply source filter\n    if (this.selectedSource) {\n      filtered = filtered.filter(item => item.source === this.selectedSource);\n    }\n    this.filteredNews = filtered;\n  }\n  onSearchChange(event) {\n    this.searchTerm = event.target.value;\n    this.applyFilters();\n  }\n  onCountryChange(event) {\n    this.selectedCountry = event.target.value;\n    this.applyFilters();\n  }\n  onSourceChange(event) {\n    this.selectedSource = event.target.value;\n    this.applyFilters();\n  }\n  clearFilters() {\n    this.searchTerm = '';\n    this.selectedCountry = '';\n    this.selectedSource = '';\n    this.applyFilters();\n  }\n  trackByTitle(index, item) {\n    return item.title;\n  }\n  refreshNews() {\n    this.loadNews();\n  }\n  static #_ = this.ɵfac = function NewsComponent_Factory(t) {\n    return new (t || NewsComponent)(i0.ɵɵdirectiveInject(i1.AuthService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: NewsComponent,\n    selectors: [[\"app-news\"]],\n    inputs: {\n      showTitle: \"showTitle\",\n      compact: \"compact\"\n    },\n    decls: 1,\n    vars: 1,\n    consts: [[\"class\", \"news-wrapper\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"news-wrapper\", 3, \"ngClass\"], [1, \"news-grid\"], [\"class\", \"news-card\", \"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 3, \"href\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"no-results\", 4, \"ngIf\"], [\"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 1, \"news-card\", 3, \"href\"], [1, \"news-card-header\"], [1, \"badge\"], [1, \"source\"], [1, \"title\"], [1, \"description\"], [1, \"news-card-footer\"], [1, \"published\"], [1, \"cta\"], [1, \"no-results\"], [\"type\", \"button\", 1, \"clear-filters-btn\", 3, \"click\"]],\n    template: function NewsComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, NewsComponent_div_0_Template, 4, 6, \"div\", 0);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.news && ctx.news.length);\n      }\n    },\n    dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i2.TitleCasePipe, i2.DatePipe],\n    styles: [\"[_nghost-%COMP%] {\\n  display: block;\\n  height: 100%;\\n}\\n\\n\\n\\n.news-card[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%] {\\n  line-height: 1.3;\\n  color: var(--color-gray-900);\\n  font-weight: var(--font-weight-semibold);\\n}\\n\\n.news-card[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%] {\\n  display: -webkit-box;\\n  -webkit-line-clamp: 3;\\n  -webkit-box-orient: vertical;\\n  overflow: hidden;\\n  color: var(--color-gray-600);\\n  line-height: 1.5;\\n}\\n\\n\\n\\n.news-wrapper.compact[_ngcontent-%COMP%]   .news-card[_ngcontent-%COMP%] {\\n  transition: all var(--transition-fast);\\n}\\n.news-wrapper.compact[_ngcontent-%COMP%]   .news-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: var(--shadow-md);\\n}\\n\\n\\n\\n.news-grid[_ngcontent-%COMP%] {\\n  overflow-x: hidden;\\n  padding-right: 4px; \\n\\n  \\n\\n  \\n\\n  scrollbar-width: thin;\\n  scrollbar-color: var(--color-gray-300, #cbd5e1) var(--color-gray-100, #f1f5f9);\\n}\\n.news-grid[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 8px;\\n}\\n.news-grid[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: var(--color-gray-100, #f1f5f9);\\n  border-radius: 4px;\\n}\\n.news-grid[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: var(--color-gray-300, #cbd5e1);\\n  border-radius: 4px;\\n}\\n.news-grid[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: var(--color-gray-400, #94a3b8);\\n}\\n\\n\\n\\n.news-wrapper.compact[_ngcontent-%COMP%]   .news-grid[_ngcontent-%COMP%] {\\n  max-height: 400px;\\n}\\n\\n\\n\\n.news-controls[_ngcontent-%COMP%] {\\n  margin-bottom: var(--space-6);\\n  padding: var(--space-4);\\n  background: var(--color-white);\\n  border: 1px solid var(--color-gray-200);\\n  border-radius: var(--radius-lg);\\n  box-shadow: var(--shadow-sm);\\n}\\n\\n.search-container[_ngcontent-%COMP%] {\\n  margin-bottom: var(--space-4);\\n}\\n\\n.search-input[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 400px;\\n  padding: var(--space-3) var(--space-4);\\n  border: 2px solid var(--color-gray-200);\\n  border-radius: var(--radius-lg);\\n  font-size: var(--font-size-base);\\n  font-family: var(--font-family-primary);\\n  transition: border-color var(--transition-fast);\\n}\\n.search-input[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: var(--color-primary);\\n}\\n.search-input[_ngcontent-%COMP%]::placeholder {\\n  color: var(--color-gray-500);\\n}\\n\\n.filter-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: var(--space-3);\\n  align-items: center;\\n}\\n\\n.filter-select[_ngcontent-%COMP%] {\\n  padding: var(--space-2) var(--space-3);\\n  border: 1px solid var(--color-gray-300);\\n  border-radius: var(--radius-md);\\n  font-size: var(--font-size-sm);\\n  background: var(--color-white);\\n  cursor: pointer;\\n  min-width: 120px;\\n}\\n.filter-select[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  border-color: var(--color-primary);\\n}\\n\\n.clear-filters-btn[_ngcontent-%COMP%] {\\n  padding: var(--space-2) var(--space-4);\\n  background: var(--color-gray-100);\\n  border: 1px solid var(--color-gray-300);\\n  border-radius: var(--radius-md);\\n  font-size: var(--font-size-sm);\\n  color: var(--color-gray-700);\\n  cursor: pointer;\\n  transition: all var(--transition-fast);\\n}\\n.clear-filters-btn[_ngcontent-%COMP%]:hover {\\n  background: var(--color-gray-200);\\n}\\n\\n.no-results[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: var(--space-8);\\n  color: var(--color-gray-600);\\n}\\n.no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-bottom: var(--space-4);\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .filter-controls[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n  }\\n  .filter-select[_ngcontent-%COMP%] {\\n    min-width: auto;\\n  }\\n  \\n\\n  .news-wrapper.compact[_ngcontent-%COMP%]   .news-grid[_ngcontent-%COMP%] {\\n    max-height: 300px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbmV3cy9uZXdzLmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsY0FBQTtFQUNBLFlBQUE7QUFDRjs7QUFFQSxpRkFBQTtBQUNBO0VBQ0UsZ0JBQUE7RUFDQSw0QkFBQTtFQUNBLHdDQUFBO0FBQ0Y7O0FBRUE7RUFDRSxvQkFBQTtFQUNBLHFCQUFBO0VBQ0EsNEJBQUE7RUFDQSxnQkFBQTtFQUNBLDRCQUFBO0VBQ0EsZ0JBQUE7QUFDRjs7QUFFQSxrREFBQTtBQUNBO0VBQ0Usc0NBQUE7QUFDRjtBQUNFO0VBQ0UsMkJBQUE7RUFDQSw0QkFBQTtBQUNKOztBQUdBLGdEQUFBO0FBQ0E7RUFDRSxrQkFBQTtFQUNBLGtCQUFBLEVBQUEsd0JBQUE7RUFFQSw2QkFBQTtFQW1CQSw4QkFBQTtFQUNBLHFCQUFBO0VBQ0EsOEVBQUE7QUFuQkY7QUFERTtFQUNFLFVBQUE7QUFHSjtBQUFFO0VBQ0UsMENBQUE7RUFDQSxrQkFBQTtBQUVKO0FBQ0U7RUFDRSwwQ0FBQTtFQUNBLGtCQUFBO0FBQ0o7QUFDSTtFQUNFLDBDQUFBO0FBQ047O0FBUUEsc0NBQUE7QUFDQTtFQUNFLGlCQUFBO0FBTEY7O0FBUUEsK0JBQUE7QUFDQTtFQUNFLDZCQUFBO0VBQ0EsdUJBQUE7RUFDQSw4QkFBQTtFQUNBLHVDQUFBO0VBQ0EsK0JBQUE7RUFDQSw0QkFBQTtBQUxGOztBQVFBO0VBQ0UsNkJBQUE7QUFMRjs7QUFRQTtFQUNFLFdBQUE7RUFDQSxnQkFBQTtFQUNBLHNDQUFBO0VBQ0EsdUNBQUE7RUFDQSwrQkFBQTtFQUNBLGdDQUFBO0VBQ0EsdUNBQUE7RUFDQSwrQ0FBQTtBQUxGO0FBT0U7RUFDRSxhQUFBO0VBQ0Esa0NBQUE7QUFMSjtBQVFFO0VBQ0UsNEJBQUE7QUFOSjs7QUFVQTtFQUNFLGFBQUE7RUFDQSxlQUFBO0VBQ0EsbUJBQUE7RUFDQSxtQkFBQTtBQVBGOztBQVVBO0VBQ0Usc0NBQUE7RUFDQSx1Q0FBQTtFQUNBLCtCQUFBO0VBQ0EsOEJBQUE7RUFDQSw4QkFBQTtFQUNBLGVBQUE7RUFDQSxnQkFBQTtBQVBGO0FBU0U7RUFDRSxhQUFBO0VBQ0Esa0NBQUE7QUFQSjs7QUFXQTtFQUNFLHNDQUFBO0VBQ0EsaUNBQUE7RUFDQSx1Q0FBQTtFQUNBLCtCQUFBO0VBQ0EsOEJBQUE7RUFDQSw0QkFBQTtFQUNBLGVBQUE7RUFDQSxzQ0FBQTtBQVJGO0FBVUU7RUFDRSxpQ0FBQTtBQVJKOztBQVlBO0VBQ0Usa0JBQUE7RUFDQSx1QkFBQTtFQUNBLDRCQUFBO0FBVEY7QUFXRTtFQUNFLDZCQUFBO0FBVEo7O0FBYUEsZUFBQTtBQUNBO0VBQ0U7SUFDRSxzQkFBQTtJQUNBLG9CQUFBO0VBVkY7RUFhQTtJQUNFLGVBQUE7RUFYRjtFQWNBLCtDQUFBO0VBRUE7SUFDRSxpQkFBQTtFQWJGO0FBQ0YiLCJzb3VyY2VzQ29udGVudCI6WyI6aG9zdCB7XHJcbiAgZGlzcGxheTogYmxvY2s7XHJcbiAgaGVpZ2h0OiAxMDAlO1xyXG59XHJcblxyXG4vKiBLZWVwIGNvbXBvbmVudC1zcGVjaWZpYyBvdmVycmlkZXMgbWluaW1hbDsgbW9zdCBzdHlsZXMgaW4gZ2xvYmFsIHN0eWxlcy5zY3NzICovXHJcbi5uZXdzLWNhcmQgLnRpdGxlIHtcclxuICBsaW5lLWhlaWdodDogMS4zO1xyXG4gIGNvbG9yOiB2YXIoLS1jb2xvci1ncmF5LTkwMCk7XHJcbiAgZm9udC13ZWlnaHQ6IHZhcigtLWZvbnQtd2VpZ2h0LXNlbWlib2xkKTtcclxufVxyXG5cclxuLm5ld3MtY2FyZCAuZGVzY3JpcHRpb24ge1xyXG4gIGRpc3BsYXk6IC13ZWJraXQtYm94O1xyXG4gIC13ZWJraXQtbGluZS1jbGFtcDogMztcclxuICAtd2Via2l0LWJveC1vcmllbnQ6IHZlcnRpY2FsO1xyXG4gIG92ZXJmbG93OiBoaWRkZW47XHJcbiAgY29sb3I6IHZhcigtLWNvbG9yLWdyYXktNjAwKTtcclxuICBsaW5lLWhlaWdodDogMS41O1xyXG59XHJcblxyXG4vKiBFbmhhbmNlZCBob3ZlciBlZmZlY3RzIGZvciBuZXdzIGNhcmRzIGluIGNoYXQgKi9cclxuLm5ld3Mtd3JhcHBlci5jb21wYWN0IC5uZXdzLWNhcmQge1xyXG4gIHRyYW5zaXRpb246IGFsbCB2YXIoLS10cmFuc2l0aW9uLWZhc3QpO1xyXG5cclxuICAmOmhvdmVyIHtcclxuICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTtcclxuICAgIGJveC1zaGFkb3c6IHZhcigtLXNoYWRvdy1tZCk7XHJcbiAgfVxyXG59XHJcblxyXG4vKiBOZXdzIEdyaWQgd2l0aCBTY3JvbGxiYXIgLSBGdWxsIGhlaWdodCBtb2RlICovXHJcbi5uZXdzLWdyaWQge1xyXG4gIG92ZXJmbG93LXg6IGhpZGRlbjtcclxuICBwYWRkaW5nLXJpZ2h0OiA0cHg7IC8qIFNwYWNlIGZvciBzY3JvbGxiYXIgKi9cclxuXHJcbiAgLyogQ3VzdG9tIHNjcm9sbGJhciBzdHlsaW5nICovXHJcbiAgJjo6LXdlYmtpdC1zY3JvbGxiYXIge1xyXG4gICAgd2lkdGg6IDhweDtcclxuICB9XHJcblxyXG4gICY6Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHtcclxuICAgIGJhY2tncm91bmQ6IHZhcigtLWNvbG9yLWdyYXktMTAwLCAjZjFmNWY5KTtcclxuICAgIGJvcmRlci1yYWRpdXM6IDRweDtcclxuICB9XHJcblxyXG4gICY6Oi13ZWJraXQtc2Nyb2xsYmFyLXRodW1iIHtcclxuICAgIGJhY2tncm91bmQ6IHZhcigtLWNvbG9yLWdyYXktMzAwLCAjY2JkNWUxKTtcclxuICAgIGJvcmRlci1yYWRpdXM6IDRweDtcclxuXHJcbiAgICAmOmhvdmVyIHtcclxuICAgICAgYmFja2dyb3VuZDogdmFyKC0tY29sb3ItZ3JheS00MDAsICM5NGEzYjgpO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLyogRmlyZWZveCBzY3JvbGxiYXIgc3R5bGluZyAqL1xyXG4gIHNjcm9sbGJhci13aWR0aDogdGhpbjtcclxuICBzY3JvbGxiYXItY29sb3I6IHZhcigtLWNvbG9yLWdyYXktMzAwLCAjY2JkNWUxKSB2YXIoLS1jb2xvci1ncmF5LTEwMCwgI2YxZjVmOSk7XHJcbn1cclxuXHJcbi8qIENvbXBhY3QgbW9kZSAtIHNtYWxsZXIgbWF4IGhlaWdodCAqL1xyXG4ubmV3cy13cmFwcGVyLmNvbXBhY3QgLm5ld3MtZ3JpZCB7XHJcbiAgbWF4LWhlaWdodDogNDAwcHg7XHJcbn1cclxuXHJcbi8qIFNlYXJjaCBhbmQgRmlsdGVyIENvbnRyb2xzICovXHJcbi5uZXdzLWNvbnRyb2xzIHtcclxuICBtYXJnaW4tYm90dG9tOiB2YXIoLS1zcGFjZS02KTtcclxuICBwYWRkaW5nOiB2YXIoLS1zcGFjZS00KTtcclxuICBiYWNrZ3JvdW5kOiB2YXIoLS1jb2xvci13aGl0ZSk7XHJcbiAgYm9yZGVyOiAxcHggc29saWQgdmFyKC0tY29sb3ItZ3JheS0yMDApO1xyXG4gIGJvcmRlci1yYWRpdXM6IHZhcigtLXJhZGl1cy1sZyk7XHJcbiAgYm94LXNoYWRvdzogdmFyKC0tc2hhZG93LXNtKTtcclxufVxyXG5cclxuLnNlYXJjaC1jb250YWluZXIge1xyXG4gIG1hcmdpbi1ib3R0b206IHZhcigtLXNwYWNlLTQpO1xyXG59XHJcblxyXG4uc2VhcmNoLWlucHV0IHtcclxuICB3aWR0aDogMTAwJTtcclxuICBtYXgtd2lkdGg6IDQwMHB4O1xyXG4gIHBhZGRpbmc6IHZhcigtLXNwYWNlLTMpIHZhcigtLXNwYWNlLTQpO1xyXG4gIGJvcmRlcjogMnB4IHNvbGlkIHZhcigtLWNvbG9yLWdyYXktMjAwKTtcclxuICBib3JkZXItcmFkaXVzOiB2YXIoLS1yYWRpdXMtbGcpO1xyXG4gIGZvbnQtc2l6ZTogdmFyKC0tZm9udC1zaXplLWJhc2UpO1xyXG4gIGZvbnQtZmFtaWx5OiB2YXIoLS1mb250LWZhbWlseS1wcmltYXJ5KTtcclxuICB0cmFuc2l0aW9uOiBib3JkZXItY29sb3IgdmFyKC0tdHJhbnNpdGlvbi1mYXN0KTtcclxuXHJcbiAgJjpmb2N1cyB7XHJcbiAgICBvdXRsaW5lOiBub25lO1xyXG4gICAgYm9yZGVyLWNvbG9yOiB2YXIoLS1jb2xvci1wcmltYXJ5KTtcclxuICB9XHJcblxyXG4gICY6OnBsYWNlaG9sZGVyIHtcclxuICAgIGNvbG9yOiB2YXIoLS1jb2xvci1ncmF5LTUwMCk7XHJcbiAgfVxyXG59XHJcblxyXG4uZmlsdGVyLWNvbnRyb2xzIHtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGZsZXgtd3JhcDogd3JhcDtcclxuICBnYXA6IHZhcigtLXNwYWNlLTMpO1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbn1cclxuXHJcbi5maWx0ZXItc2VsZWN0IHtcclxuICBwYWRkaW5nOiB2YXIoLS1zcGFjZS0yKSB2YXIoLS1zcGFjZS0zKTtcclxuICBib3JkZXI6IDFweCBzb2xpZCB2YXIoLS1jb2xvci1ncmF5LTMwMCk7XHJcbiAgYm9yZGVyLXJhZGl1czogdmFyKC0tcmFkaXVzLW1kKTtcclxuICBmb250LXNpemU6IHZhcigtLWZvbnQtc2l6ZS1zbSk7XHJcbiAgYmFja2dyb3VuZDogdmFyKC0tY29sb3Itd2hpdGUpO1xyXG4gIGN1cnNvcjogcG9pbnRlcjtcclxuICBtaW4td2lkdGg6IDEyMHB4O1xyXG5cclxuICAmOmZvY3VzIHtcclxuICAgIG91dGxpbmU6IG5vbmU7XHJcbiAgICBib3JkZXItY29sb3I6IHZhcigtLWNvbG9yLXByaW1hcnkpO1xyXG4gIH1cclxufVxyXG5cclxuLmNsZWFyLWZpbHRlcnMtYnRuIHtcclxuICBwYWRkaW5nOiB2YXIoLS1zcGFjZS0yKSB2YXIoLS1zcGFjZS00KTtcclxuICBiYWNrZ3JvdW5kOiB2YXIoLS1jb2xvci1ncmF5LTEwMCk7XHJcbiAgYm9yZGVyOiAxcHggc29saWQgdmFyKC0tY29sb3ItZ3JheS0zMDApO1xyXG4gIGJvcmRlci1yYWRpdXM6IHZhcigtLXJhZGl1cy1tZCk7XHJcbiAgZm9udC1zaXplOiB2YXIoLS1mb250LXNpemUtc20pO1xyXG4gIGNvbG9yOiB2YXIoLS1jb2xvci1ncmF5LTcwMCk7XHJcbiAgY3Vyc29yOiBwb2ludGVyO1xyXG4gIHRyYW5zaXRpb246IGFsbCB2YXIoLS10cmFuc2l0aW9uLWZhc3QpO1xyXG5cclxuICAmOmhvdmVyIHtcclxuICAgIGJhY2tncm91bmQ6IHZhcigtLWNvbG9yLWdyYXktMjAwKTtcclxuICB9XHJcbn1cclxuXHJcbi5uby1yZXN1bHRzIHtcclxuICB0ZXh0LWFsaWduOiBjZW50ZXI7XHJcbiAgcGFkZGluZzogdmFyKC0tc3BhY2UtOCk7XHJcbiAgY29sb3I6IHZhcigtLWNvbG9yLWdyYXktNjAwKTtcclxuXHJcbiAgcCB7XHJcbiAgICBtYXJnaW4tYm90dG9tOiB2YXIoLS1zcGFjZS00KTtcclxuICB9XHJcbn1cclxuXHJcbi8qIFJlc3BvbnNpdmUgKi9cclxuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XHJcbiAgLmZpbHRlci1jb250cm9scyB7XHJcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xyXG4gICAgYWxpZ24taXRlbXM6IHN0cmV0Y2g7XHJcbiAgfVxyXG5cclxuICAuZmlsdGVyLXNlbGVjdCB7XHJcbiAgICBtaW4td2lkdGg6IGF1dG87XHJcbiAgfVxyXG5cclxuICAvKiBNb2JpbGUgYWRqdXN0bWVudHMgLSBncmlkIHJlbWFpbnMgZmxleGlibGUgKi9cclxuXHJcbiAgLm5ld3Mtd3JhcHBlci5jb21wYWN0IC5uZXdzLWdyaWQge1xyXG4gICAgbWF4LWhlaWdodDogMzAwcHg7XHJcbiAgfVxyXG59Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "item_r3", "link", "ɵɵsanitizeUrl", "ɵɵadvance", "ɵɵtextInterpolate", "ɵɵpipeBind1", "country", "source", "title", "description", "ɵɵpipeBind2", "published", "ɵɵlistener", "NewsComponent_div_0_div_3_Template_button_click_3_listener", "ɵɵrestoreView", "_r5", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "clearFilters", "ɵɵtemplate", "NewsComponent_div_0_a_2_Template", "NewsComponent_div_0_div_3_Template", "ɵɵpureFunction1", "_c0", "ctx_r0", "compact", "filteredNews", "trackByTitle", "length", "searchTerm", "selectedCountry", "selectedSource", "NewsComponent", "constructor", "authService", "showTitle", "news", "loading", "error", "availableCountries", "availableSources", "userCountry", "ngOnInit", "detectUserCountry", "fetch", "then", "response", "json", "data", "country_code", "toLowerCase", "console", "log", "loadNews", "catch", "ensureAuthenticated", "subscribe", "next", "fetchNews", "initializeFilters", "applyFilters", "Set", "map", "item", "sort", "filtered", "trim", "searchLower", "filter", "includes", "onSearchChange", "event", "target", "value", "onCountryChange", "onSourceChange", "index", "refreshNews", "_", "ɵɵdirectiveInject", "i1", "AuthService", "_2", "selectors", "inputs", "decls", "vars", "consts", "template", "NewsComponent_Template", "rf", "ctx", "NewsComponent_div_0_Template"], "sources": ["C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\news_separate\\frontend\\src\\app\\news\\news.component.ts", "C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\news_separate\\frontend\\src\\app\\news\\news.component.html"], "sourcesContent": ["import { Component, Input, OnInit, OnChanges, SimpleChanges } from '@angular/core';\nimport { AuthService } from '../auth.service';\n\nexport interface NewsItem {\n  country: string;\n  source: string;\n  title: string;\n  link: string;\n  published: string; // ISO string\n  description: string;\n  fetched_at?: string;\n  // allow extra fields (e.g., id, created_at) from backend without strict typing\n  [key: string]: any;\n}\n\n@Component({\n  selector: 'app-news',\n  templateUrl: './news.component.html',\n  styleUrls: ['./news.component.scss']\n})\nexport class NewsComponent implements OnInit {\n  @Input() showTitle: boolean = true;\n  @Input() compact: boolean = false;\n\n  // News data\n  news: NewsItem[] = [];\n  loading: boolean = false;\n  error: string = '';\n\n  // Filter properties\n  filteredNews: NewsItem[] = [];\n  searchTerm: string = '';\n  selectedCountry: string = '';\n  selectedSource: string = '';\n\n  // Available filter options\n  availableCountries: string[] = [];\n  availableSources: string[] = [];\n\n  userCountry: string = '';\n\n  constructor(private authService: AuthService,\n  ) {}\n\n  ngOnInit() {\n    this.detectUserCountry();\n  }\n  \n  detectUserCountry() {\n    fetch('https://ipwhois.app/json/')\n      .then(response => response.json())\n      .then(data => {\n        this.userCountry = data.country_code.toLowerCase();\n        console.log('User country detected:', data.country, data.country_code);\n        this.userCountry = 'poland';\n        this.loadNews(this.userCountry);\n      })\n      .catch(error => {\n        console.error('IP detection failed:', error);\n      });\n  }\n  \n  loadNews(country: string = 'poland') {\n    this.loading = true;\n    this.error = '';\n    \n    this.authService.ensureAuthenticated().subscribe({\n      next: () => {\n        this.authService.fetchNews(country).subscribe({\n          next: (response) => {\n            this.news = response.news || [];\n            this.initializeFilters();\n            this.applyFilters();\n            this.loading = false;\n          },\n          error: (error) => {\n            this.error = 'Failed to load news';\n            this.loading = false;\n            console.error('Error loading news:', error);\n          }\n        });\n      },\n      error: (error) => {\n        this.error = 'Authentication failed';\n        this.loading = false;\n        console.error('Auth error:', error);\n      }\n    });\n  }\n\n  initializeFilters() {\n    this.availableCountries = [...new Set(this.news.map(item => item.country))].sort();\n    this.availableSources = [...new Set(this.news.map(item => item.source))].sort();\n  }\n\n  applyFilters() {\n    let filtered = [...this.news];\n\n    // Apply search filter\n    if (this.searchTerm.trim()) {\n      const searchLower = this.searchTerm.toLowerCase();\n      filtered = filtered.filter(item =>\n        item.title.toLowerCase().includes(searchLower) ||\n        item.description.toLowerCase().includes(searchLower) ||\n        item.source.toLowerCase().includes(searchLower)\n      );\n    }\n\n    // Apply country filter\n    if (this.selectedCountry) {\n      filtered = filtered.filter(item => item.country === this.selectedCountry);\n    }\n\n    // Apply source filter\n    if (this.selectedSource) {\n      filtered = filtered.filter(item => item.source === this.selectedSource);\n    }\n\n    this.filteredNews = filtered;\n  }\n\n  onSearchChange(event: any) {\n    this.searchTerm = event.target.value;\n    this.applyFilters();\n  }\n\n  onCountryChange(event: any) {\n    this.selectedCountry = event.target.value;\n    this.applyFilters();\n  }\n\n  onSourceChange(event: any) {\n    this.selectedSource = event.target.value;\n    this.applyFilters();\n  }\n\n  clearFilters() {\n    this.searchTerm = '';\n    this.selectedCountry = '';\n    this.selectedSource = '';\n    this.applyFilters();\n  }\n\n  trackByTitle(index: number, item: NewsItem) {\n    return item.title;\n  }\n\n  refreshNews() {\n    this.loadNews();\n  }\n}", "<div class=\"news-wrapper\" [ngClass]=\"{ 'compact': compact }\" *ngIf=\"news && news.length\">\n\n  <!-- Search and Filter Controls - Only show when not in compact mode -->\n  <!-- <div class=\"news-controls\" *ngIf=\"!compact\">\n    \n    <div class=\"search-container\">\n      <input\n        type=\"text\"\n        class=\"search-input\"\n        placeholder=\"Search news...\"\n        [value]=\"searchTerm\"\n        (input)=\"onSearchChange($event)\"\n      />\n    </div>\n\n    \n    <div class=\"filter-controls\">\n      <select\n        class=\"filter-select\"\n        [value]=\"selectedCountry\"\n        (change)=\"onCountryChange($event)\"\n      >\n        <option value=\"\">All Countries</option>\n        <option *ngFor=\"let country of availableCountries\" [value]=\"country\">\n          {{ country | titlecase }}\n        </option>\n      </select>\n\n      <select\n        class=\"filter-select\"\n        [value]=\"selectedSource\"\n        (change)=\"onSourceChange($event)\"\n      >\n        <option value=\"\">All Sources</option>\n        <option *ngFor=\"let source of availableSources\" [value]=\"source\">\n          {{ source }}\n        </option>\n      </select>\n\n      <button\n        class=\"clear-filters-btn\"\n        (click)=\"clearFilters()\"\n        type=\"button\"\n        *ngIf=\"searchTerm || selectedCountry || selectedSource\"\n      >\n        Clear Filters\n      </button>\n    </div>\n  </div> -->\n\n  <div class=\"news-grid\">\n    <a\n      class=\"news-card\"\n      *ngFor=\"let item of filteredNews; trackBy: trackByTitle\"\n      [href]=\"item.link\"\n      target=\"_blank\"\n      rel=\"noopener noreferrer\"\n    >\n      <div class=\"news-card-header\">\n        <span class=\"badge\">{{ item.country | titlecase }}</span>\n        <span class=\"source\">{{ item.source }}</span>\n      </div>\n\n      <h3 class=\"title\">{{ item.title }}</h3>\n      <p class=\"description\">{{ item.description }}</p>\n\n      <div class=\"news-card-footer\">\n        <span class=\"published\">{{ item.published | date:'medium' }}</span>\n        <span class=\"cta\">Read →</span>\n      </div>\n    </a>\n  </div>\n\n  <!-- No Results Message -->\n  <div class=\"no-results\" *ngIf=\"filteredNews.length === 0 && (searchTerm || selectedCountry || selectedSource)\">\n    <p>No articles found. Try adjusting your search or filters.</p>\n    <button class=\"clear-filters-btn\" (click)=\"clearFilters()\" type=\"button\">\n      Clear All Filters\n    </button>\n  </div>\n</div>\n"], "mappings": ";;;;;ICmDIA,EAAA,CAAAC,cAAA,WAMC;IAEuBD,EAAA,CAAAE,MAAA,GAA8B;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzDH,EAAA,CAAAC,cAAA,cAAqB;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAG/CH,EAAA,CAAAC,cAAA,YAAkB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvCH,EAAA,CAAAC,cAAA,YAAuB;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEjDH,EAAA,CAAAC,cAAA,eAA8B;IACJD,EAAA,CAAAE,MAAA,IAAoC;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAC,cAAA,gBAAkB;IAAAD,EAAA,CAAAE,MAAA,mBAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAdjCH,EAAA,CAAAI,UAAA,SAAAC,OAAA,CAAAC,IAAA,EAAAN,EAAA,CAAAO,aAAA,CAAkB;IAKIP,EAAA,CAAAQ,SAAA,GAA8B;IAA9BR,EAAA,CAAAS,iBAAA,CAAAT,EAAA,CAAAU,WAAA,OAAAL,OAAA,CAAAM,OAAA,EAA8B;IAC7BX,EAAA,CAAAQ,SAAA,GAAiB;IAAjBR,EAAA,CAAAS,iBAAA,CAAAJ,OAAA,CAAAO,MAAA,CAAiB;IAGtBZ,EAAA,CAAAQ,SAAA,GAAgB;IAAhBR,EAAA,CAAAS,iBAAA,CAAAJ,OAAA,CAAAQ,KAAA,CAAgB;IACXb,EAAA,CAAAQ,SAAA,GAAsB;IAAtBR,EAAA,CAAAS,iBAAA,CAAAJ,OAAA,CAAAS,WAAA,CAAsB;IAGnBd,EAAA,CAAAQ,SAAA,GAAoC;IAApCR,EAAA,CAAAS,iBAAA,CAAAT,EAAA,CAAAe,WAAA,QAAAV,OAAA,CAAAW,SAAA,YAAoC;;;;;;IAOlEhB,EAAA,CAAAC,cAAA,cAA+G;IAC1GD,EAAA,CAAAE,MAAA,+DAAwD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC/DH,EAAA,CAAAC,cAAA,iBAAyE;IAAvCD,EAAA,CAAAiB,UAAA,mBAAAC,2DAAA;MAAAlB,EAAA,CAAAmB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAStB,EAAA,CAAAuB,WAAA,CAAAF,MAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IACxDxB,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;;;IA9EbH,EAAA,CAAAC,cAAA,aAAyF;IAmDrFD,EAAA,CAAAyB,UAAA,IAAAC,gCAAA,iBAmBI;IACN1B,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAyB,UAAA,IAAAE,kCAAA,iBAKM;IACR3B,EAAA,CAAAG,YAAA,EAAM;;;;IAhFoBH,EAAA,CAAAI,UAAA,YAAAJ,EAAA,CAAA4B,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,OAAA,EAAkC;IAqDrC/B,EAAA,CAAAQ,SAAA,GAAiB;IAAjBR,EAAA,CAAAI,UAAA,YAAA0B,MAAA,CAAAE,YAAA,CAAiB,iBAAAF,MAAA,CAAAG,YAAA;IAqBbjC,EAAA,CAAAQ,SAAA,GAAoF;IAApFR,EAAA,CAAAI,UAAA,SAAA0B,MAAA,CAAAE,YAAA,CAAAE,MAAA,WAAAJ,MAAA,CAAAK,UAAA,IAAAL,MAAA,CAAAM,eAAA,IAAAN,MAAA,CAAAO,cAAA,EAAoF;;;ADtD/G,OAAM,MAAOC,aAAa;EAqBxBC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IApBtB,KAAAC,SAAS,GAAY,IAAI;IACzB,KAAAV,OAAO,GAAY,KAAK;IAEjC;IACA,KAAAW,IAAI,GAAe,EAAE;IACrB,KAAAC,OAAO,GAAY,KAAK;IACxB,KAAAC,KAAK,GAAW,EAAE;IAElB;IACA,KAAAZ,YAAY,GAAe,EAAE;IAC7B,KAAAG,UAAU,GAAW,EAAE;IACvB,KAAAC,eAAe,GAAW,EAAE;IAC5B,KAAAC,cAAc,GAAW,EAAE;IAE3B;IACA,KAAAQ,kBAAkB,GAAa,EAAE;IACjC,KAAAC,gBAAgB,GAAa,EAAE;IAE/B,KAAAC,WAAW,GAAW,EAAE;EAGrB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,iBAAiB,EAAE;EAC1B;EAEAA,iBAAiBA,CAAA;IACfC,KAAK,CAAC,2BAA2B,CAAC,CAC/BC,IAAI,CAACC,QAAQ,IAAIA,QAAQ,CAACC,IAAI,EAAE,CAAC,CACjCF,IAAI,CAACG,IAAI,IAAG;MACX,IAAI,CAACP,WAAW,GAAGO,IAAI,CAACC,YAAY,CAACC,WAAW,EAAE;MAClDC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEJ,IAAI,CAAC3C,OAAO,EAAE2C,IAAI,CAACC,YAAY,CAAC;MACtE,IAAI,CAACR,WAAW,GAAG,QAAQ;MAC3B,IAAI,CAACY,QAAQ,CAAC,IAAI,CAACZ,WAAW,CAAC;IACjC,CAAC,CAAC,CACDa,KAAK,CAAChB,KAAK,IAAG;MACba,OAAO,CAACb,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C,CAAC,CAAC;EACN;EAEAe,QAAQA,CAAChD,OAAA,GAAkB,QAAQ;IACjC,IAAI,CAACgC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,KAAK,GAAG,EAAE;IAEf,IAAI,CAACJ,WAAW,CAACqB,mBAAmB,EAAE,CAACC,SAAS,CAAC;MAC/CC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACvB,WAAW,CAACwB,SAAS,CAACrD,OAAO,CAAC,CAACmD,SAAS,CAAC;UAC5CC,IAAI,EAAGX,QAAQ,IAAI;YACjB,IAAI,CAACV,IAAI,GAAGU,QAAQ,CAACV,IAAI,IAAI,EAAE;YAC/B,IAAI,CAACuB,iBAAiB,EAAE;YACxB,IAAI,CAACC,YAAY,EAAE;YACnB,IAAI,CAACvB,OAAO,GAAG,KAAK;UACtB,CAAC;UACDC,KAAK,EAAGA,KAAK,IAAI;YACf,IAAI,CAACA,KAAK,GAAG,qBAAqB;YAClC,IAAI,CAACD,OAAO,GAAG,KAAK;YACpBc,OAAO,CAACb,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;UAC7C;SACD,CAAC;MACJ,CAAC;MACDA,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACA,KAAK,GAAG,uBAAuB;QACpC,IAAI,CAACD,OAAO,GAAG,KAAK;QACpBc,OAAO,CAACb,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACrC;KACD,CAAC;EACJ;EAEAqB,iBAAiBA,CAAA;IACf,IAAI,CAACpB,kBAAkB,GAAG,CAAC,GAAG,IAAIsB,GAAG,CAAC,IAAI,CAACzB,IAAI,CAAC0B,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAC1D,OAAO,CAAC,CAAC,CAAC,CAAC2D,IAAI,EAAE;IAClF,IAAI,CAACxB,gBAAgB,GAAG,CAAC,GAAG,IAAIqB,GAAG,CAAC,IAAI,CAACzB,IAAI,CAAC0B,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACzD,MAAM,CAAC,CAAC,CAAC,CAAC0D,IAAI,EAAE;EACjF;EAEAJ,YAAYA,CAAA;IACV,IAAIK,QAAQ,GAAG,CAAC,GAAG,IAAI,CAAC7B,IAAI,CAAC;IAE7B;IACA,IAAI,IAAI,CAACP,UAAU,CAACqC,IAAI,EAAE,EAAE;MAC1B,MAAMC,WAAW,GAAG,IAAI,CAACtC,UAAU,CAACqB,WAAW,EAAE;MACjDe,QAAQ,GAAGA,QAAQ,CAACG,MAAM,CAACL,IAAI,IAC7BA,IAAI,CAACxD,KAAK,CAAC2C,WAAW,EAAE,CAACmB,QAAQ,CAACF,WAAW,CAAC,IAC9CJ,IAAI,CAACvD,WAAW,CAAC0C,WAAW,EAAE,CAACmB,QAAQ,CAACF,WAAW,CAAC,IACpDJ,IAAI,CAACzD,MAAM,CAAC4C,WAAW,EAAE,CAACmB,QAAQ,CAACF,WAAW,CAAC,CAChD;;IAGH;IACA,IAAI,IAAI,CAACrC,eAAe,EAAE;MACxBmC,QAAQ,GAAGA,QAAQ,CAACG,MAAM,CAACL,IAAI,IAAIA,IAAI,CAAC1D,OAAO,KAAK,IAAI,CAACyB,eAAe,CAAC;;IAG3E;IACA,IAAI,IAAI,CAACC,cAAc,EAAE;MACvBkC,QAAQ,GAAGA,QAAQ,CAACG,MAAM,CAACL,IAAI,IAAIA,IAAI,CAACzD,MAAM,KAAK,IAAI,CAACyB,cAAc,CAAC;;IAGzE,IAAI,CAACL,YAAY,GAAGuC,QAAQ;EAC9B;EAEAK,cAAcA,CAACC,KAAU;IACvB,IAAI,CAAC1C,UAAU,GAAG0C,KAAK,CAACC,MAAM,CAACC,KAAK;IACpC,IAAI,CAACb,YAAY,EAAE;EACrB;EAEAc,eAAeA,CAACH,KAAU;IACxB,IAAI,CAACzC,eAAe,GAAGyC,KAAK,CAACC,MAAM,CAACC,KAAK;IACzC,IAAI,CAACb,YAAY,EAAE;EACrB;EAEAe,cAAcA,CAACJ,KAAU;IACvB,IAAI,CAACxC,cAAc,GAAGwC,KAAK,CAACC,MAAM,CAACC,KAAK;IACxC,IAAI,CAACb,YAAY,EAAE;EACrB;EAEA1C,YAAYA,CAAA;IACV,IAAI,CAACW,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,eAAe,GAAG,EAAE;IACzB,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC6B,YAAY,EAAE;EACrB;EAEAjC,YAAYA,CAACiD,KAAa,EAAEb,IAAc;IACxC,OAAOA,IAAI,CAACxD,KAAK;EACnB;EAEAsE,WAAWA,CAAA;IACT,IAAI,CAACxB,QAAQ,EAAE;EACjB;EAAC,QAAAyB,CAAA,G;qBAjIU9C,aAAa,EAAAtC,EAAA,CAAAqF,iBAAA,CAAAC,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAblD,aAAa;IAAAmD,SAAA;IAAAC,MAAA;MAAAjD,SAAA;MAAAV,OAAA;IAAA;IAAA4D,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCpB1BhG,EAAA,CAAAyB,UAAA,IAAAyE,4BAAA,iBAgFM;;;QAhFwDlG,EAAA,CAAAI,UAAA,SAAA6F,GAAA,CAAAvD,IAAA,IAAAuD,GAAA,CAAAvD,IAAA,CAAAR,MAAA,CAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}