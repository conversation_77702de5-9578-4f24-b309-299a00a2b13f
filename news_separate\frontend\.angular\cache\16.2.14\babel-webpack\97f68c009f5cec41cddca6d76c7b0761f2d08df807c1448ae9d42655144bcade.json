{"ast": null, "code": "import { innerFrom } from '../observable/innerFrom';\nimport { Subject } from '../Subject';\nimport { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nexport function repeatWhen(notifier) {\n  return operate((source, subscriber) => {\n    let innerSub;\n    let syncResub = false;\n    let completions$;\n    let isNotifierComplete = false;\n    let isMainComplete = false;\n    const checkComplete = () => isMainComplete && isNotifierComplete && (subscriber.complete(), true);\n    const getCompletionSubject = () => {\n      if (!completions$) {\n        completions$ = new Subject();\n        innerFrom(notifier(completions$)).subscribe(createOperatorSubscriber(subscriber, () => {\n          if (innerSub) {\n            subscribeForRepeatWhen();\n          } else {\n            syncResub = true;\n          }\n        }, () => {\n          isNotifierComplete = true;\n          checkComplete();\n        }));\n      }\n      return completions$;\n    };\n    const subscribeForRepeatWhen = () => {\n      isMainComplete = false;\n      innerSub = source.subscribe(createOperatorSubscriber(subscriber, undefined, () => {\n        isMainComplete = true;\n        !checkComplete() && getCompletionSubject().next();\n      }));\n      if (syncResub) {\n        innerSub.unsubscribe();\n        innerSub = null;\n        syncResub = false;\n        subscribeForRepeatWhen();\n      }\n    };\n    subscribeForRepeatWhen();\n  });\n}\n//# sourceMappingURL=repeatWhen.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}