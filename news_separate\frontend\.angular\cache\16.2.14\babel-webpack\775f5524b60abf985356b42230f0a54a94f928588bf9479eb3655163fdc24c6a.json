{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { Observable, of } from 'rxjs';\nimport { catchError, map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let AuthService = /*#__PURE__*/(() => {\n  class AuthService {\n    constructor(http) {\n      this.http = http;\n      // private baseUrl = 'http://localhost:8000/api';\n      this.baseUrl = 'https://news-agent-backend.duckdns.org/api';\n      this.tokenKey = 'token';\n    }\n    // Get token from localStorage\n    getToken() {\n      return localStorage.getItem(this.tokenKey);\n    }\n    // Set token in localStorage\n    setToken(token) {\n      localStorage.setItem(this.tokenKey, token);\n    }\n    // Check if token exists and is valid\n    checkAuthStatus() {\n      const token = this.getToken();\n      if (!token) {\n        return of(false);\n      }\n      const headers = new HttpHeaders({\n        'Authorization': `Token ${token}`\n      });\n      return this.http.get(`${this.baseUrl}/check-auth/`, {\n        headers\n      }).pipe(map(() => true), catchError(() => of(false)));\n    }\n    // Create anonymous user and get token\n    createAnonymousUser() {\n      return this.http.post(`${this.baseUrl}/anonymous-user/`, {}).pipe(map(response => {\n        this.setToken(response.token);\n        return response.token;\n      }));\n    }\n    // Main function to ensure user is authenticated\n    ensureAuthenticated() {\n      return new Observable(observer => {\n        this.checkAuthStatus().subscribe(isValid => {\n          if (isValid) {\n            observer.next(this.getToken());\n            observer.complete();\n          } else {\n            this.createAnonymousUser().subscribe(token => {\n              observer.next(token);\n              observer.complete();\n            }, error => {\n              observer.error(error);\n            });\n          }\n        });\n      });\n    }\n    // Get headers with authentication\n    getHeaders() {\n      const token = this.getToken();\n      return new HttpHeaders({\n        'Authorization': `Token ${token}`\n      });\n    }\n    // Load chat history with pagination\n    loadChatHistory(page = 1, sessionId) {\n      let url = `${this.baseUrl}/chat-history/?page=${page}`;\n      if (sessionId) {\n        url += `&session_id=${sessionId}`;\n      }\n      return this.http.get(url, {\n        headers: this.getHeaders()\n      });\n    }\n    // Send message to chatbot\n    sendMessageToChatbot(message, sessionId) {\n      const payload = {\n        message\n      };\n      if (sessionId) {\n        payload.session_id = sessionId;\n      }\n      return this.http.post(`${this.baseUrl}/chatbot/`, payload, {\n        headers: this.getHeaders()\n      });\n    }\n    // Add this method to your AuthService class\n    fetchNews(country = 'poland') {\n      return this.http.post(`${this.baseUrl}/news/`, {\n        country\n      }, {\n        headers: this.getHeaders()\n      });\n    }\n    static #_ = this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient));\n    };\n    static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return AuthService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}