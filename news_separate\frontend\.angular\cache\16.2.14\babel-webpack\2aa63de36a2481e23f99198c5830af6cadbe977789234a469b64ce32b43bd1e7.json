{"ast": null, "code": "import { ReplaySubject } from '../ReplaySubject';\nimport { multicast } from './multicast';\nimport { isFunction } from '../util/isFunction';\nexport function publishReplay(bufferSize, windowTime, selectorOrScheduler, timestampProvider) {\n  if (selectorOrScheduler && !isFunction(selectorOrScheduler)) {\n    timestampProvider = selectorOrScheduler;\n  }\n  const selector = isFunction(selectorOrScheduler) ? selectorOrScheduler : undefined;\n  return source => multicast(new ReplaySubject(bufferSize, windowTime, timestampProvider), selector)(source);\n}\n//# sourceMappingURL=publishReplay.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}