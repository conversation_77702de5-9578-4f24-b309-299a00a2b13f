{"ast": null, "code": "/**\n * marked v16.1.1 - a markdown parser\n * Copyright (c) 2011-2025, <PERSON>. (MIT Licensed)\n * https://github.com/markedjs/marked\n */\n\n/**\n * DO NOT EDIT THIS FILE\n * The code in this file is generated from files in ./src/\n */\n\nfunction L() {\n  return {\n    async: !1,\n    breaks: !1,\n    extensions: null,\n    gfm: !0,\n    hooks: null,\n    pedantic: !1,\n    renderer: null,\n    silent: !1,\n    tokenizer: null,\n    walkTokens: null\n  };\n}\nvar O = L();\nfunction H(l) {\n  O = l;\n}\nvar E = {\n  exec: () => null\n};\nfunction h(l, e = \"\") {\n  let t = typeof l == \"string\" ? l : l.source,\n    n = {\n      replace: (r, i) => {\n        let s = typeof i == \"string\" ? i : i.source;\n        return s = s.replace(m.caret, \"$1\"), t = t.replace(r, s), n;\n      },\n      getRegex: () => new RegExp(t, e)\n    };\n  return n;\n}\nvar m = {\n    codeRemoveIndent: /^(?: {1,4}| {0,3}\\t)/gm,\n    outputLinkReplace: /\\\\([\\[\\]])/g,\n    indentCodeCompensation: /^(\\s+)(?:```)/,\n    beginningSpace: /^\\s+/,\n    endingHash: /#$/,\n    startingSpaceChar: /^ /,\n    endingSpaceChar: / $/,\n    nonSpaceChar: /[^ ]/,\n    newLineCharGlobal: /\\n/g,\n    tabCharGlobal: /\\t/g,\n    multipleSpaceGlobal: /\\s+/g,\n    blankLine: /^[ \\t]*$/,\n    doubleBlankLine: /\\n[ \\t]*\\n[ \\t]*$/,\n    blockquoteStart: /^ {0,3}>/,\n    blockquoteSetextReplace: /\\n {0,3}((?:=+|-+) *)(?=\\n|$)/g,\n    blockquoteSetextReplace2: /^ {0,3}>[ \\t]?/gm,\n    listReplaceTabs: /^\\t+/,\n    listReplaceNesting: /^ {1,4}(?=( {4})*[^ ])/g,\n    listIsTask: /^\\[[ xX]\\] /,\n    listReplaceTask: /^\\[[ xX]\\] +/,\n    anyLine: /\\n.*\\n/,\n    hrefBrackets: /^<(.*)>$/,\n    tableDelimiter: /[:|]/,\n    tableAlignChars: /^\\||\\| *$/g,\n    tableRowBlankLine: /\\n[ \\t]*$/,\n    tableAlignRight: /^ *-+: *$/,\n    tableAlignCenter: /^ *:-+: *$/,\n    tableAlignLeft: /^ *:-+ *$/,\n    startATag: /^<a /i,\n    endATag: /^<\\/a>/i,\n    startPreScriptTag: /^<(pre|code|kbd|script)(\\s|>)/i,\n    endPreScriptTag: /^<\\/(pre|code|kbd|script)(\\s|>)/i,\n    startAngleBracket: /^</,\n    endAngleBracket: />$/,\n    pedanticHrefTitle: /^([^'\"]*[^\\s])\\s+(['\"])(.*)\\2/,\n    unicodeAlphaNumeric: /[\\p{L}\\p{N}]/u,\n    escapeTest: /[&<>\"']/,\n    escapeReplace: /[&<>\"']/g,\n    escapeTestNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/,\n    escapeReplaceNoEncode: /[<>\"']|&(?!(#\\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\\w+);)/g,\n    unescapeTest: /&(#(?:\\d+)|(?:#x[0-9A-Fa-f]+)|(?:\\w+));?/ig,\n    caret: /(^|[^\\[])\\^/g,\n    percentDecode: /%25/g,\n    findPipe: /\\|/g,\n    splitPipe: / \\|/,\n    slashPipe: /\\\\\\|/g,\n    carriageReturn: /\\r\\n|\\r/g,\n    spaceLine: /^ +$/gm,\n    notSpaceStart: /^\\S*/,\n    endingNewline: /\\n$/,\n    listItemRegex: l => new RegExp(`^( {0,3}${l})((?:[\t ][^\\\\n]*)?(?:\\\\n|$))`),\n    nextBulletRegex: l => new RegExp(`^ {0,${Math.min(3, l - 1)}}(?:[*+-]|\\\\d{1,9}[.)])((?:[ \t][^\\\\n]*)?(?:\\\\n|$))`),\n    hrRegex: l => new RegExp(`^ {0,${Math.min(3, l - 1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\\\* *){3,})(?:\\\\n+|$)`),\n    fencesBeginRegex: l => new RegExp(`^ {0,${Math.min(3, l - 1)}}(?:\\`\\`\\`|~~~)`),\n    headingBeginRegex: l => new RegExp(`^ {0,${Math.min(3, l - 1)}}#`),\n    htmlBeginRegex: l => new RegExp(`^ {0,${Math.min(3, l - 1)}}<(?:[a-z].*>|!--)`, \"i\")\n  },\n  xe = /^(?:[ \\t]*(?:\\n|$))+/,\n  be = /^((?: {4}| {0,3}\\t)[^\\n]+(?:\\n(?:[ \\t]*(?:\\n|$))*)?)+/,\n  Re = /^ {0,3}(`{3,}(?=[^`\\n]*(?:\\n|$))|~{3,})([^\\n]*)(?:\\n|$)(?:|([\\s\\S]*?)(?:\\n|$))(?: {0,3}\\1[~`]* *(?=\\n|$)|$)/,\n  C = /^ {0,3}((?:-[\\t ]*){3,}|(?:_[ \\t]*){3,}|(?:\\*[ \\t]*){3,})(?:\\n+|$)/,\n  Oe = /^ {0,3}(#{1,6})(?=\\s|$)(.*)(?:\\n+|$)/,\n  j = /(?:[*+-]|\\d{1,9}[.)])/,\n  se = /^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\\n(?!\\s*?\\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/,\n  ie = h(se).replace(/bull/g, j).replace(/blockCode/g, /(?: {4}| {0,3}\\t)/).replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g, / {0,3}>/).replace(/heading/g, / {0,3}#{1,6}/).replace(/html/g, / {0,3}<[^\\n>]+>\\n/).replace(/\\|table/g, \"\").getRegex(),\n  Te = h(se).replace(/bull/g, j).replace(/blockCode/g, /(?: {4}| {0,3}\\t)/).replace(/fences/g, / {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g, / {0,3}>/).replace(/heading/g, / {0,3}#{1,6}/).replace(/html/g, / {0,3}<[^\\n>]+>\\n/).replace(/table/g, / {0,3}\\|?(?:[:\\- ]*\\|)+[\\:\\- ]*\\n/).getRegex(),\n  F = /^([^\\n]+(?:\\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\\n)[^\\n]+)*)/,\n  we = /^[^\\n]+/,\n  Q = /(?!\\s*\\])(?:\\\\.|[^\\[\\]\\\\])+/,\n  ye = h(/^ {0,3}\\[(label)\\]: *(?:\\n[ \\t]*)?([^<\\s][^\\s]*|<.*?>)(?:(?: +(?:\\n[ \\t]*)?| *\\n[ \\t]*)(title))? *(?:\\n+|$)/).replace(\"label\", Q).replace(\"title\", /(?:\"(?:\\\\\"?|[^\"\\\\])*\"|'[^'\\n]*(?:\\n[^'\\n]+)*\\n?'|\\([^()]*\\))/).getRegex(),\n  Pe = h(/^( {0,3}bull)([ \\t][^\\n]+?)?(?:\\n|$)/).replace(/bull/g, j).getRegex(),\n  v = \"address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul\",\n  U = /<!--(?:-?>|[\\s\\S]*?(?:-->|$))/,\n  Se = h(\"^ {0,3}(?:<(script|pre|style|textarea)[\\\\s>][\\\\s\\\\S]*?(?:</\\\\1>[^\\\\n]*\\\\n+|$)|comment[^\\\\n]*(\\\\n+|$)|<\\\\?[\\\\s\\\\S]*?(?:\\\\?>\\\\n*|$)|<![A-Z][\\\\s\\\\S]*?(?:>\\\\n*|$)|<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?(?:\\\\]\\\\]>\\\\n*|$)|</?(tag)(?: +|\\\\n|/?>)[\\\\s\\\\S]*?(?:(?:\\\\n[ \t]*)+\\\\n|$)|<(?!script|pre|style|textarea)([a-z][\\\\w-]*)(?:attribute)*? */?>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \t]*)+\\\\n|$)|</(?!script|pre|style|textarea)[a-z][\\\\w-]*\\\\s*>(?=[ \\\\t]*(?:\\\\n|$))[\\\\s\\\\S]*?(?:(?:\\\\n[ \t]*)+\\\\n|$))\", \"i\").replace(\"comment\", U).replace(\"tag\", v).replace(\"attribute\", / +[a-zA-Z:_][\\w.:-]*(?: *= *\"[^\"\\n]*\"| *= *'[^'\\n]*'| *= *[^\\s\"'=<>`]+)?/).getRegex(),\n  oe = h(F).replace(\"hr\", C).replace(\"heading\", \" {0,3}#{1,6}(?:\\\\s|$)\").replace(\"|lheading\", \"\").replace(\"|table\", \"\").replace(\"blockquote\", \" {0,3}>\").replace(\"fences\", \" {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n\").replace(\"list\", \" {0,3}(?:[*+-]|1[.)]) \").replace(\"html\", \"</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)\").replace(\"tag\", v).getRegex(),\n  $e = h(/^( {0,3}> ?(paragraph|[^\\n]*)(?:\\n|$))+/).replace(\"paragraph\", oe).getRegex(),\n  K = {\n    blockquote: $e,\n    code: be,\n    def: ye,\n    fences: Re,\n    heading: Oe,\n    hr: C,\n    html: Se,\n    lheading: ie,\n    list: Pe,\n    newline: xe,\n    paragraph: oe,\n    table: E,\n    text: we\n  },\n  re = h(\"^ *([^\\\\n ].*)\\\\n {0,3}((?:\\\\| *)?:?-+:? *(?:\\\\| *:?-+:? *)*(?:\\\\| *)?)(?:\\\\n((?:(?! *\\\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\\\n|$))*)\\\\n*|$)\").replace(\"hr\", C).replace(\"heading\", \" {0,3}#{1,6}(?:\\\\s|$)\").replace(\"blockquote\", \" {0,3}>\").replace(\"code\", \"(?: {4}| {0,3}\t)[^\\\\n]\").replace(\"fences\", \" {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n\").replace(\"list\", \" {0,3}(?:[*+-]|1[.)]) \").replace(\"html\", \"</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)\").replace(\"tag\", v).getRegex(),\n  _e = {\n    ...K,\n    lheading: Te,\n    table: re,\n    paragraph: h(F).replace(\"hr\", C).replace(\"heading\", \" {0,3}#{1,6}(?:\\\\s|$)\").replace(\"|lheading\", \"\").replace(\"table\", re).replace(\"blockquote\", \" {0,3}>\").replace(\"fences\", \" {0,3}(?:`{3,}(?=[^`\\\\n]*\\\\n)|~{3,})[^\\\\n]*\\\\n\").replace(\"list\", \" {0,3}(?:[*+-]|1[.)]) \").replace(\"html\", \"</?(?:tag)(?: +|\\\\n|/?>)|<(?:script|pre|style|textarea|!--)\").replace(\"tag\", v).getRegex()\n  },\n  Le = {\n    ...K,\n    html: h(`^ *(?:comment *(?:\\\\n|\\\\s*$)|<(tag)[\\\\s\\\\S]+?</\\\\1> *(?:\\\\n{2,}|\\\\s*$)|<tag(?:\"[^\"]*\"|'[^']*'|\\\\s[^'\"/>\\\\s]*)*?/?> *(?:\\\\n{2,}|\\\\s*$))`).replace(\"comment\", U).replace(/tag/g, \"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\\\b)\\\\w+(?!:|[^\\\\w\\\\s@]*@)\\\\b\").getRegex(),\n    def: /^ *\\[([^\\]]+)\\]: *<?([^\\s>]+)>?(?: +([\"(][^\\n]+[\")]))? *(?:\\n+|$)/,\n    heading: /^(#{1,6})(.*)(?:\\n+|$)/,\n    fences: E,\n    lheading: /^(.+?)\\n {0,3}(=+|-+) *(?:\\n+|$)/,\n    paragraph: h(F).replace(\"hr\", C).replace(\"heading\", ` *#{1,6} *[^\n]`).replace(\"lheading\", ie).replace(\"|table\", \"\").replace(\"blockquote\", \" {0,3}>\").replace(\"|fences\", \"\").replace(\"|list\", \"\").replace(\"|html\", \"\").replace(\"|tag\", \"\").getRegex()\n  },\n  Me = /^\\\\([!\"#$%&'()*+,\\-./:;<=>?@\\[\\]\\\\^_`{|}~])/,\n  ze = /^(`+)([^`]|[^`][\\s\\S]*?[^`])\\1(?!`)/,\n  ae = /^( {2,}|\\\\)\\n(?!\\s*$)/,\n  Ae = /^(`+|[^`])(?:(?= {2,}\\n)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*_]|\\b_|$)|[^ ](?= {2,}\\n)))/,\n  D = /[\\p{P}\\p{S}]/u,\n  X = /[\\s\\p{P}\\p{S}]/u,\n  le = /[^\\s\\p{P}\\p{S}]/u,\n  Ee = h(/^((?![*_])punctSpace)/, \"u\").replace(/punctSpace/g, X).getRegex(),\n  ue = /(?!~)[\\p{P}\\p{S}]/u,\n  Ce = /(?!~)[\\s\\p{P}\\p{S}]/u,\n  Ie = /(?:[^\\s\\p{P}\\p{S}]|~)/u,\n  Be = /\\[[^[\\]]*?\\]\\((?:\\\\.|[^\\\\\\(\\)]|\\((?:\\\\.|[^\\\\\\(\\)])*\\))*\\)|`[^`]*?`|<(?! )[^<>]*?>/g,\n  pe = /^(?:\\*+(?:((?!\\*)punct)|[^\\s*]))|^_+(?:((?!_)punct)|([^\\s_]))/,\n  qe = h(pe, \"u\").replace(/punct/g, D).getRegex(),\n  ve = h(pe, \"u\").replace(/punct/g, ue).getRegex(),\n  ce = \"^[^_*]*?__[^_*]*?\\\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\\\*)punct(\\\\*+)(?=[\\\\s]|$)|notPunctSpace(\\\\*+)(?!\\\\*)(?=punctSpace|$)|(?!\\\\*)punctSpace(\\\\*+)(?=notPunctSpace)|[\\\\s](\\\\*+)(?!\\\\*)(?=punct)|(?!\\\\*)punct(\\\\*+)(?!\\\\*)(?=punct)|notPunctSpace(\\\\*+)(?=notPunctSpace)\",\n  De = h(ce, \"gu\").replace(/notPunctSpace/g, le).replace(/punctSpace/g, X).replace(/punct/g, D).getRegex(),\n  Ze = h(ce, \"gu\").replace(/notPunctSpace/g, Ie).replace(/punctSpace/g, Ce).replace(/punct/g, ue).getRegex(),\n  Ge = h(\"^[^_*]*?\\\\*\\\\*[^_*]*?_[^_*]*?(?=\\\\*\\\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)\", \"gu\").replace(/notPunctSpace/g, le).replace(/punctSpace/g, X).replace(/punct/g, D).getRegex(),\n  He = h(/\\\\(punct)/, \"gu\").replace(/punct/g, D).getRegex(),\n  Ne = h(/^<(scheme:[^\\s\\x00-\\x1f<>]*|email)>/).replace(\"scheme\", /[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace(\"email\", /[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),\n  je = h(U).replace(\"(?:-->|$)\", \"-->\").getRegex(),\n  Fe = h(\"^comment|^</[a-zA-Z][\\\\w:-]*\\\\s*>|^<[a-zA-Z][\\\\w-]*(?:attribute)*?\\\\s*/?>|^<\\\\?[\\\\s\\\\S]*?\\\\?>|^<![a-zA-Z]+\\\\s[\\\\s\\\\S]*?>|^<!\\\\[CDATA\\\\[[\\\\s\\\\S]*?\\\\]\\\\]>\").replace(\"comment\", je).replace(\"attribute\", /\\s+[a-zA-Z:_][\\w.:-]*(?:\\s*=\\s*\"[^\"]*\"|\\s*=\\s*'[^']*'|\\s*=\\s*[^\\s\"'=<>`]+)?/).getRegex(),\n  q = /(?:\\[(?:\\\\.|[^\\[\\]\\\\])*\\]|\\\\.|`[^`]*`|[^\\[\\]\\\\`])*?/,\n  Qe = h(/^!?\\[(label)\\]\\(\\s*(href)(?:(?:[ \\t]*(?:\\n[ \\t]*)?)(title))?\\s*\\)/).replace(\"label\", q).replace(\"href\", /<(?:\\\\.|[^\\n<>\\\\])+>|[^ \\t\\n\\x00-\\x1f]*/).replace(\"title\", /\"(?:\\\\\"?|[^\"\\\\])*\"|'(?:\\\\'?|[^'\\\\])*'|\\((?:\\\\\\)?|[^)\\\\])*\\)/).getRegex(),\n  he = h(/^!?\\[(label)\\]\\[(ref)\\]/).replace(\"label\", q).replace(\"ref\", Q).getRegex(),\n  de = h(/^!?\\[(ref)\\](?:\\[\\])?/).replace(\"ref\", Q).getRegex(),\n  Ue = h(\"reflink|nolink(?!\\\\()\", \"g\").replace(\"reflink\", he).replace(\"nolink\", de).getRegex(),\n  W = {\n    _backpedal: E,\n    anyPunctuation: He,\n    autolink: Ne,\n    blockSkip: Be,\n    br: ae,\n    code: ze,\n    del: E,\n    emStrongLDelim: qe,\n    emStrongRDelimAst: De,\n    emStrongRDelimUnd: Ge,\n    escape: Me,\n    link: Qe,\n    nolink: de,\n    punctuation: Ee,\n    reflink: he,\n    reflinkSearch: Ue,\n    tag: Fe,\n    text: Ae,\n    url: E\n  },\n  Ke = {\n    ...W,\n    link: h(/^!?\\[(label)\\]\\((.*?)\\)/).replace(\"label\", q).getRegex(),\n    reflink: h(/^!?\\[(label)\\]\\s*\\[([^\\]]*)\\]/).replace(\"label\", q).getRegex()\n  },\n  N = {\n    ...W,\n    emStrongRDelimAst: Ze,\n    emStrongLDelim: ve,\n    url: h(/^((?:ftp|https?):\\/\\/|www\\.)(?:[a-zA-Z0-9\\-]+\\.?)+[^\\s<]*|^email/, \"i\").replace(\"email\", /[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),\n    _backpedal: /(?:[^?!.,:;*_'\"~()&]+|\\([^)]*\\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'\"~)]+(?!$))+/,\n    del: /^(~~?)(?=[^\\s~])((?:\\\\.|[^\\\\])*?(?:\\\\.|[^\\s~\\\\]))\\1(?=[^~]|$)/,\n    text: /^([`~]+|[^`~])(?:(?= {2,}\\n)|(?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)|[\\s\\S]*?(?:(?=[\\\\<!\\[`*~_]|\\b_|https?:\\/\\/|ftp:\\/\\/|www\\.|$)|[^ ](?= {2,}\\n)|[^a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-](?=[a-zA-Z0-9.!#$%&'*+\\/=?_`{\\|}~-]+@)))/\n  },\n  Xe = {\n    ...N,\n    br: h(ae).replace(\"{2,}\", \"*\").getRegex(),\n    text: h(N.text).replace(\"\\\\b_\", \"\\\\b_| {2,}\\\\n\").replace(/\\{2,\\}/g, \"*\").getRegex()\n  },\n  I = {\n    normal: K,\n    gfm: _e,\n    pedantic: Le\n  },\n  M = {\n    normal: W,\n    gfm: N,\n    breaks: Xe,\n    pedantic: Ke\n  };\nvar We = {\n    \"&\": \"&amp;\",\n    \"<\": \"&lt;\",\n    \">\": \"&gt;\",\n    '\"': \"&quot;\",\n    \"'\": \"&#39;\"\n  },\n  ke = l => We[l];\nfunction w(l, e) {\n  if (e) {\n    if (m.escapeTest.test(l)) return l.replace(m.escapeReplace, ke);\n  } else if (m.escapeTestNoEncode.test(l)) return l.replace(m.escapeReplaceNoEncode, ke);\n  return l;\n}\nfunction J(l) {\n  try {\n    l = encodeURI(l).replace(m.percentDecode, \"%\");\n  } catch {\n    return null;\n  }\n  return l;\n}\nfunction V(l, e) {\n  let t = l.replace(m.findPipe, (i, s, o) => {\n      let a = !1,\n        u = s;\n      for (; --u >= 0 && o[u] === \"\\\\\";) a = !a;\n      return a ? \"|\" : \" |\";\n    }),\n    n = t.split(m.splitPipe),\n    r = 0;\n  if (n[0].trim() || n.shift(), n.length > 0 && !n.at(-1)?.trim() && n.pop(), e) if (n.length > e) n.splice(e);else for (; n.length < e;) n.push(\"\");\n  for (; r < n.length; r++) n[r] = n[r].trim().replace(m.slashPipe, \"|\");\n  return n;\n}\nfunction z(l, e, t) {\n  let n = l.length;\n  if (n === 0) return \"\";\n  let r = 0;\n  for (; r < n;) {\n    let i = l.charAt(n - r - 1);\n    if (i === e && !t) r++;else if (i !== e && t) r++;else break;\n  }\n  return l.slice(0, n - r);\n}\nfunction ge(l, e) {\n  if (l.indexOf(e[1]) === -1) return -1;\n  let t = 0;\n  for (let n = 0; n < l.length; n++) if (l[n] === \"\\\\\") n++;else if (l[n] === e[0]) t++;else if (l[n] === e[1] && (t--, t < 0)) return n;\n  return t > 0 ? -2 : -1;\n}\nfunction fe(l, e, t, n, r) {\n  let i = e.href,\n    s = e.title || null,\n    o = l[1].replace(r.other.outputLinkReplace, \"$1\");\n  n.state.inLink = !0;\n  let a = {\n    type: l[0].charAt(0) === \"!\" ? \"image\" : \"link\",\n    raw: t,\n    href: i,\n    title: s,\n    text: o,\n    tokens: n.inlineTokens(o)\n  };\n  return n.state.inLink = !1, a;\n}\nfunction Je(l, e, t) {\n  let n = l.match(t.other.indentCodeCompensation);\n  if (n === null) return e;\n  let r = n[1];\n  return e.split(`\n`).map(i => {\n    let s = i.match(t.other.beginningSpace);\n    if (s === null) return i;\n    let [o] = s;\n    return o.length >= r.length ? i.slice(r.length) : i;\n  }).join(`\n`);\n}\nvar y = class {\n  options;\n  rules;\n  lexer;\n  constructor(e) {\n    this.options = e || O;\n  }\n  space(e) {\n    let t = this.rules.block.newline.exec(e);\n    if (t && t[0].length > 0) return {\n      type: \"space\",\n      raw: t[0]\n    };\n  }\n  code(e) {\n    let t = this.rules.block.code.exec(e);\n    if (t) {\n      let n = t[0].replace(this.rules.other.codeRemoveIndent, \"\");\n      return {\n        type: \"code\",\n        raw: t[0],\n        codeBlockStyle: \"indented\",\n        text: this.options.pedantic ? n : z(n, `\n`)\n      };\n    }\n  }\n  fences(e) {\n    let t = this.rules.block.fences.exec(e);\n    if (t) {\n      let n = t[0],\n        r = Je(n, t[3] || \"\", this.rules);\n      return {\n        type: \"code\",\n        raw: n,\n        lang: t[2] ? t[2].trim().replace(this.rules.inline.anyPunctuation, \"$1\") : t[2],\n        text: r\n      };\n    }\n  }\n  heading(e) {\n    let t = this.rules.block.heading.exec(e);\n    if (t) {\n      let n = t[2].trim();\n      if (this.rules.other.endingHash.test(n)) {\n        let r = z(n, \"#\");\n        (this.options.pedantic || !r || this.rules.other.endingSpaceChar.test(r)) && (n = r.trim());\n      }\n      return {\n        type: \"heading\",\n        raw: t[0],\n        depth: t[1].length,\n        text: n,\n        tokens: this.lexer.inline(n)\n      };\n    }\n  }\n  hr(e) {\n    let t = this.rules.block.hr.exec(e);\n    if (t) return {\n      type: \"hr\",\n      raw: z(t[0], `\n`)\n    };\n  }\n  blockquote(e) {\n    let t = this.rules.block.blockquote.exec(e);\n    if (t) {\n      let n = z(t[0], `\n`).split(`\n`),\n        r = \"\",\n        i = \"\",\n        s = [];\n      for (; n.length > 0;) {\n        let o = !1,\n          a = [],\n          u;\n        for (u = 0; u < n.length; u++) if (this.rules.other.blockquoteStart.test(n[u])) a.push(n[u]), o = !0;else if (!o) a.push(n[u]);else break;\n        n = n.slice(u);\n        let p = a.join(`\n`),\n          c = p.replace(this.rules.other.blockquoteSetextReplace, `\n    $1`).replace(this.rules.other.blockquoteSetextReplace2, \"\");\n        r = r ? `${r}\n${p}` : p, i = i ? `${i}\n${c}` : c;\n        let f = this.lexer.state.top;\n        if (this.lexer.state.top = !0, this.lexer.blockTokens(c, s, !0), this.lexer.state.top = f, n.length === 0) break;\n        let k = s.at(-1);\n        if (k?.type === \"code\") break;\n        if (k?.type === \"blockquote\") {\n          let x = k,\n            g = x.raw + `\n` + n.join(`\n`),\n            T = this.blockquote(g);\n          s[s.length - 1] = T, r = r.substring(0, r.length - x.raw.length) + T.raw, i = i.substring(0, i.length - x.text.length) + T.text;\n          break;\n        } else if (k?.type === \"list\") {\n          let x = k,\n            g = x.raw + `\n` + n.join(`\n`),\n            T = this.list(g);\n          s[s.length - 1] = T, r = r.substring(0, r.length - k.raw.length) + T.raw, i = i.substring(0, i.length - x.raw.length) + T.raw, n = g.substring(s.at(-1).raw.length).split(`\n`);\n          continue;\n        }\n      }\n      return {\n        type: \"blockquote\",\n        raw: r,\n        tokens: s,\n        text: i\n      };\n    }\n  }\n  list(e) {\n    let t = this.rules.block.list.exec(e);\n    if (t) {\n      let n = t[1].trim(),\n        r = n.length > 1,\n        i = {\n          type: \"list\",\n          raw: \"\",\n          ordered: r,\n          start: r ? +n.slice(0, -1) : \"\",\n          loose: !1,\n          items: []\n        };\n      n = r ? `\\\\d{1,9}\\\\${n.slice(-1)}` : `\\\\${n}`, this.options.pedantic && (n = r ? n : \"[*+-]\");\n      let s = this.rules.other.listItemRegex(n),\n        o = !1;\n      for (; e;) {\n        let u = !1,\n          p = \"\",\n          c = \"\";\n        if (!(t = s.exec(e)) || this.rules.block.hr.test(e)) break;\n        p = t[0], e = e.substring(p.length);\n        let f = t[2].split(`\n`, 1)[0].replace(this.rules.other.listReplaceTabs, Z => \" \".repeat(3 * Z.length)),\n          k = e.split(`\n`, 1)[0],\n          x = !f.trim(),\n          g = 0;\n        if (this.options.pedantic ? (g = 2, c = f.trimStart()) : x ? g = t[1].length + 1 : (g = t[2].search(this.rules.other.nonSpaceChar), g = g > 4 ? 1 : g, c = f.slice(g), g += t[1].length), x && this.rules.other.blankLine.test(k) && (p += k + `\n`, e = e.substring(k.length + 1), u = !0), !u) {\n          let Z = this.rules.other.nextBulletRegex(g),\n            ee = this.rules.other.hrRegex(g),\n            te = this.rules.other.fencesBeginRegex(g),\n            ne = this.rules.other.headingBeginRegex(g),\n            me = this.rules.other.htmlBeginRegex(g);\n          for (; e;) {\n            let G = e.split(`\n`, 1)[0],\n              A;\n            if (k = G, this.options.pedantic ? (k = k.replace(this.rules.other.listReplaceNesting, \"  \"), A = k) : A = k.replace(this.rules.other.tabCharGlobal, \"    \"), te.test(k) || ne.test(k) || me.test(k) || Z.test(k) || ee.test(k)) break;\n            if (A.search(this.rules.other.nonSpaceChar) >= g || !k.trim()) c += `\n` + A.slice(g);else {\n              if (x || f.replace(this.rules.other.tabCharGlobal, \"    \").search(this.rules.other.nonSpaceChar) >= 4 || te.test(f) || ne.test(f) || ee.test(f)) break;\n              c += `\n` + k;\n            }\n            !x && !k.trim() && (x = !0), p += G + `\n`, e = e.substring(G.length + 1), f = A.slice(g);\n          }\n        }\n        i.loose || (o ? i.loose = !0 : this.rules.other.doubleBlankLine.test(p) && (o = !0));\n        let T = null,\n          Y;\n        this.options.gfm && (T = this.rules.other.listIsTask.exec(c), T && (Y = T[0] !== \"[ ] \", c = c.replace(this.rules.other.listReplaceTask, \"\"))), i.items.push({\n          type: \"list_item\",\n          raw: p,\n          task: !!T,\n          checked: Y,\n          loose: !1,\n          text: c,\n          tokens: []\n        }), i.raw += p;\n      }\n      let a = i.items.at(-1);\n      if (a) a.raw = a.raw.trimEnd(), a.text = a.text.trimEnd();else return;\n      i.raw = i.raw.trimEnd();\n      for (let u = 0; u < i.items.length; u++) if (this.lexer.state.top = !1, i.items[u].tokens = this.lexer.blockTokens(i.items[u].text, []), !i.loose) {\n        let p = i.items[u].tokens.filter(f => f.type === \"space\"),\n          c = p.length > 0 && p.some(f => this.rules.other.anyLine.test(f.raw));\n        i.loose = c;\n      }\n      if (i.loose) for (let u = 0; u < i.items.length; u++) i.items[u].loose = !0;\n      return i;\n    }\n  }\n  html(e) {\n    let t = this.rules.block.html.exec(e);\n    if (t) return {\n      type: \"html\",\n      block: !0,\n      raw: t[0],\n      pre: t[1] === \"pre\" || t[1] === \"script\" || t[1] === \"style\",\n      text: t[0]\n    };\n  }\n  def(e) {\n    let t = this.rules.block.def.exec(e);\n    if (t) {\n      let n = t[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal, \" \"),\n        r = t[2] ? t[2].replace(this.rules.other.hrefBrackets, \"$1\").replace(this.rules.inline.anyPunctuation, \"$1\") : \"\",\n        i = t[3] ? t[3].substring(1, t[3].length - 1).replace(this.rules.inline.anyPunctuation, \"$1\") : t[3];\n      return {\n        type: \"def\",\n        tag: n,\n        raw: t[0],\n        href: r,\n        title: i\n      };\n    }\n  }\n  table(e) {\n    let t = this.rules.block.table.exec(e);\n    if (!t || !this.rules.other.tableDelimiter.test(t[2])) return;\n    let n = V(t[1]),\n      r = t[2].replace(this.rules.other.tableAlignChars, \"\").split(\"|\"),\n      i = t[3]?.trim() ? t[3].replace(this.rules.other.tableRowBlankLine, \"\").split(`\n`) : [],\n      s = {\n        type: \"table\",\n        raw: t[0],\n        header: [],\n        align: [],\n        rows: []\n      };\n    if (n.length === r.length) {\n      for (let o of r) this.rules.other.tableAlignRight.test(o) ? s.align.push(\"right\") : this.rules.other.tableAlignCenter.test(o) ? s.align.push(\"center\") : this.rules.other.tableAlignLeft.test(o) ? s.align.push(\"left\") : s.align.push(null);\n      for (let o = 0; o < n.length; o++) s.header.push({\n        text: n[o],\n        tokens: this.lexer.inline(n[o]),\n        header: !0,\n        align: s.align[o]\n      });\n      for (let o of i) s.rows.push(V(o, s.header.length).map((a, u) => ({\n        text: a,\n        tokens: this.lexer.inline(a),\n        header: !1,\n        align: s.align[u]\n      })));\n      return s;\n    }\n  }\n  lheading(e) {\n    let t = this.rules.block.lheading.exec(e);\n    if (t) return {\n      type: \"heading\",\n      raw: t[0],\n      depth: t[2].charAt(0) === \"=\" ? 1 : 2,\n      text: t[1],\n      tokens: this.lexer.inline(t[1])\n    };\n  }\n  paragraph(e) {\n    let t = this.rules.block.paragraph.exec(e);\n    if (t) {\n      let n = t[1].charAt(t[1].length - 1) === `\n` ? t[1].slice(0, -1) : t[1];\n      return {\n        type: \"paragraph\",\n        raw: t[0],\n        text: n,\n        tokens: this.lexer.inline(n)\n      };\n    }\n  }\n  text(e) {\n    let t = this.rules.block.text.exec(e);\n    if (t) return {\n      type: \"text\",\n      raw: t[0],\n      text: t[0],\n      tokens: this.lexer.inline(t[0])\n    };\n  }\n  escape(e) {\n    let t = this.rules.inline.escape.exec(e);\n    if (t) return {\n      type: \"escape\",\n      raw: t[0],\n      text: t[1]\n    };\n  }\n  tag(e) {\n    let t = this.rules.inline.tag.exec(e);\n    if (t) return !this.lexer.state.inLink && this.rules.other.startATag.test(t[0]) ? this.lexer.state.inLink = !0 : this.lexer.state.inLink && this.rules.other.endATag.test(t[0]) && (this.lexer.state.inLink = !1), !this.lexer.state.inRawBlock && this.rules.other.startPreScriptTag.test(t[0]) ? this.lexer.state.inRawBlock = !0 : this.lexer.state.inRawBlock && this.rules.other.endPreScriptTag.test(t[0]) && (this.lexer.state.inRawBlock = !1), {\n      type: \"html\",\n      raw: t[0],\n      inLink: this.lexer.state.inLink,\n      inRawBlock: this.lexer.state.inRawBlock,\n      block: !1,\n      text: t[0]\n    };\n  }\n  link(e) {\n    let t = this.rules.inline.link.exec(e);\n    if (t) {\n      let n = t[2].trim();\n      if (!this.options.pedantic && this.rules.other.startAngleBracket.test(n)) {\n        if (!this.rules.other.endAngleBracket.test(n)) return;\n        let s = z(n.slice(0, -1), \"\\\\\");\n        if ((n.length - s.length) % 2 === 0) return;\n      } else {\n        let s = ge(t[2], \"()\");\n        if (s === -2) return;\n        if (s > -1) {\n          let a = (t[0].indexOf(\"!\") === 0 ? 5 : 4) + t[1].length + s;\n          t[2] = t[2].substring(0, s), t[0] = t[0].substring(0, a).trim(), t[3] = \"\";\n        }\n      }\n      let r = t[2],\n        i = \"\";\n      if (this.options.pedantic) {\n        let s = this.rules.other.pedanticHrefTitle.exec(r);\n        s && (r = s[1], i = s[3]);\n      } else i = t[3] ? t[3].slice(1, -1) : \"\";\n      return r = r.trim(), this.rules.other.startAngleBracket.test(r) && (this.options.pedantic && !this.rules.other.endAngleBracket.test(n) ? r = r.slice(1) : r = r.slice(1, -1)), fe(t, {\n        href: r && r.replace(this.rules.inline.anyPunctuation, \"$1\"),\n        title: i && i.replace(this.rules.inline.anyPunctuation, \"$1\")\n      }, t[0], this.lexer, this.rules);\n    }\n  }\n  reflink(e, t) {\n    let n;\n    if ((n = this.rules.inline.reflink.exec(e)) || (n = this.rules.inline.nolink.exec(e))) {\n      let r = (n[2] || n[1]).replace(this.rules.other.multipleSpaceGlobal, \" \"),\n        i = t[r.toLowerCase()];\n      if (!i) {\n        let s = n[0].charAt(0);\n        return {\n          type: \"text\",\n          raw: s,\n          text: s\n        };\n      }\n      return fe(n, i, n[0], this.lexer, this.rules);\n    }\n  }\n  emStrong(e, t, n = \"\") {\n    let r = this.rules.inline.emStrongLDelim.exec(e);\n    if (!r || r[3] && n.match(this.rules.other.unicodeAlphaNumeric)) return;\n    if (!(r[1] || r[2] || \"\") || !n || this.rules.inline.punctuation.exec(n)) {\n      let s = [...r[0]].length - 1,\n        o,\n        a,\n        u = s,\n        p = 0,\n        c = r[0][0] === \"*\" ? this.rules.inline.emStrongRDelimAst : this.rules.inline.emStrongRDelimUnd;\n      for (c.lastIndex = 0, t = t.slice(-1 * e.length + s); (r = c.exec(t)) != null;) {\n        if (o = r[1] || r[2] || r[3] || r[4] || r[5] || r[6], !o) continue;\n        if (a = [...o].length, r[3] || r[4]) {\n          u += a;\n          continue;\n        } else if ((r[5] || r[6]) && s % 3 && !((s + a) % 3)) {\n          p += a;\n          continue;\n        }\n        if (u -= a, u > 0) continue;\n        a = Math.min(a, a + u + p);\n        let f = [...r[0]][0].length,\n          k = e.slice(0, s + r.index + f + a);\n        if (Math.min(s, a) % 2) {\n          let g = k.slice(1, -1);\n          return {\n            type: \"em\",\n            raw: k,\n            text: g,\n            tokens: this.lexer.inlineTokens(g)\n          };\n        }\n        let x = k.slice(2, -2);\n        return {\n          type: \"strong\",\n          raw: k,\n          text: x,\n          tokens: this.lexer.inlineTokens(x)\n        };\n      }\n    }\n  }\n  codespan(e) {\n    let t = this.rules.inline.code.exec(e);\n    if (t) {\n      let n = t[2].replace(this.rules.other.newLineCharGlobal, \" \"),\n        r = this.rules.other.nonSpaceChar.test(n),\n        i = this.rules.other.startingSpaceChar.test(n) && this.rules.other.endingSpaceChar.test(n);\n      return r && i && (n = n.substring(1, n.length - 1)), {\n        type: \"codespan\",\n        raw: t[0],\n        text: n\n      };\n    }\n  }\n  br(e) {\n    let t = this.rules.inline.br.exec(e);\n    if (t) return {\n      type: \"br\",\n      raw: t[0]\n    };\n  }\n  del(e) {\n    let t = this.rules.inline.del.exec(e);\n    if (t) return {\n      type: \"del\",\n      raw: t[0],\n      text: t[2],\n      tokens: this.lexer.inlineTokens(t[2])\n    };\n  }\n  autolink(e) {\n    let t = this.rules.inline.autolink.exec(e);\n    if (t) {\n      let n, r;\n      return t[2] === \"@\" ? (n = t[1], r = \"mailto:\" + n) : (n = t[1], r = n), {\n        type: \"link\",\n        raw: t[0],\n        text: n,\n        href: r,\n        tokens: [{\n          type: \"text\",\n          raw: n,\n          text: n\n        }]\n      };\n    }\n  }\n  url(e) {\n    let t;\n    if (t = this.rules.inline.url.exec(e)) {\n      let n, r;\n      if (t[2] === \"@\") n = t[0], r = \"mailto:\" + n;else {\n        let i;\n        do i = t[0], t[0] = this.rules.inline._backpedal.exec(t[0])?.[0] ?? \"\"; while (i !== t[0]);\n        n = t[0], t[1] === \"www.\" ? r = \"http://\" + t[0] : r = t[0];\n      }\n      return {\n        type: \"link\",\n        raw: t[0],\n        text: n,\n        href: r,\n        tokens: [{\n          type: \"text\",\n          raw: n,\n          text: n\n        }]\n      };\n    }\n  }\n  inlineText(e) {\n    let t = this.rules.inline.text.exec(e);\n    if (t) {\n      let n = this.lexer.state.inRawBlock;\n      return {\n        type: \"text\",\n        raw: t[0],\n        text: t[0],\n        escaped: n\n      };\n    }\n  }\n};\nvar b = class l {\n  tokens;\n  options;\n  state;\n  tokenizer;\n  inlineQueue;\n  constructor(e) {\n    this.tokens = [], this.tokens.links = Object.create(null), this.options = e || O, this.options.tokenizer = this.options.tokenizer || new y(), this.tokenizer = this.options.tokenizer, this.tokenizer.options = this.options, this.tokenizer.lexer = this, this.inlineQueue = [], this.state = {\n      inLink: !1,\n      inRawBlock: !1,\n      top: !0\n    };\n    let t = {\n      other: m,\n      block: I.normal,\n      inline: M.normal\n    };\n    this.options.pedantic ? (t.block = I.pedantic, t.inline = M.pedantic) : this.options.gfm && (t.block = I.gfm, this.options.breaks ? t.inline = M.breaks : t.inline = M.gfm), this.tokenizer.rules = t;\n  }\n  static get rules() {\n    return {\n      block: I,\n      inline: M\n    };\n  }\n  static lex(e, t) {\n    return new l(t).lex(e);\n  }\n  static lexInline(e, t) {\n    return new l(t).inlineTokens(e);\n  }\n  lex(e) {\n    e = e.replace(m.carriageReturn, `\n`), this.blockTokens(e, this.tokens);\n    for (let t = 0; t < this.inlineQueue.length; t++) {\n      let n = this.inlineQueue[t];\n      this.inlineTokens(n.src, n.tokens);\n    }\n    return this.inlineQueue = [], this.tokens;\n  }\n  blockTokens(e, t = [], n = !1) {\n    for (this.options.pedantic && (e = e.replace(m.tabCharGlobal, \"    \").replace(m.spaceLine, \"\")); e;) {\n      let r;\n      if (this.options.extensions?.block?.some(s => (r = s.call({\n        lexer: this\n      }, e, t)) ? (e = e.substring(r.raw.length), t.push(r), !0) : !1)) continue;\n      if (r = this.tokenizer.space(e)) {\n        e = e.substring(r.raw.length);\n        let s = t.at(-1);\n        r.raw.length === 1 && s !== void 0 ? s.raw += `\n` : t.push(r);\n        continue;\n      }\n      if (r = this.tokenizer.code(e)) {\n        e = e.substring(r.raw.length);\n        let s = t.at(-1);\n        s?.type === \"paragraph\" || s?.type === \"text\" ? (s.raw += `\n` + r.raw, s.text += `\n` + r.text, this.inlineQueue.at(-1).src = s.text) : t.push(r);\n        continue;\n      }\n      if (r = this.tokenizer.fences(e)) {\n        e = e.substring(r.raw.length), t.push(r);\n        continue;\n      }\n      if (r = this.tokenizer.heading(e)) {\n        e = e.substring(r.raw.length), t.push(r);\n        continue;\n      }\n      if (r = this.tokenizer.hr(e)) {\n        e = e.substring(r.raw.length), t.push(r);\n        continue;\n      }\n      if (r = this.tokenizer.blockquote(e)) {\n        e = e.substring(r.raw.length), t.push(r);\n        continue;\n      }\n      if (r = this.tokenizer.list(e)) {\n        e = e.substring(r.raw.length), t.push(r);\n        continue;\n      }\n      if (r = this.tokenizer.html(e)) {\n        e = e.substring(r.raw.length), t.push(r);\n        continue;\n      }\n      if (r = this.tokenizer.def(e)) {\n        e = e.substring(r.raw.length);\n        let s = t.at(-1);\n        s?.type === \"paragraph\" || s?.type === \"text\" ? (s.raw += `\n` + r.raw, s.text += `\n` + r.raw, this.inlineQueue.at(-1).src = s.text) : this.tokens.links[r.tag] || (this.tokens.links[r.tag] = {\n          href: r.href,\n          title: r.title\n        });\n        continue;\n      }\n      if (r = this.tokenizer.table(e)) {\n        e = e.substring(r.raw.length), t.push(r);\n        continue;\n      }\n      if (r = this.tokenizer.lheading(e)) {\n        e = e.substring(r.raw.length), t.push(r);\n        continue;\n      }\n      let i = e;\n      if (this.options.extensions?.startBlock) {\n        let s = 1 / 0,\n          o = e.slice(1),\n          a;\n        this.options.extensions.startBlock.forEach(u => {\n          a = u.call({\n            lexer: this\n          }, o), typeof a == \"number\" && a >= 0 && (s = Math.min(s, a));\n        }), s < 1 / 0 && s >= 0 && (i = e.substring(0, s + 1));\n      }\n      if (this.state.top && (r = this.tokenizer.paragraph(i))) {\n        let s = t.at(-1);\n        n && s?.type === \"paragraph\" ? (s.raw += `\n` + r.raw, s.text += `\n` + r.text, this.inlineQueue.pop(), this.inlineQueue.at(-1).src = s.text) : t.push(r), n = i.length !== e.length, e = e.substring(r.raw.length);\n        continue;\n      }\n      if (r = this.tokenizer.text(e)) {\n        e = e.substring(r.raw.length);\n        let s = t.at(-1);\n        s?.type === \"text\" ? (s.raw += `\n` + r.raw, s.text += `\n` + r.text, this.inlineQueue.pop(), this.inlineQueue.at(-1).src = s.text) : t.push(r);\n        continue;\n      }\n      if (e) {\n        let s = \"Infinite loop on byte: \" + e.charCodeAt(0);\n        if (this.options.silent) {\n          console.error(s);\n          break;\n        } else throw new Error(s);\n      }\n    }\n    return this.state.top = !0, t;\n  }\n  inline(e, t = []) {\n    return this.inlineQueue.push({\n      src: e,\n      tokens: t\n    }), t;\n  }\n  inlineTokens(e, t = []) {\n    let n = e,\n      r = null;\n    if (this.tokens.links) {\n      let o = Object.keys(this.tokens.links);\n      if (o.length > 0) for (; (r = this.tokenizer.rules.inline.reflinkSearch.exec(n)) != null;) o.includes(r[0].slice(r[0].lastIndexOf(\"[\") + 1, -1)) && (n = n.slice(0, r.index) + \"[\" + \"a\".repeat(r[0].length - 2) + \"]\" + n.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex));\n    }\n    for (; (r = this.tokenizer.rules.inline.anyPunctuation.exec(n)) != null;) n = n.slice(0, r.index) + \"++\" + n.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);\n    for (; (r = this.tokenizer.rules.inline.blockSkip.exec(n)) != null;) n = n.slice(0, r.index) + \"[\" + \"a\".repeat(r[0].length - 2) + \"]\" + n.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);\n    let i = !1,\n      s = \"\";\n    for (; e;) {\n      i || (s = \"\"), i = !1;\n      let o;\n      if (this.options.extensions?.inline?.some(u => (o = u.call({\n        lexer: this\n      }, e, t)) ? (e = e.substring(o.raw.length), t.push(o), !0) : !1)) continue;\n      if (o = this.tokenizer.escape(e)) {\n        e = e.substring(o.raw.length), t.push(o);\n        continue;\n      }\n      if (o = this.tokenizer.tag(e)) {\n        e = e.substring(o.raw.length), t.push(o);\n        continue;\n      }\n      if (o = this.tokenizer.link(e)) {\n        e = e.substring(o.raw.length), t.push(o);\n        continue;\n      }\n      if (o = this.tokenizer.reflink(e, this.tokens.links)) {\n        e = e.substring(o.raw.length);\n        let u = t.at(-1);\n        o.type === \"text\" && u?.type === \"text\" ? (u.raw += o.raw, u.text += o.text) : t.push(o);\n        continue;\n      }\n      if (o = this.tokenizer.emStrong(e, n, s)) {\n        e = e.substring(o.raw.length), t.push(o);\n        continue;\n      }\n      if (o = this.tokenizer.codespan(e)) {\n        e = e.substring(o.raw.length), t.push(o);\n        continue;\n      }\n      if (o = this.tokenizer.br(e)) {\n        e = e.substring(o.raw.length), t.push(o);\n        continue;\n      }\n      if (o = this.tokenizer.del(e)) {\n        e = e.substring(o.raw.length), t.push(o);\n        continue;\n      }\n      if (o = this.tokenizer.autolink(e)) {\n        e = e.substring(o.raw.length), t.push(o);\n        continue;\n      }\n      if (!this.state.inLink && (o = this.tokenizer.url(e))) {\n        e = e.substring(o.raw.length), t.push(o);\n        continue;\n      }\n      let a = e;\n      if (this.options.extensions?.startInline) {\n        let u = 1 / 0,\n          p = e.slice(1),\n          c;\n        this.options.extensions.startInline.forEach(f => {\n          c = f.call({\n            lexer: this\n          }, p), typeof c == \"number\" && c >= 0 && (u = Math.min(u, c));\n        }), u < 1 / 0 && u >= 0 && (a = e.substring(0, u + 1));\n      }\n      if (o = this.tokenizer.inlineText(a)) {\n        e = e.substring(o.raw.length), o.raw.slice(-1) !== \"_\" && (s = o.raw.slice(-1)), i = !0;\n        let u = t.at(-1);\n        u?.type === \"text\" ? (u.raw += o.raw, u.text += o.text) : t.push(o);\n        continue;\n      }\n      if (e) {\n        let u = \"Infinite loop on byte: \" + e.charCodeAt(0);\n        if (this.options.silent) {\n          console.error(u);\n          break;\n        } else throw new Error(u);\n      }\n    }\n    return t;\n  }\n};\nvar P = class {\n  options;\n  parser;\n  constructor(e) {\n    this.options = e || O;\n  }\n  space(e) {\n    return \"\";\n  }\n  code({\n    text: e,\n    lang: t,\n    escaped: n\n  }) {\n    let r = (t || \"\").match(m.notSpaceStart)?.[0],\n      i = e.replace(m.endingNewline, \"\") + `\n`;\n    return r ? '<pre><code class=\"language-' + w(r) + '\">' + (n ? i : w(i, !0)) + `</code></pre>\n` : \"<pre><code>\" + (n ? i : w(i, !0)) + `</code></pre>\n`;\n  }\n  blockquote({\n    tokens: e\n  }) {\n    return `<blockquote>\n${this.parser.parse(e)}</blockquote>\n`;\n  }\n  html({\n    text: e\n  }) {\n    return e;\n  }\n  heading({\n    tokens: e,\n    depth: t\n  }) {\n    return `<h${t}>${this.parser.parseInline(e)}</h${t}>\n`;\n  }\n  hr(e) {\n    return `<hr>\n`;\n  }\n  list(e) {\n    let t = e.ordered,\n      n = e.start,\n      r = \"\";\n    for (let o = 0; o < e.items.length; o++) {\n      let a = e.items[o];\n      r += this.listitem(a);\n    }\n    let i = t ? \"ol\" : \"ul\",\n      s = t && n !== 1 ? ' start=\"' + n + '\"' : \"\";\n    return \"<\" + i + s + `>\n` + r + \"</\" + i + `>\n`;\n  }\n  listitem(e) {\n    let t = \"\";\n    if (e.task) {\n      let n = this.checkbox({\n        checked: !!e.checked\n      });\n      e.loose ? e.tokens[0]?.type === \"paragraph\" ? (e.tokens[0].text = n + \" \" + e.tokens[0].text, e.tokens[0].tokens && e.tokens[0].tokens.length > 0 && e.tokens[0].tokens[0].type === \"text\" && (e.tokens[0].tokens[0].text = n + \" \" + w(e.tokens[0].tokens[0].text), e.tokens[0].tokens[0].escaped = !0)) : e.tokens.unshift({\n        type: \"text\",\n        raw: n + \" \",\n        text: n + \" \",\n        escaped: !0\n      }) : t += n + \" \";\n    }\n    return t += this.parser.parse(e.tokens, !!e.loose), `<li>${t}</li>\n`;\n  }\n  checkbox({\n    checked: e\n  }) {\n    return \"<input \" + (e ? 'checked=\"\" ' : \"\") + 'disabled=\"\" type=\"checkbox\">';\n  }\n  paragraph({\n    tokens: e\n  }) {\n    return `<p>${this.parser.parseInline(e)}</p>\n`;\n  }\n  table(e) {\n    let t = \"\",\n      n = \"\";\n    for (let i = 0; i < e.header.length; i++) n += this.tablecell(e.header[i]);\n    t += this.tablerow({\n      text: n\n    });\n    let r = \"\";\n    for (let i = 0; i < e.rows.length; i++) {\n      let s = e.rows[i];\n      n = \"\";\n      for (let o = 0; o < s.length; o++) n += this.tablecell(s[o]);\n      r += this.tablerow({\n        text: n\n      });\n    }\n    return r && (r = `<tbody>${r}</tbody>`), `<table>\n<thead>\n` + t + `</thead>\n` + r + `</table>\n`;\n  }\n  tablerow({\n    text: e\n  }) {\n    return `<tr>\n${e}</tr>\n`;\n  }\n  tablecell(e) {\n    let t = this.parser.parseInline(e.tokens),\n      n = e.header ? \"th\" : \"td\";\n    return (e.align ? `<${n} align=\"${e.align}\">` : `<${n}>`) + t + `</${n}>\n`;\n  }\n  strong({\n    tokens: e\n  }) {\n    return `<strong>${this.parser.parseInline(e)}</strong>`;\n  }\n  em({\n    tokens: e\n  }) {\n    return `<em>${this.parser.parseInline(e)}</em>`;\n  }\n  codespan({\n    text: e\n  }) {\n    return `<code>${w(e, !0)}</code>`;\n  }\n  br(e) {\n    return \"<br>\";\n  }\n  del({\n    tokens: e\n  }) {\n    return `<del>${this.parser.parseInline(e)}</del>`;\n  }\n  link({\n    href: e,\n    title: t,\n    tokens: n\n  }) {\n    let r = this.parser.parseInline(n),\n      i = J(e);\n    if (i === null) return r;\n    e = i;\n    let s = '<a href=\"' + e + '\"';\n    return t && (s += ' title=\"' + w(t) + '\"'), s += \">\" + r + \"</a>\", s;\n  }\n  image({\n    href: e,\n    title: t,\n    text: n,\n    tokens: r\n  }) {\n    r && (n = this.parser.parseInline(r, this.parser.textRenderer));\n    let i = J(e);\n    if (i === null) return w(n);\n    e = i;\n    let s = `<img src=\"${e}\" alt=\"${n}\"`;\n    return t && (s += ` title=\"${w(t)}\"`), s += \">\", s;\n  }\n  text(e) {\n    return \"tokens\" in e && e.tokens ? this.parser.parseInline(e.tokens) : \"escaped\" in e && e.escaped ? e.text : w(e.text);\n  }\n};\nvar S = class {\n  strong({\n    text: e\n  }) {\n    return e;\n  }\n  em({\n    text: e\n  }) {\n    return e;\n  }\n  codespan({\n    text: e\n  }) {\n    return e;\n  }\n  del({\n    text: e\n  }) {\n    return e;\n  }\n  html({\n    text: e\n  }) {\n    return e;\n  }\n  text({\n    text: e\n  }) {\n    return e;\n  }\n  link({\n    text: e\n  }) {\n    return \"\" + e;\n  }\n  image({\n    text: e\n  }) {\n    return \"\" + e;\n  }\n  br() {\n    return \"\";\n  }\n};\nvar R = class l {\n  options;\n  renderer;\n  textRenderer;\n  constructor(e) {\n    this.options = e || O, this.options.renderer = this.options.renderer || new P(), this.renderer = this.options.renderer, this.renderer.options = this.options, this.renderer.parser = this, this.textRenderer = new S();\n  }\n  static parse(e, t) {\n    return new l(t).parse(e);\n  }\n  static parseInline(e, t) {\n    return new l(t).parseInline(e);\n  }\n  parse(e, t = !0) {\n    let n = \"\";\n    for (let r = 0; r < e.length; r++) {\n      let i = e[r];\n      if (this.options.extensions?.renderers?.[i.type]) {\n        let o = i,\n          a = this.options.extensions.renderers[o.type].call({\n            parser: this\n          }, o);\n        if (a !== !1 || ![\"space\", \"hr\", \"heading\", \"code\", \"table\", \"blockquote\", \"list\", \"html\", \"paragraph\", \"text\"].includes(o.type)) {\n          n += a || \"\";\n          continue;\n        }\n      }\n      let s = i;\n      switch (s.type) {\n        case \"space\":\n          {\n            n += this.renderer.space(s);\n            continue;\n          }\n        case \"hr\":\n          {\n            n += this.renderer.hr(s);\n            continue;\n          }\n        case \"heading\":\n          {\n            n += this.renderer.heading(s);\n            continue;\n          }\n        case \"code\":\n          {\n            n += this.renderer.code(s);\n            continue;\n          }\n        case \"table\":\n          {\n            n += this.renderer.table(s);\n            continue;\n          }\n        case \"blockquote\":\n          {\n            n += this.renderer.blockquote(s);\n            continue;\n          }\n        case \"list\":\n          {\n            n += this.renderer.list(s);\n            continue;\n          }\n        case \"html\":\n          {\n            n += this.renderer.html(s);\n            continue;\n          }\n        case \"paragraph\":\n          {\n            n += this.renderer.paragraph(s);\n            continue;\n          }\n        case \"text\":\n          {\n            let o = s,\n              a = this.renderer.text(o);\n            for (; r + 1 < e.length && e[r + 1].type === \"text\";) o = e[++r], a += `\n` + this.renderer.text(o);\n            t ? n += this.renderer.paragraph({\n              type: \"paragraph\",\n              raw: a,\n              text: a,\n              tokens: [{\n                type: \"text\",\n                raw: a,\n                text: a,\n                escaped: !0\n              }]\n            }) : n += a;\n            continue;\n          }\n        default:\n          {\n            let o = 'Token with \"' + s.type + '\" type was not found.';\n            if (this.options.silent) return console.error(o), \"\";\n            throw new Error(o);\n          }\n      }\n    }\n    return n;\n  }\n  parseInline(e, t = this.renderer) {\n    let n = \"\";\n    for (let r = 0; r < e.length; r++) {\n      let i = e[r];\n      if (this.options.extensions?.renderers?.[i.type]) {\n        let o = this.options.extensions.renderers[i.type].call({\n          parser: this\n        }, i);\n        if (o !== !1 || ![\"escape\", \"html\", \"link\", \"image\", \"strong\", \"em\", \"codespan\", \"br\", \"del\", \"text\"].includes(i.type)) {\n          n += o || \"\";\n          continue;\n        }\n      }\n      let s = i;\n      switch (s.type) {\n        case \"escape\":\n          {\n            n += t.text(s);\n            break;\n          }\n        case \"html\":\n          {\n            n += t.html(s);\n            break;\n          }\n        case \"link\":\n          {\n            n += t.link(s);\n            break;\n          }\n        case \"image\":\n          {\n            n += t.image(s);\n            break;\n          }\n        case \"strong\":\n          {\n            n += t.strong(s);\n            break;\n          }\n        case \"em\":\n          {\n            n += t.em(s);\n            break;\n          }\n        case \"codespan\":\n          {\n            n += t.codespan(s);\n            break;\n          }\n        case \"br\":\n          {\n            n += t.br(s);\n            break;\n          }\n        case \"del\":\n          {\n            n += t.del(s);\n            break;\n          }\n        case \"text\":\n          {\n            n += t.text(s);\n            break;\n          }\n        default:\n          {\n            let o = 'Token with \"' + s.type + '\" type was not found.';\n            if (this.options.silent) return console.error(o), \"\";\n            throw new Error(o);\n          }\n      }\n    }\n    return n;\n  }\n};\nvar $ = class {\n  options;\n  block;\n  constructor(e) {\n    this.options = e || O;\n  }\n  static passThroughHooks = new Set([\"preprocess\", \"postprocess\", \"processAllTokens\"]);\n  preprocess(e) {\n    return e;\n  }\n  postprocess(e) {\n    return e;\n  }\n  processAllTokens(e) {\n    return e;\n  }\n  provideLexer() {\n    return this.block ? b.lex : b.lexInline;\n  }\n  provideParser() {\n    return this.block ? R.parse : R.parseInline;\n  }\n};\nvar B = class {\n  defaults = L();\n  options = this.setOptions;\n  parse = this.parseMarkdown(!0);\n  parseInline = this.parseMarkdown(!1);\n  Parser = R;\n  Renderer = P;\n  TextRenderer = S;\n  Lexer = b;\n  Tokenizer = y;\n  Hooks = $;\n  constructor(...e) {\n    this.use(...e);\n  }\n  walkTokens(e, t) {\n    let n = [];\n    for (let r of e) switch (n = n.concat(t.call(this, r)), r.type) {\n      case \"table\":\n        {\n          let i = r;\n          for (let s of i.header) n = n.concat(this.walkTokens(s.tokens, t));\n          for (let s of i.rows) for (let o of s) n = n.concat(this.walkTokens(o.tokens, t));\n          break;\n        }\n      case \"list\":\n        {\n          let i = r;\n          n = n.concat(this.walkTokens(i.items, t));\n          break;\n        }\n      default:\n        {\n          let i = r;\n          this.defaults.extensions?.childTokens?.[i.type] ? this.defaults.extensions.childTokens[i.type].forEach(s => {\n            let o = i[s].flat(1 / 0);\n            n = n.concat(this.walkTokens(o, t));\n          }) : i.tokens && (n = n.concat(this.walkTokens(i.tokens, t)));\n        }\n    }\n    return n;\n  }\n  use(...e) {\n    let t = this.defaults.extensions || {\n      renderers: {},\n      childTokens: {}\n    };\n    return e.forEach(n => {\n      let r = {\n        ...n\n      };\n      if (r.async = this.defaults.async || r.async || !1, n.extensions && (n.extensions.forEach(i => {\n        if (!i.name) throw new Error(\"extension name required\");\n        if (\"renderer\" in i) {\n          let s = t.renderers[i.name];\n          s ? t.renderers[i.name] = function (...o) {\n            let a = i.renderer.apply(this, o);\n            return a === !1 && (a = s.apply(this, o)), a;\n          } : t.renderers[i.name] = i.renderer;\n        }\n        if (\"tokenizer\" in i) {\n          if (!i.level || i.level !== \"block\" && i.level !== \"inline\") throw new Error(\"extension level must be 'block' or 'inline'\");\n          let s = t[i.level];\n          s ? s.unshift(i.tokenizer) : t[i.level] = [i.tokenizer], i.start && (i.level === \"block\" ? t.startBlock ? t.startBlock.push(i.start) : t.startBlock = [i.start] : i.level === \"inline\" && (t.startInline ? t.startInline.push(i.start) : t.startInline = [i.start]));\n        }\n        \"childTokens\" in i && i.childTokens && (t.childTokens[i.name] = i.childTokens);\n      }), r.extensions = t), n.renderer) {\n        let i = this.defaults.renderer || new P(this.defaults);\n        for (let s in n.renderer) {\n          if (!(s in i)) throw new Error(`renderer '${s}' does not exist`);\n          if ([\"options\", \"parser\"].includes(s)) continue;\n          let o = s,\n            a = n.renderer[o],\n            u = i[o];\n          i[o] = (...p) => {\n            let c = a.apply(i, p);\n            return c === !1 && (c = u.apply(i, p)), c || \"\";\n          };\n        }\n        r.renderer = i;\n      }\n      if (n.tokenizer) {\n        let i = this.defaults.tokenizer || new y(this.defaults);\n        for (let s in n.tokenizer) {\n          if (!(s in i)) throw new Error(`tokenizer '${s}' does not exist`);\n          if ([\"options\", \"rules\", \"lexer\"].includes(s)) continue;\n          let o = s,\n            a = n.tokenizer[o],\n            u = i[o];\n          i[o] = (...p) => {\n            let c = a.apply(i, p);\n            return c === !1 && (c = u.apply(i, p)), c;\n          };\n        }\n        r.tokenizer = i;\n      }\n      if (n.hooks) {\n        let i = this.defaults.hooks || new $();\n        for (let s in n.hooks) {\n          if (!(s in i)) throw new Error(`hook '${s}' does not exist`);\n          if ([\"options\", \"block\"].includes(s)) continue;\n          let o = s,\n            a = n.hooks[o],\n            u = i[o];\n          $.passThroughHooks.has(s) ? i[o] = p => {\n            if (this.defaults.async) return Promise.resolve(a.call(i, p)).then(f => u.call(i, f));\n            let c = a.call(i, p);\n            return u.call(i, c);\n          } : i[o] = (...p) => {\n            let c = a.apply(i, p);\n            return c === !1 && (c = u.apply(i, p)), c;\n          };\n        }\n        r.hooks = i;\n      }\n      if (n.walkTokens) {\n        let i = this.defaults.walkTokens,\n          s = n.walkTokens;\n        r.walkTokens = function (o) {\n          let a = [];\n          return a.push(s.call(this, o)), i && (a = a.concat(i.call(this, o))), a;\n        };\n      }\n      this.defaults = {\n        ...this.defaults,\n        ...r\n      };\n    }), this;\n  }\n  setOptions(e) {\n    return this.defaults = {\n      ...this.defaults,\n      ...e\n    }, this;\n  }\n  lexer(e, t) {\n    return b.lex(e, t ?? this.defaults);\n  }\n  parser(e, t) {\n    return R.parse(e, t ?? this.defaults);\n  }\n  parseMarkdown(e) {\n    return (n, r) => {\n      let i = {\n          ...r\n        },\n        s = {\n          ...this.defaults,\n          ...i\n        },\n        o = this.onError(!!s.silent, !!s.async);\n      if (this.defaults.async === !0 && i.async === !1) return o(new Error(\"marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise.\"));\n      if (typeof n > \"u\" || n === null) return o(new Error(\"marked(): input parameter is undefined or null\"));\n      if (typeof n != \"string\") return o(new Error(\"marked(): input parameter is of type \" + Object.prototype.toString.call(n) + \", string expected\"));\n      s.hooks && (s.hooks.options = s, s.hooks.block = e);\n      let a = s.hooks ? s.hooks.provideLexer() : e ? b.lex : b.lexInline,\n        u = s.hooks ? s.hooks.provideParser() : e ? R.parse : R.parseInline;\n      if (s.async) return Promise.resolve(s.hooks ? s.hooks.preprocess(n) : n).then(p => a(p, s)).then(p => s.hooks ? s.hooks.processAllTokens(p) : p).then(p => s.walkTokens ? Promise.all(this.walkTokens(p, s.walkTokens)).then(() => p) : p).then(p => u(p, s)).then(p => s.hooks ? s.hooks.postprocess(p) : p).catch(o);\n      try {\n        s.hooks && (n = s.hooks.preprocess(n));\n        let p = a(n, s);\n        s.hooks && (p = s.hooks.processAllTokens(p)), s.walkTokens && this.walkTokens(p, s.walkTokens);\n        let c = u(p, s);\n        return s.hooks && (c = s.hooks.postprocess(c)), c;\n      } catch (p) {\n        return o(p);\n      }\n    };\n  }\n  onError(e, t) {\n    return n => {\n      if (n.message += `\nPlease report this to https://github.com/markedjs/marked.`, e) {\n        let r = \"<p>An error occurred:</p><pre>\" + w(n.message + \"\", !0) + \"</pre>\";\n        return t ? Promise.resolve(r) : r;\n      }\n      if (t) return Promise.reject(n);\n      throw n;\n    };\n  }\n};\nvar _ = new B();\nfunction d(l, e) {\n  return _.parse(l, e);\n}\nd.options = d.setOptions = function (l) {\n  return _.setOptions(l), d.defaults = _.defaults, H(d.defaults), d;\n};\nd.getDefaults = L;\nd.defaults = O;\nd.use = function (...l) {\n  return _.use(...l), d.defaults = _.defaults, H(d.defaults), d;\n};\nd.walkTokens = function (l, e) {\n  return _.walkTokens(l, e);\n};\nd.parseInline = _.parseInline;\nd.Parser = R;\nd.parser = R.parse;\nd.Renderer = P;\nd.TextRenderer = S;\nd.Lexer = b;\nd.lexer = b.lex;\nd.Tokenizer = y;\nd.Hooks = $;\nd.parse = d;\nvar Dt = d.options,\n  Zt = d.setOptions,\n  Gt = d.use,\n  Ht = d.walkTokens,\n  Nt = d.parseInline,\n  jt = d,\n  Ft = R.parse,\n  Qt = b.lex;\nexport { $ as Hooks, b as Lexer, B as Marked, R as Parser, P as Renderer, S as TextRenderer, y as Tokenizer, O as defaults, L as getDefaults, Qt as lexer, d as marked, Dt as options, jt as parse, Nt as parseInline, Ft as parser, Zt as setOptions, Gt as use, Ht as walkTokens };\n//# sourceMappingURL=marked.esm.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}