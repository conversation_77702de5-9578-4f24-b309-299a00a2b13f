{"ast": null, "code": "import { marked } from 'marked';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../auth.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"../anonymous-user/anonymous-user.component\";\nconst _c0 = [\"messagesContainer\"];\nfunction ChatComponent_div_1_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 17);\n    i0.ɵɵelement(1, \"div\", 18);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    \"user-message\": a0,\n    \"bot-message\": a1\n  };\n};\nfunction ChatComponent_div_1_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20);\n    i0.ɵɵelement(2, \"div\", 21);\n    i0.ɵɵelementStart(3, \"div\", 22);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r9 = ctx.$implicit;\n    const ctx_r7 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(6, _c1, message_r9.isUser, !message_r9.isUser));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r7.formatMessageText(message_r9.text), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 3, message_r9.timestamp, \"short\"));\n  }\n}\nfunction ChatComponent_div_1_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 23)(1, \"div\", 20)(2, \"div\", 24);\n    i0.ɵɵelement(3, \"span\")(4, \"span\")(5, \"span\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ChatComponent_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 12, 13);\n    i0.ɵɵlistener(\"scroll\", function ChatComponent_div_1_div_1_Template_div_scroll_0_listener($event) {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r10.onScroll($event));\n    });\n    i0.ɵɵtemplate(2, ChatComponent_div_1_div_1_div_2_Template, 2, 0, \"div\", 14);\n    i0.ɵɵtemplate(3, ChatComponent_div_1_div_1_div_3_Template, 6, 9, \"div\", 15);\n    i0.ɵɵtemplate(4, ChatComponent_div_1_div_1_div_4_Template, 6, 0, \"div\", 16);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoadingHistory);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.messages);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoading);\n  }\n}\nfunction ChatComponent_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"div\", 27);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 28);\n    i0.ɵɵelement(4, \"path\", 29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(5, \"h2\");\n    i0.ɵɵtext(6, \"Welcome to News Agent\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8, \"Ask me anything about current news, events, or any topic you're curious about!\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ChatComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵtemplate(1, ChatComponent_div_1_div_1_Template, 5, 3, \"div\", 3);\n    i0.ɵɵtemplate(2, ChatComponent_div_1_div_2_Template, 9, 0, \"div\", 4);\n    i0.ɵɵelementStart(3, \"div\", 5)(4, \"div\", 6)(5, \"textarea\", 7, 8);\n    i0.ɵɵlistener(\"ngModelChange\", function ChatComponent_div_1_Template_textarea_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.currentMessage = $event);\n    })(\"keydown\", function ChatComponent_div_1_Template_textarea_keydown_5_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.onKeyPress($event));\n    })(\"input\", function ChatComponent_div_1_Template_textarea_input_5_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.adjustTextareaHeight($event));\n    });\n    i0.ɵɵtext(7, \"      \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function ChatComponent_div_1_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r16 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r16.sendMessage());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(9, \"svg\", 10);\n    i0.ɵɵelement(10, \"path\", 11);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messages.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messages.length === 0 && !ctx_r0.isLoading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"centered\", ctx_r0.messages.length === 0)(\"bottom\", ctx_r0.messages.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.currentMessage);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.currentMessage.trim() || ctx_r0.isLoading);\n  }\n}\nfunction ChatComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵelement(1, \"div\", 18);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Initializing chat...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ChatComponent {\n  constructor(authService) {\n    this.authService = authService;\n    this.messages = [];\n    this.currentMessage = '';\n    this.isLoading = false;\n    this.isAuthenticated = false;\n    this.currentSessionId = null;\n    // Pagination\n    this.currentPage = 1;\n    this.hasNextPage = false;\n    this.isLoadingHistory = false;\n    // Scroll management\n    this.shouldScrollToBottom = true;\n    this.lastScrollHeight = 0;\n    this.initialLoadComplete = false;\n    this.userHasScrolled = false;\n  }\n  ngOnInit() {\n    // Ensure user is authenticated before initializing chat\n    this.authService.ensureAuthenticated().subscribe({\n      next: token => {\n        console.log('User authenticated successfully');\n        this.isAuthenticated = true;\n        this.loadChatHistory(1, false);\n      },\n      error: error => {\n        console.error('Authentication failed:', error);\n        this.isAuthenticated = false;\n      }\n    });\n  }\n  ngAfterViewChecked() {\n    // Auto-scroll to bottom only for new messages\n    if (this.shouldScrollToBottom) {\n      this.scrollToBottom();\n      this.shouldScrollToBottom = false;\n    }\n  }\n  // Listen for scroll events to load more history\n  onScroll(event) {\n    const element = event.target;\n    const scrollTop = element.scrollTop;\n    const scrollHeight = element.scrollHeight;\n    const clientHeight = element.clientHeight;\n    // Mark that user has scrolled manually (not programmatic)\n    if (this.initialLoadComplete) {\n      this.userHasScrolled = true;\n    }\n    // Clear existing timeout\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n    // Debounce scroll events and check conditions\n    this.scrollTimeout = setTimeout(() => {\n      // Only load more if:\n      // 1. Initial load is complete\n      // 2. User has scrolled manually at least once\n      // 3. User is near the top (scrollTop < 100)\n      // 4. There are more pages to load\n      // 5. Not currently loading\n      // 6. User is not at the very bottom (to avoid conflicts with auto-scroll)\n      const isNearTop = scrollTop < 100;\n      const isNotAtBottom = scrollTop < scrollHeight - clientHeight - 50;\n      if (isNearTop && isNotAtBottom && this.hasNextPage && !this.isLoadingHistory && this.initialLoadComplete && this.userHasScrolled) {\n        this.lastScrollHeight = scrollHeight;\n        this.loadMoreHistory();\n      }\n    }, 100); // 100ms debounce\n  }\n\n  loadChatHistory(page = 1, append = false) {\n    if (!this.isAuthenticated) return;\n    this.isLoadingHistory = true;\n    this.authService.loadChatHistory(page, this.currentSessionId || undefined).subscribe({\n      next: response => {\n        const newMessages = response.results.map(msg => this.convertChatMessageToMessage(msg));\n        if (append) {\n          // For pagination - reverse the new messages (since API returns newest first)\n          // and prepend older messages to beginning\n          const reversedNewMessages = [...newMessages].reverse();\n          this.messages = [...reversedNewMessages, ...this.messages];\n          this.maintainScrollPosition();\n        } else {\n          // For initial load - reverse messages to get chronological order (oldest first)\n          this.messages = [...newMessages].reverse();\n          this.shouldScrollToBottom = true;\n          // Set initial load complete after scroll positioning is done\n          setTimeout(() => {\n            this.initialLoadComplete = true;\n          }, 200);\n        }\n        // Update pagination info\n        this.currentPage = page;\n        this.hasNextPage = !!response.next;\n        this.isLoadingHistory = false;\n      },\n      error: error => {\n        console.error('Error loading chat history:', error);\n        this.isLoadingHistory = false;\n        // If this was the initial load, still mark it as complete after delay\n        if (!append) {\n          setTimeout(() => {\n            this.initialLoadComplete = true;\n          }, 200);\n        }\n      }\n    });\n  }\n  // Convert Django ChatMessage to frontend Message format\n  convertChatMessageToMessage(chatMessage) {\n    return {\n      id: chatMessage.id,\n      text: chatMessage.message,\n      isUser: chatMessage.sender === 'user',\n      timestamp: new Date(chatMessage.timestamp),\n      session: chatMessage.session,\n      prompt: chatMessage.prompt || undefined,\n      model: chatMessage.model || undefined,\n      news_articles: chatMessage.news_articles || undefined\n    };\n  }\n  // Load more chat history (pagination) - triggered by scroll\n  loadMoreHistory() {\n    if (this.hasNextPage && !this.isLoadingHistory) {\n      this.loadChatHistory(this.currentPage + 1, true);\n    }\n  }\n  // Maintain scroll position when loading older messages\n  maintainScrollPosition() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        const newScrollHeight = element.scrollHeight;\n        const scrollDifference = newScrollHeight - this.lastScrollHeight;\n        element.scrollTop = scrollDifference;\n      }\n    }, 50);\n  }\n  sendMessage() {\n    if (!this.currentMessage.trim() || this.isLoading || !this.isAuthenticated) {\n      return;\n    }\n    // Store the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n    // Create temporary user message and add it instantly\n    const tempUserMessage = {\n      text: messageToSend,\n      isUser: true,\n      timestamp: new Date()\n    };\n    // Add user message instantly to the chat\n    this.messages.push(tempUserMessage);\n    this.shouldScrollToBottom = true;\n    // Set loading state\n    this.isLoading = true;\n    // Call the API through auth service\n    this.authService.sendMessageToChatbot(messageToSend, this.currentSessionId || undefined).subscribe({\n      next: response => {\n        // Convert backend messages\n        const userMessage = this.convertChatMessageToMessage(response.user_message);\n        const botMessage = this.convertChatMessageToMessage(response.bot_message);\n        // Replace the temporary user message with the one from backend\n        const lastMessageIndex = this.messages.length - 1;\n        if (lastMessageIndex >= 0 && this.messages[lastMessageIndex].isUser) {\n          this.messages[lastMessageIndex] = userMessage;\n        }\n        // Add bot message\n        this.messages.push(botMessage);\n        // Store session ID for future requests\n        if (!this.currentSessionId) {\n          this.currentSessionId = response.user_message.session;\n        }\n        this.isLoading = false;\n        this.shouldScrollToBottom = true;\n      },\n      error: error => {\n        console.error('Error sending message:', error);\n        const errorMessage = {\n          text: 'Sorry, there was an error processing your message. Please try again.',\n          isUser: false,\n          timestamp: new Date()\n        };\n        this.messages.push(errorMessage);\n        this.isLoading = false;\n        this.shouldScrollToBottom = true;\n      }\n    });\n  }\n  clearHistory() {\n    this.messages = [];\n    this.currentPage = 1;\n    this.hasNextPage = false;\n    this.initialLoadComplete = false;\n    this.userHasScrolled = false;\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n  }\n  refreshHistory() {\n    this.currentPage = 1;\n    this.initialLoadComplete = false;\n    this.userHasScrolled = false;\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n    this.loadChatHistory();\n  }\n  onKeyPress(event) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n  adjustTextareaHeight(event) {\n    const textarea = event.target;\n    textarea.style.height = 'auto';\n    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';\n  }\n  scrollToBottom() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 50);\n  }\n  // Format message text using marked library for markdown\n  formatMessageText(text) {\n    if (!text) return '';\n    try {\n      // Configure marked to be more restrictive for security\n      marked.setOptions({\n        breaks: true,\n        gfm: true // Enable GitHub Flavored Markdown\n      });\n      // Convert markdown to HTML using marked (synchronous)\n      const htmlContent = marked.parse(text);\n      // Basic sanitization - remove script tags and dangerous attributes\n      let sanitizedHtml = htmlContent.replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, '').replace(/javascript:/gi, '').replace(/on\\w+\\s*=/gi, '');\n      return sanitizedHtml;\n    } catch (error) {\n      console.error('Error formatting message text:', error);\n      // Fallback to plain text with basic line break conversion\n      return text.replace(/\\n/g, '<br>');\n    }\n  }\n  ngOnDestroy() {\n    // Clean up any pending timeouts\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n  }\n  static #_ = this.ɵfac = function ChatComponent_Factory(t) {\n    return new (t || ChatComponent)(i0.ɵɵdirectiveInject(i1.AuthService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ChatComponent,\n    selectors: [[\"app-chat\"]],\n    viewQuery: function ChatComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n      }\n    },\n    decls: 3,\n    vars: 2,\n    consts: [[\"class\", \"chat-container\", 4, \"ngIf\"], [\"class\", \"auth-loading\", 4, \"ngIf\"], [1, \"chat-container\"], [\"class\", \"messages-container\", 3, \"scroll\", 4, \"ngIf\"], [\"class\", \"welcome-container\", 4, \"ngIf\"], [1, \"input-container\"], [1, \"input-wrapper\"], [\"placeholder\", \"Ask anything\", \"rows\", \"1\", 1, \"message-input\", 3, \"ngModel\", \"ngModelChange\", \"keydown\", \"input\"], [\"messageTextarea\", \"\"], [1, \"send-button\", 3, \"disabled\", \"click\"], [\"width\", \"24\", \"height\", \"24\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M2 21L23 12L2 3V10L17 12L2 14V21Z\", \"fill\", \"currentColor\"], [1, \"messages-container\", 3, \"scroll\"], [\"messagesContainer\", \"\"], [\"class\", \"pagination-loading\", 4, \"ngIf\"], [\"class\", \"message\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"message bot-message\", 4, \"ngIf\"], [1, \"pagination-loading\"], [1, \"loading-spinner\"], [1, \"message\", 3, \"ngClass\"], [1, \"message-content\"], [1, \"message-text\", 3, \"innerHTML\"], [1, \"message-time\"], [1, \"message\", \"bot-message\"], [1, \"typing-indicator\"], [1, \"welcome-container\"], [1, \"welcome-content\"], [1, \"welcome-icon\"], [\"width\", \"48\", \"height\", \"48\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\", \"fill\", \"currentColor\"], [1, \"auth-loading\"]],\n    template: function ChatComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelement(0, \"app-anonymous-user\");\n        i0.ɵɵtemplate(1, ChatComponent_div_1_Template, 11, 8, \"div\", 0);\n        i0.ɵɵtemplate(2, ChatComponent_div_2_Template, 4, 0, \"div\", 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.isAuthenticated);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.isAuthenticated);\n      }\n    },\n    dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, i4.AnonymousUserComponent, i2.DatePipe],\n    styles: [\"\\n\\n.chat-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%; \\n\\n  min-height: 500px; \\n\\n  max-width: 900px;\\n  margin: 0 auto;\\n  background: linear-gradient(to bottom, #ffffff 0%, #fefefe 100%);\\n  position: relative; \\n\\n  overflow: hidden;\\n  font-family: var(--font-family-primary);\\n  border-radius: var(--radius-2xl);\\n  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08), 0 4px 12px rgba(0, 0, 0, 0.04);\\n  border: 1px solid rgba(255, 255, 255, 0.8);\\n}\\n\\n.messages-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  overflow-y: auto;\\n  padding: var(--space-6) var(--space-8) 120px var(--space-8);\\n  background: #f9fafb;\\n  max-height: calc(100vh - 160px);\\n  scroll-behavior: smooth;\\n  position: relative;\\n  z-index: 10;\\n  \\n\\n}\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 6px;\\n}\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f5f9;\\n  border-radius: var(--radius-md);\\n}\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #cbd5e1;\\n  border-radius: var(--radius-md);\\n  -webkit-transition: var(--transition-fast);\\n  transition: var(--transition-fast);\\n}\\n.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #94a3b8;\\n}\\n\\n\\n\\n.welcome-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  padding: var(--space-8);\\n  background: #f9fafb;\\n}\\n\\n.welcome-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  max-width: 400px;\\n}\\n.welcome-content[_ngcontent-%COMP%]   .welcome-icon[_ngcontent-%COMP%] {\\n  margin-bottom: var(--space-6);\\n  color: var(--color-primary);\\n  display: flex;\\n  justify-content: center;\\n}\\n.welcome-content[_ngcontent-%COMP%]   .welcome-icon[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 64px;\\n  height: 64px;\\n  opacity: 0.8;\\n}\\n.welcome-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: var(--font-size-2xl);\\n  font-weight: var(--font-weight-bold);\\n  color: var(--color-gray-800);\\n  margin-bottom: var(--space-4);\\n  background: linear-gradient(135deg, #4f46e5 0%, #6366f1 50%, #8b5cf6 100%);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n.welcome-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: var(--font-size-lg);\\n  color: var(--color-gray-600);\\n  line-height: 1.6;\\n  margin: 0;\\n}\\n\\n\\n\\n.pagination-loading[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  padding: var(--space-4) 0;\\n  margin-bottom: var(--space-4);\\n}\\n.pagination-loading[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  width: 24px;\\n  height: 24px;\\n  border: 2px solid #e2e8f0;\\n  border-top: 2px solid #bdf2bd;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n.message[_ngcontent-%COMP%] {\\n  margin-bottom: var(--space-4);\\n  display: flex;\\n  animation: _ngcontent-%COMP%_fadeInUp 0.3s ease-out;\\n  position: relative;\\n}\\n.message.user-message[_ngcontent-%COMP%] {\\n  justify-content: flex-end;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #d0ceff 0%, #e6e6ff 100%);\\n  color: #ffffff;\\n  max-width: 75%;\\n  border-radius: 20px 20px 6px 20px;\\n  box-shadow: 0 3px 12px rgba(79, 70, 229, 0.25);\\n  transition: all var(--transition-fast);\\n  position: relative;\\n  border: none;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 6px 20px rgba(79, 70, 229, 0.3);\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.15);\\n  color: #059669;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.05);\\n  border-color: rgba(0, 0, 0, 0.1);\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  color: #047857;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #059669;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  color: #047857;\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\n  border-left-color: #059669;\\n  background: rgba(0, 0, 0, 0.05);\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  border-color: rgba(0, 0, 0, 0.1);\\n}\\n.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.05);\\n}\\n.message.bot-message[_ngcontent-%COMP%] {\\n  justify-content: flex-start;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  \\n\\n}\\n.message.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\\n  color: var(--color-gray-800);\\n  max-width: 75%;\\n  border-radius: 20px 20px 20px 6px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);\\n  transition: all var(--transition-fast);\\n  position: relative;\\n  border: 1px solid var(--color-gray-200);\\n}\\n.message.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\\n  border-color: var(--color-gray-300);\\n  \\n\\n}\\n.message.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]:hover   .message-news[_ngcontent-%COMP%] {\\n  margin-top: var(--space-3);\\n}\\n.message.bot-message[_ngcontent-%COMP%]   .message.bot-message[_ngcontent-%COMP%] {\\n  flex-direction: column;\\n  align-items: flex-start;\\n}\\n.message.bot-message[_ngcontent-%COMP%]   .message-news[_ngcontent-%COMP%] {\\n  max-width: 80%;\\n  margin-left: 0;\\n  align-self: flex-start;\\n}\\n\\n.message-content[_ngcontent-%COMP%] {\\n  padding: var(--space-4) var(--space-5);\\n  word-wrap: break-word;\\n  position: relative;\\n}\\n\\n.message-text[_ngcontent-%COMP%] {\\n  font-size: var(--font-size-base);\\n  line-height: 1.6;\\n  margin: 0;\\n  font-weight: var(--font-weight-normal);\\n  word-break: break-word;\\n}\\n.message-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 var(--space-3) 0;\\n}\\n.message-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  margin: var(--space-4) 0 var(--space-2) 0;\\n  font-weight: var(--font-weight-semibold);\\n  line-height: 1.3;\\n}\\n.message-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  font-size: 1.5em;\\n}\\n.message-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  font-size: 1.3em;\\n}\\n.message-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  font-size: 1.2em;\\n}\\n.message-text[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 1.1em;\\n}\\n.message-text[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%] {\\n  font-size: 1em;\\n}\\n.message-text[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   b[_ngcontent-%COMP%] {\\n  font-weight: var(--font-weight-semibold);\\n}\\n.message-text[_ngcontent-%COMP%]   em[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-style: italic;\\n}\\n.message-text[_ngcontent-%COMP%]   del[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   strike[_ngcontent-%COMP%] {\\n  text-decoration: line-through;\\n}\\n.message-text[_ngcontent-%COMP%]   u[_ngcontent-%COMP%] {\\n  text-decoration: underline;\\n}\\n.message-text[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%] {\\n  margin: var(--space-3) 0;\\n  padding-left: var(--space-6);\\n}\\n.message-text[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]:last-child, .message-text[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin: var(--space-1) 0;\\n  line-height: 1.5;\\n}\\n.message-text[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%] {\\n  margin: var(--space-4) 0;\\n  padding: var(--space-3) var(--space-4);\\n  border-left: 4px solid #e5e7eb;\\n  background: rgba(0, 0, 0, 0.02);\\n  border-radius: 0 var(--radius-sm) var(--radius-sm) 0;\\n  font-style: italic;\\n}\\n.message-text[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background: rgba(0, 0, 0, 0.08);\\n  padding: 2px 6px;\\n  border-radius: var(--radius-sm);\\n  font-family: \\\"Monaco\\\", \\\"Menlo\\\", \\\"Ubuntu Mono\\\", monospace;\\n  font-size: 0.9em;\\n  color: #e11d48;\\n}\\n.message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  background: #f8fafc;\\n  border: 1px solid #e2e8f0;\\n  border-radius: var(--radius-md);\\n  padding: var(--space-4);\\n  margin: var(--space-4) 0;\\n  overflow-x: auto;\\n}\\n.message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background: none;\\n  padding: 0;\\n  color: #334155;\\n  font-size: 0.875em;\\n}\\n.message-text[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #2563eb;\\n  text-decoration: underline;\\n  transition: var(--transition-fast);\\n}\\n.message-text[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  color: #1d4ed8;\\n  text-decoration: none;\\n}\\n.message-text[_ngcontent-%COMP%]   table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-collapse: collapse;\\n  margin: var(--space-4) 0;\\n}\\n.message-text[_ngcontent-%COMP%]   table[_ngcontent-%COMP%]:first-child {\\n  margin-top: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   table[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.message-text[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: var(--space-2) var(--space-3);\\n  border: 1px solid #e2e8f0;\\n  text-align: left;\\n}\\n.message-text[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  background: #f8fafc;\\n  font-weight: var(--font-weight-semibold);\\n}\\n.message-text[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  max-width: 100%;\\n  height: auto;\\n  border-radius: var(--radius-sm);\\n  margin: var(--space-2) 0;\\n}\\n\\n.message-time[_ngcontent-%COMP%] {\\n  font-size: var(--font-size-xs);\\n  color: rgba(255, 255, 255, 0.7);\\n  margin-top: var(--space-2);\\n  text-align: right;\\n  opacity: 0.8;\\n  transition: var(--transition-fast);\\n  font-weight: var(--font-weight-normal);\\n}\\n\\n.bot-message[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%] {\\n  text-align: left;\\n  color: var(--color-gray-600);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%] {\\n  text-align: right;\\n  color: var(--color-gray-600);\\n}\\n\\n.message[_ngcontent-%COMP%]:hover   .message-time[_ngcontent-%COMP%] {\\n  opacity: 1;\\n}\\n\\n.input-container[_ngcontent-%COMP%] {\\n  padding: var(--space-6) var(--space-8) var(--space-8) var(--space-8);\\n  background: linear-gradient(to top, #ffffff 0%, #fefefe 100%);\\n  position: absolute; \\n\\n  bottom: 0;\\n  left: 0; \\n\\n  right: 0; \\n\\n  transform: none; \\n\\n  width: 100%;\\n  z-index: 50;\\n  border-top: 1px solid rgba(0, 0, 0, 0.06);\\n  transition: var(--transition-fast);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n.input-container.centered[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: auto;\\n  bottom: var(--space-8);\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: calc(100% - var(--space-16));\\n  max-width: 600px;\\n  background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);\\n  z-index: 50;\\n  padding: var(--space-8);\\n  border-radius: var(--radius-2xl);\\n  border: 1px solid rgba(0, 0, 0, 0.06);\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12), 0 8px 24px rgba(0, 0, 0, 0.08);\\n  border-top: none;\\n  -webkit-backdrop-filter: none;\\n          backdrop-filter: none;\\n}\\n.input-container.bottom[_ngcontent-%COMP%] {\\n  position: absolute; \\n\\n  bottom: 0;\\n  left: 0; \\n\\n  right: 0; \\n\\n  transform: none; \\n\\n  border-top: 1px solid rgba(0, 0, 0, 0.06);\\n  background: linear-gradient(to top, #ffffff 0%, #fefefe 100%);\\n  width: 100%;\\n  z-index: 50;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.input-wrapper[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-end;\\n  gap: var(--space-4);\\n  border: 2px solid var(--color-gray-200);\\n  border-radius: 28px;\\n  padding: var(--space-4) var(--space-5);\\n  background: var(--color-white);\\n  transition: all var(--transition-normal);\\n  position: relative;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);\\n}\\n.input-wrapper[_ngcontent-%COMP%]:focus-within {\\n  border-color: var(--color-primary);\\n  background: var(--color-white);\\n  box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.08), 0 4px 12px rgba(0, 0, 0, 0.08);\\n  transform: translateY(-1px);\\n}\\n.input-wrapper[_ngcontent-%COMP%]:hover {\\n  border-color: var(--color-gray-300);\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);\\n}\\n\\n.message-input[_ngcontent-%COMP%] {\\n  flex: 1;\\n  border: none;\\n  outline: none;\\n  background: transparent;\\n  resize: none;\\n  font-size: var(--font-size-base);\\n  line-height: 1.6;\\n  padding: var(--space-2) 0;\\n  min-height: 28px;\\n  max-height: 120px;\\n  font-family: var(--font-family-primary);\\n  overflow-y: auto;\\n  transition: var(--transition-fast);\\n  color: var(--color-gray-900);\\n  font-weight: var(--font-weight-normal);\\n  \\n\\n}\\n.message-input[_ngcontent-%COMP%]::placeholder {\\n  color: var(--color-gray-500);\\n  opacity: 1;\\n  font-weight: var(--font-weight-normal);\\n}\\n.message-input[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.message-input[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  width: 4px;\\n}\\n.message-input[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: transparent;\\n}\\n.message-input[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #cbd5e1;\\n  border-radius: var(--radius-sm);\\n}\\n.message-input[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover {\\n  background: #94a3b8;\\n}\\n\\n.send-button[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4f46e5 0%, #6366f1 100%);\\n  border: none;\\n  border-radius: 50%;\\n  width: 44px;\\n  height: 44px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: var(--color-white);\\n  cursor: pointer;\\n  flex-shrink: 0;\\n  transition: all var(--transition-normal);\\n  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);\\n  position: relative;\\n  overflow: hidden;\\n}\\n.send-button[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);\\n  border-radius: 50%;\\n  opacity: 0;\\n  transition: opacity var(--transition-fast);\\n}\\n.send-button[_ngcontent-%COMP%]:hover:not(:disabled) {\\n  transform: translateY(-2px) scale(1.05);\\n  box-shadow: 0 8px 20px rgba(79, 70, 229, 0.4);\\n}\\n.send-button[_ngcontent-%COMP%]:hover:not(:disabled)::before {\\n  opacity: 1;\\n}\\n.send-button[_ngcontent-%COMP%]:active:not(:disabled) {\\n  transform: translateY(-1px) scale(1.02);\\n  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);\\n}\\n.send-button[_ngcontent-%COMP%]:disabled {\\n  background: var(--color-gray-400);\\n  cursor: not-allowed;\\n  transform: none;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.send-button[_ngcontent-%COMP%]:disabled::before {\\n  display: none;\\n}\\n.send-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n  width: 20px;\\n  height: 20px;\\n  transition: var(--transition-fast);\\n  transform: translateX(1px); \\n\\n}\\n.send-button[_ngcontent-%COMP%]:hover:not(:disabled)   svg[_ngcontent-%COMP%] {\\n  transform: translateX(1px) scale(1.1);\\n}\\n\\n.typing-indicator[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: var(--space-1);\\n  padding: var(--space-1) 0;\\n}\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  background: #94a3b8;\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_typing 1.4s infinite ease-in-out;\\n}\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1) {\\n  animation-delay: 0s;\\n}\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2) {\\n  animation-delay: 0.2s;\\n}\\n.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(3) {\\n  animation-delay: 0.4s;\\n}\\n\\n\\n\\n.auth-loading[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  justify-content: center;\\n  align-items: center;\\n  height: 100vh;\\n  color: var(--color-gray-600);\\n  background: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-white) 50%, #f0f4f8 100%);\\n}\\n.auth-loading[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border: 4px solid var(--color-gray-200);\\n  border-top: 4px solid var(--color-accent);\\n  border-radius: 50%;\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n  margin-bottom: var(--space-4);\\n}\\n.auth-loading[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  font-size: var(--font-size-lg);\\n  color: var(--color-gray-600);\\n  margin: 0;\\n  font-weight: var(--font-weight-medium);\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_typing {\\n  0%, 60%, 100% {\\n    opacity: 0.3;\\n    transform: scale(0.7) translateY(0);\\n  }\\n  30% {\\n    opacity: 1;\\n    transform: scale(1.3) translateY(-6px);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_slideInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(40px) scale(0.9);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0) scale(1);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n\\n@media (max-width: 768px) {\\n  .chat-container[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n    height: 100vh;\\n    border-radius: 0;\\n    border: none;\\n  }\\n  .messages-container[_ngcontent-%COMP%] {\\n    padding: var(--space-5) var(--space-5) 140px var(--space-5);\\n  }\\n  .input-container[_ngcontent-%COMP%] {\\n    padding: var(--space-5) var(--space-5) var(--space-6) var(--space-5);\\n    max-width: 100%;\\n  }\\n  .input-container.centered[_ngcontent-%COMP%] {\\n    max-width: 100%;\\n    padding: var(--space-5);\\n    margin: 0 var(--space-4);\\n    width: calc(100% - var(--space-8));\\n  }\\n  .message[_ngcontent-%COMP%] {\\n    margin-bottom: var(--space-5);\\n  }\\n  .message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%], .message.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%] {\\n    max-width: 88%;\\n  }\\n  .input-wrapper[_ngcontent-%COMP%] {\\n    padding: var(--space-1) var(--space-2) var(--space-1) var(--space-4);\\n    gap: var(--space-2);\\n  }\\n  .send-button[_ngcontent-%COMP%] {\\n    width: 42px;\\n    height: 42px;\\n  }\\n  .send-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%] {\\n    width: 18px;\\n    height: 18px;\\n  }\\n}\\n\\n\\n*[_ngcontent-%COMP%] {\\n  transition: color var(--transition-fast), background-color var(--transition-fast), border-color var(--transition-fast), transform var(--transition-fast), box-shadow var(--transition-fast);\\n}\\n\\n\\n\\n.send-button[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid var(--color-primary);\\n  outline-offset: 3px;\\n}\\n\\n.message-input[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n}\\n\\n\\n\\n.message-content[_ngcontent-%COMP%]::selection {\\n  background: rgba(16, 185, 129, 0.2);\\n}\\n\\n.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]::selection {\\n  background: rgba(255, 255, 255, 0.3);\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeInUp {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_typing {\\n  0%, 60%, 100% {\\n    transform: translateY(0);\\n    opacity: 0.4;\\n  }\\n  30% {\\n    transform: translateY(-10px);\\n    opacity: 1;\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_spin {\\n  0% {\\n    transform: rotate(0deg);\\n  }\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["marked", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵproperty", "ɵɵpureFunction2", "_c1", "message_r9", "isUser", "ɵɵadvance", "ctx_r7", "formatMessageText", "text", "ɵɵsanitizeHtml", "ɵɵtextInterpolate", "ɵɵpipeBind2", "timestamp", "ɵɵlistener", "ChatComponent_div_1_div_1_Template_div_scroll_0_listener", "$event", "ɵɵrestoreView", "_r11", "ctx_r10", "ɵɵnextContext", "ɵɵresetView", "onScroll", "ɵɵtemplate", "ChatComponent_div_1_div_1_div_2_Template", "ChatComponent_div_1_div_1_div_3_Template", "ChatComponent_div_1_div_1_div_4_Template", "ctx_r2", "isLoadingHistory", "messages", "isLoading", "ɵɵnamespaceSVG", "ɵɵnamespaceHTML", "ChatComponent_div_1_div_1_Template", "ChatComponent_div_1_div_2_Template", "ChatComponent_div_1_Template_textarea_ngModelChange_5_listener", "_r13", "ctx_r12", "currentMessage", "ChatComponent_div_1_Template_textarea_keydown_5_listener", "ctx_r14", "onKeyPress", "ChatComponent_div_1_Template_textarea_input_5_listener", "ctx_r15", "adjustTextareaHeight", "ChatComponent_div_1_Template_button_click_8_listener", "ctx_r16", "sendMessage", "ctx_r0", "length", "ɵɵclassProp", "trim", "ChatComponent", "constructor", "authService", "isAuthenticated", "currentSessionId", "currentPage", "hasNextPage", "shouldScrollToBottom", "lastScrollHeight", "initialLoadComplete", "userHasScrolled", "ngOnInit", "ensureAuthenticated", "subscribe", "next", "token", "console", "log", "loadChatHistory", "error", "ngAfterViewChecked", "scrollToBottom", "event", "element", "target", "scrollTop", "scrollHeight", "clientHeight", "scrollTimeout", "clearTimeout", "setTimeout", "isNearTop", "isNotAtBottom", "loadMoreHistory", "page", "append", "undefined", "response", "newMessages", "results", "map", "msg", "convertChatMessageToMessage", "reversedNewMessages", "reverse", "maintainScrollPosition", "chatMessage", "id", "message", "sender", "Date", "session", "prompt", "model", "news_articles", "messagesContainer", "nativeElement", "newScrollHeight", "scrollDifference", "messageToSend", "tempUserMessage", "push", "sendMessageToChatbot", "userMessage", "user_message", "botMessage", "bot_message", "lastMessageIndex", "errorMessage", "clearHistory", "refreshHistory", "key", "shift<PERSON>ey", "preventDefault", "textarea", "style", "height", "Math", "min", "setOptions", "breaks", "gfm", "htmlContent", "parse", "sanitizedHtml", "replace", "ngOnDestroy", "_", "ɵɵdirectiveInject", "i1", "AuthService", "_2", "selectors", "viewQuery", "ChatComponent_Query", "rf", "ctx", "ChatComponent_div_1_Template", "ChatComponent_div_2_Template"], "sources": ["C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\news_separate\\frontend\\src\\app\\chat\\chat.component.ts", "C:\\Users\\<USER>\\PycharmProjects\\GenAI\\Borys-Slavic-News-Chatbot\\news_separate\\frontend\\src\\app\\chat\\chat.component.html"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ViewChild, AfterViewChecked } from '@angular/core';\nimport { AuthService } from '../auth.service'; // Adjust path as needed\nimport { marked } from 'marked';\n\ninterface NewsArticle {\n  id?: number;\n  country: string;\n  source: string;\n  title: string;\n  link: string;\n  published: string;\n  description: string;\n  fetched_at?: string;\n  created_at?: string;\n}\n\ninterface Message {\n  id?: number;\n  text: string;\n  isUser: boolean;\n  timestamp: Date;\n  session?: number;\n  prompt?: number;\n  model?: number;\n  news_articles?: NewsArticle[]; // for bot messages that include news\n}\n\n// Django ChatMessage structure\ninterface ChatMessage {\n  id: number;\n  session: number;\n  sender: 'user' | 'bot';\n  message: string;\n  timestamp: string;\n  prompt: number | null;\n  model: number | null;\n  news_articles?: NewsArticle[] | null;\n}\n\n// Django paginated response\ninterface ChatHistoryResponse {\n  count: number;\n  next: string | null;\n  previous: string | null;\n  results: ChatMessage[];\n}\n\n// Django chatbot response\ninterface ChatbotResponse {\n  user_message: ChatMessage;\n  bot_message: ChatMessage;\n}\n\n@Component({\n  selector: 'app-chat',\n  templateUrl: './chat.component.html',\n  styleUrls: ['./chat.component.scss']\n})\nexport class ChatComponent implements OnInit, OnDestroy, AfterViewChecked {\n  @ViewChild('messagesContainer') private messagesContainer!: ElementRef;\n  \n  messages: Message[] = [];\n  currentMessage: string = '';\n  isLoading: boolean = false;\n  isAuthenticated: boolean = false;\n  currentSessionId: number | null = null;\n  \n  // Pagination\n  currentPage: number = 1;\n  hasNextPage: boolean = false;\n  isLoadingHistory: boolean = false;\n  \n  // Scroll management\n  private shouldScrollToBottom: boolean = true;\n  private lastScrollHeight: number = 0;\n  private initialLoadComplete: boolean = false;\n  private userHasScrolled: boolean = false;\n  private scrollTimeout: any;\n\n  constructor(private authService: AuthService) {}\n\n  ngOnInit() {\n    // Ensure user is authenticated before initializing chat\n    this.authService.ensureAuthenticated().subscribe({\n      next: (token) => {\n        console.log('User authenticated successfully');\n        this.isAuthenticated = true;\n        this.loadChatHistory(1, false);\n      },\n      error: (error) => {\n        console.error('Authentication failed:', error);\n        this.isAuthenticated = false;\n      }\n    });\n  }\n\n  ngAfterViewChecked() {\n    // Auto-scroll to bottom only for new messages\n    if (this.shouldScrollToBottom) {\n      this.scrollToBottom();\n      this.shouldScrollToBottom = false;\n    }\n  }\n\n  // Listen for scroll events to load more history\n  onScroll(event: any) {\n    const element = event.target;\n    const scrollTop = element.scrollTop;\n    const scrollHeight = element.scrollHeight;\n    const clientHeight = element.clientHeight;\n    \n    // Mark that user has scrolled manually (not programmatic)\n    if (this.initialLoadComplete) {\n      this.userHasScrolled = true;\n    }\n    \n    // Clear existing timeout\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n    \n    // Debounce scroll events and check conditions\n    this.scrollTimeout = setTimeout(() => {\n      // Only load more if:\n      // 1. Initial load is complete\n      // 2. User has scrolled manually at least once\n      // 3. User is near the top (scrollTop < 100)\n      // 4. There are more pages to load\n      // 5. Not currently loading\n      // 6. User is not at the very bottom (to avoid conflicts with auto-scroll)\n      const isNearTop = scrollTop < 100;\n      const isNotAtBottom = scrollTop < (scrollHeight - clientHeight - 50);\n      \n      if (isNearTop && \n          isNotAtBottom &&\n          this.hasNextPage && \n          !this.isLoadingHistory && \n          this.initialLoadComplete &&\n          this.userHasScrolled) {\n        this.lastScrollHeight = scrollHeight;\n        this.loadMoreHistory();\n      }\n    }, 100); // 100ms debounce\n  }\n\n  loadChatHistory(page: number = 1, append: boolean = false) {\n    if (!this.isAuthenticated) return;\n\n    this.isLoadingHistory = true;\n\n    this.authService.loadChatHistory(page, this.currentSessionId || undefined).subscribe({\n      next: (response: ChatHistoryResponse) => {\n        const newMessages = response.results.map(msg => this.convertChatMessageToMessage(msg));\n        \n        if (append) {\n          // For pagination - reverse the new messages (since API returns newest first)\n          // and prepend older messages to beginning\n          const reversedNewMessages = [...newMessages].reverse();\n          this.messages = [...reversedNewMessages, ...this.messages];\n          this.maintainScrollPosition();\n        } else {\n          // For initial load - reverse messages to get chronological order (oldest first)\n          this.messages = [...newMessages].reverse();\n          this.shouldScrollToBottom = true;\n          \n          // Set initial load complete after scroll positioning is done\n          setTimeout(() => {\n            this.initialLoadComplete = true;\n          }, 200);\n        }\n        \n        // Update pagination info\n        this.currentPage = page;\n        this.hasNextPage = !!response.next;\n        \n        this.isLoadingHistory = false;\n      },\n      error: (error) => {\n        console.error('Error loading chat history:', error);\n        this.isLoadingHistory = false;\n        \n        // If this was the initial load, still mark it as complete after delay\n        if (!append) {\n          setTimeout(() => {\n            this.initialLoadComplete = true;\n          }, 200);\n        }\n      }\n    });\n  }\n\n  // Convert Django ChatMessage to frontend Message format\n  private convertChatMessageToMessage(chatMessage: ChatMessage): Message {\n    return {\n      id: chatMessage.id,\n      text: chatMessage.message,\n      isUser: chatMessage.sender === 'user',\n      timestamp: new Date(chatMessage.timestamp),\n      session: chatMessage.session,\n      prompt: chatMessage.prompt || undefined,\n      model: chatMessage.model || undefined,\n      news_articles: chatMessage.news_articles || undefined,\n    };\n  }\n\n  // Load more chat history (pagination) - triggered by scroll\n  loadMoreHistory() {\n    if (this.hasNextPage && !this.isLoadingHistory) {\n      this.loadChatHistory(this.currentPage + 1, true);\n    }\n  }\n\n  // Maintain scroll position when loading older messages\n  private maintainScrollPosition() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        const newScrollHeight = element.scrollHeight;\n        const scrollDifference = newScrollHeight - this.lastScrollHeight;\n        element.scrollTop = scrollDifference;\n      }\n    }, 50);\n  }\n\n  sendMessage() {\n    if (!this.currentMessage.trim() || this.isLoading || !this.isAuthenticated) {\n      return;\n    }\n\n    // Store the message to send\n    const messageToSend = this.currentMessage;\n    this.currentMessage = '';\n\n    // Create temporary user message and add it instantly\n    const tempUserMessage: Message = {\n      text: messageToSend,\n      isUser: true,\n      timestamp: new Date()\n    };\n\n    // Add user message instantly to the chat\n    this.messages.push(tempUserMessage);\n    this.shouldScrollToBottom = true;\n\n    // Set loading state\n    this.isLoading = true;\n\n    // Call the API through auth service\n    this.authService.sendMessageToChatbot(messageToSend, this.currentSessionId || undefined).subscribe({\n      next: (response: ChatbotResponse) => {\n        // Convert backend messages\n        const userMessage = this.convertChatMessageToMessage(response.user_message);\n        const botMessage = this.convertChatMessageToMessage(response.bot_message);\n\n        // Replace the temporary user message with the one from backend\n        const lastMessageIndex = this.messages.length - 1;\n        if (lastMessageIndex >= 0 && this.messages[lastMessageIndex].isUser) {\n          this.messages[lastMessageIndex] = userMessage;\n        }\n\n        // Add bot message\n        this.messages.push(botMessage);\n\n        // Store session ID for future requests\n        if (!this.currentSessionId) {\n          this.currentSessionId = response.user_message.session;\n        }\n\n        this.isLoading = false;\n        this.shouldScrollToBottom = true;\n      },\n      error: (error) => {\n        console.error('Error sending message:', error);\n        const errorMessage: Message = {\n          text: 'Sorry, there was an error processing your message. Please try again.',\n          isUser: false,\n          timestamp: new Date()\n        };\n        this.messages.push(errorMessage);\n        this.isLoading = false;\n        this.shouldScrollToBottom = true;\n      }\n    });\n  }\n\n  clearHistory() {\n    this.messages = [];\n    this.currentPage = 1;\n    this.hasNextPage = false;\n    this.initialLoadComplete = false;\n    this.userHasScrolled = false;\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n  }\n\n  refreshHistory() {\n    this.currentPage = 1;\n    this.initialLoadComplete = false;\n    this.userHasScrolled = false;\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n    this.loadChatHistory();\n  }\n\n  onKeyPress(event: KeyboardEvent) {\n    if (event.key === 'Enter' && !event.shiftKey) {\n      event.preventDefault();\n      this.sendMessage();\n    }\n  }\n\n  adjustTextareaHeight(event: any) {\n    const textarea = event.target;\n    textarea.style.height = 'auto';\n    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';\n  }\n\n  private scrollToBottom() {\n    setTimeout(() => {\n      if (this.messagesContainer) {\n        const element = this.messagesContainer.nativeElement;\n        element.scrollTop = element.scrollHeight;\n      }\n    }, 50);\n  }\n\n  // Format message text using marked library for markdown\n  formatMessageText(text: string): string {\n    if (!text) return '';\n\n    try {\n      // Configure marked to be more restrictive for security\n      marked.setOptions({\n        breaks: true, // Convert line breaks to <br>\n        gfm: true // Enable GitHub Flavored Markdown\n      });\n\n      // Convert markdown to HTML using marked (synchronous)\n      const htmlContent = marked.parse(text) as string;\n\n      // Basic sanitization - remove script tags and dangerous attributes\n      let sanitizedHtml = htmlContent\n        .replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, '')\n        .replace(/javascript:/gi, '')\n        .replace(/on\\w+\\s*=/gi, '');\n\n      return sanitizedHtml;\n    } catch (error) {\n      console.error('Error formatting message text:', error);\n      // Fallback to plain text with basic line break conversion\n      return text.replace(/\\n/g, '<br>');\n    }\n  }\n\n  ngOnDestroy() {\n    // Clean up any pending timeouts\n    if (this.scrollTimeout) {\n      clearTimeout(this.scrollTimeout);\n    }\n  }\n}", "<!-- chat.component.html -->\n\n<!-- Anonymous user component runs in background -->\n<app-anonymous-user></app-anonymous-user>\n\n<div class=\"chat-container\" *ngIf=\"isAuthenticated\">\n  <!-- Messages container - only show when there are messages -->\n  <div\n    #messagesContainer\n    class=\"messages-container\"\n    (scroll)=\"onScroll($event)\"\n    *ngIf=\"messages.length > 0\">\n\n    <!-- Loading indicator for pagination at the top -->\n    <div class=\"pagination-loading\" *ngIf=\"isLoadingHistory\">\n      <div class=\"loading-spinner\"></div>\n    </div>\n\n    <div class=\"message\"\n         *ngFor=\"let message of messages\"\n         [ngClass]=\"{'user-message': message.isUser, 'bot-message': !message.isUser}\">\n      <div class=\"message-content\">\n        <div class=\"message-text\" [innerHTML]=\"formatMessageText(message.text)\"></div>\n        <div class=\"message-time\">{{ message.timestamp | date:'short' }}</div>\n      </div>\n\n    </div>\n\n    <!-- Loading indicator for current message -->\n    <div class=\"message bot-message\" *ngIf=\"isLoading\">\n      <div class=\"message-content\">\n        <div class=\"typing-indicator\">\n          <span></span>\n          <span></span>\n          <span></span>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Welcome message when no messages exist -->\n  <div class=\"welcome-container\" *ngIf=\"messages.length === 0 && !isLoading\">\n    <div class=\"welcome-content\">\n      <div class=\"welcome-icon\">\n        <svg width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n          <path d=\"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z\" fill=\"currentColor\"/>\n        </svg>\n      </div>\n      <h2>Welcome to News Agent</h2>\n      <p>Ask me anything about current news, events, or any topic you're curious about!</p>\n    </div>\n  </div>\n\n  <!-- Input container - always visible -->\n  <div class=\"input-container\" [class.centered]=\"messages.length === 0\" [class.bottom]=\"messages.length > 0\">\n    <div class=\"input-wrapper\">\n      <textarea\n        #messageTextarea\n        [(ngModel)]=\"currentMessage\"\n        (keydown)=\"onKeyPress($event)\"\n        (input)=\"adjustTextareaHeight($event)\"\n        placeholder=\"Ask anything\"\n        class=\"message-input\"\n        rows=\"1\">\n      </textarea>\n      <button\n        (click)=\"sendMessage()\"\n        class=\"send-button\"\n        [disabled]=\"!currentMessage.trim() || isLoading\">\n        <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n          <path d=\"M2 21L23 12L2 3V10L17 12L2 14V21Z\" fill=\"currentColor\"/>\n        </svg>\n      </button>\n    </div>\n  </div>\n</div>\n\n<!-- Show loading message while authenticating -->\n<div *ngIf=\"!isAuthenticated\" class=\"auth-loading\">\n  <div class=\"loading-spinner\"></div>\n  <p>Initializing chat...</p>\n</div>"], "mappings": "AAEA,SAASA,MAAM,QAAQ,QAAQ;;;;;;;;;ICY3BC,EAAA,CAAAC,cAAA,cAAyD;IACvDD,EAAA,CAAAE,SAAA,cAAmC;IACrCF,EAAA,CAAAG,YAAA,EAAM;;;;;;;;;;;IAENH,EAAA,CAAAC,cAAA,cAEkF;IAE9ED,EAAA,CAAAE,SAAA,cAA8E;IAC9EF,EAAA,CAAAC,cAAA,cAA0B;IAAAD,EAAA,CAAAI,MAAA,GAAsC;;IAAAJ,EAAA,CAAAG,YAAA,EAAM;;;;;IAHrEH,EAAA,CAAAK,UAAA,YAAAL,EAAA,CAAAM,eAAA,IAAAC,GAAA,EAAAC,UAAA,CAAAC,MAAA,GAAAD,UAAA,CAAAC,MAAA,EAA4E;IAEnDT,EAAA,CAAAU,SAAA,GAA6C;IAA7CV,EAAA,CAAAK,UAAA,cAAAM,MAAA,CAAAC,iBAAA,CAAAJ,UAAA,CAAAK,IAAA,GAAAb,EAAA,CAAAc,cAAA,CAA6C;IAC7Cd,EAAA,CAAAU,SAAA,GAAsC;IAAtCV,EAAA,CAAAe,iBAAA,CAAAf,EAAA,CAAAgB,WAAA,OAAAR,UAAA,CAAAS,SAAA,WAAsC;;;;;IAMpEjB,EAAA,CAAAC,cAAA,cAAmD;IAG7CD,EAAA,CAAAE,SAAA,WAAa;IAGfF,EAAA,CAAAG,YAAA,EAAM;;;;;;IA5BZH,EAAA,CAAAC,cAAA,kBAI8B;IAD5BD,EAAA,CAAAkB,UAAA,oBAAAC,yDAAAC,MAAA;MAAApB,EAAA,CAAAqB,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAvB,EAAA,CAAAwB,aAAA;MAAA,OAAUxB,EAAA,CAAAyB,WAAA,CAAAF,OAAA,CAAAG,QAAA,CAAAN,MAAA,CAAgB;IAAA,EAAC;IAI3BpB,EAAA,CAAA2B,UAAA,IAAAC,wCAAA,kBAEM;IAEN5B,EAAA,CAAA2B,UAAA,IAAAE,wCAAA,kBAQM;IAGN7B,EAAA,CAAA2B,UAAA,IAAAG,wCAAA,kBAQM;IACR9B,EAAA,CAAAG,YAAA,EAAM;;;;IAxB6BH,EAAA,CAAAU,SAAA,GAAsB;IAAtBV,EAAA,CAAAK,UAAA,SAAA0B,MAAA,CAAAC,gBAAA,CAAsB;IAK9BhC,EAAA,CAAAU,SAAA,GAAW;IAAXV,EAAA,CAAAK,UAAA,YAAA0B,MAAA,CAAAE,QAAA,CAAW;IAUFjC,EAAA,CAAAU,SAAA,GAAe;IAAfV,EAAA,CAAAK,UAAA,SAAA0B,MAAA,CAAAG,SAAA,CAAe;;;;;IAYnDlC,EAAA,CAAAC,cAAA,cAA2E;IAGrED,EAAA,CAAAmC,cAAA,EAA+F;IAA/FnC,EAAA,CAAAC,cAAA,cAA+F;IAC7FD,EAAA,CAAAE,SAAA,eAAqJ;IACvJF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAoC,eAAA,EAAI;IAAJpC,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,4BAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC9BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAI,MAAA,qFAA8E;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;;;;IA5C3FH,EAAA,CAAAC,cAAA,aAAoD;IAElDD,EAAA,CAAA2B,UAAA,IAAAU,kCAAA,iBA+BM;IAGNrC,EAAA,CAAA2B,UAAA,IAAAW,kCAAA,iBAUM;IAGNtC,EAAA,CAAAC,cAAA,aAA2G;IAIrGD,EAAA,CAAAkB,UAAA,2BAAAqB,+DAAAnB,MAAA;MAAApB,EAAA,CAAAqB,aAAA,CAAAmB,IAAA;MAAA,MAAAC,OAAA,GAAAzC,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAAAgB,OAAA,CAAAC,cAAA,GAAAtB,MAAA;IAAA,EAA4B,qBAAAuB,yDAAAvB,MAAA;MAAApB,EAAA,CAAAqB,aAAA,CAAAmB,IAAA;MAAA,MAAAI,OAAA,GAAA5C,EAAA,CAAAwB,aAAA;MAAA,OACjBxB,EAAA,CAAAyB,WAAA,CAAAmB,OAAA,CAAAC,UAAA,CAAAzB,MAAA,CAAkB;IAAA,EADD,mBAAA0B,uDAAA1B,MAAA;MAAApB,EAAA,CAAAqB,aAAA,CAAAmB,IAAA;MAAA,MAAAO,OAAA,GAAA/C,EAAA,CAAAwB,aAAA;MAAA,OAEnBxB,EAAA,CAAAyB,WAAA,CAAAsB,OAAA,CAAAC,oBAAA,CAAA5B,MAAA,CAA4B;IAAA,EAFT;IAM9BpB,EAAA,CAAAI,MAAA;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IACXH,EAAA,CAAAC,cAAA,gBAGmD;IAFjDD,EAAA,CAAAkB,UAAA,mBAAA+B,qDAAA;MAAAjD,EAAA,CAAAqB,aAAA,CAAAmB,IAAA;MAAA,MAAAU,OAAA,GAAAlD,EAAA,CAAAwB,aAAA;MAAA,OAASxB,EAAA,CAAAyB,WAAA,CAAAyB,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IAGvBnD,EAAA,CAAAmC,cAAA,EAA+F;IAA/FnC,EAAA,CAAAC,cAAA,cAA+F;IAC7FD,EAAA,CAAAE,SAAA,gBAAiE;IACnEF,EAAA,CAAAG,YAAA,EAAM;;;;IA5DTH,EAAA,CAAAU,SAAA,GAAyB;IAAzBV,EAAA,CAAAK,UAAA,SAAA+C,MAAA,CAAAnB,QAAA,CAAAoB,MAAA,KAAyB;IA8BIrD,EAAA,CAAAU,SAAA,GAAyC;IAAzCV,EAAA,CAAAK,UAAA,SAAA+C,MAAA,CAAAnB,QAAA,CAAAoB,MAAA,WAAAD,MAAA,CAAAlB,SAAA,CAAyC;IAa5ClC,EAAA,CAAAU,SAAA,GAAwC;IAAxCV,EAAA,CAAAsD,WAAA,aAAAF,MAAA,CAAAnB,QAAA,CAAAoB,MAAA,OAAwC,WAAAD,MAAA,CAAAnB,QAAA,CAAAoB,MAAA;IAI/DrD,EAAA,CAAAU,SAAA,GAA4B;IAA5BV,EAAA,CAAAK,UAAA,YAAA+C,MAAA,CAAAV,cAAA,CAA4B;IAU5B1C,EAAA,CAAAU,SAAA,GAAgD;IAAhDV,EAAA,CAAAK,UAAA,cAAA+C,MAAA,CAAAV,cAAA,CAAAa,IAAA,MAAAH,MAAA,CAAAlB,SAAA,CAAgD;;;;;IAUxDlC,EAAA,CAAAC,cAAA,cAAmD;IACjDD,EAAA,CAAAE,SAAA,cAAmC;IACnCF,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAI,MAAA,2BAAoB;IAAAJ,EAAA,CAAAG,YAAA,EAAI;;;ADtB7B,OAAM,MAAOqD,aAAa;EAqBxBC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAlB/B,KAAAzB,QAAQ,GAAc,EAAE;IACxB,KAAAS,cAAc,GAAW,EAAE;IAC3B,KAAAR,SAAS,GAAY,KAAK;IAC1B,KAAAyB,eAAe,GAAY,KAAK;IAChC,KAAAC,gBAAgB,GAAkB,IAAI;IAEtC;IACA,KAAAC,WAAW,GAAW,CAAC;IACvB,KAAAC,WAAW,GAAY,KAAK;IAC5B,KAAA9B,gBAAgB,GAAY,KAAK;IAEjC;IACQ,KAAA+B,oBAAoB,GAAY,IAAI;IACpC,KAAAC,gBAAgB,GAAW,CAAC;IAC5B,KAAAC,mBAAmB,GAAY,KAAK;IACpC,KAAAC,eAAe,GAAY,KAAK;EAGO;EAE/CC,QAAQA,CAAA;IACN;IACA,IAAI,CAACT,WAAW,CAACU,mBAAmB,EAAE,CAACC,SAAS,CAAC;MAC/CC,IAAI,EAAGC,KAAK,IAAI;QACdC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C,IAAI,CAACd,eAAe,GAAG,IAAI;QAC3B,IAAI,CAACe,eAAe,CAAC,CAAC,EAAE,KAAK,CAAC;MAChC,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfH,OAAO,CAACG,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,IAAI,CAAChB,eAAe,GAAG,KAAK;MAC9B;KACD,CAAC;EACJ;EAEAiB,kBAAkBA,CAAA;IAChB;IACA,IAAI,IAAI,CAACb,oBAAoB,EAAE;MAC7B,IAAI,CAACc,cAAc,EAAE;MACrB,IAAI,CAACd,oBAAoB,GAAG,KAAK;;EAErC;EAEA;EACArC,QAAQA,CAACoD,KAAU;IACjB,MAAMC,OAAO,GAAGD,KAAK,CAACE,MAAM;IAC5B,MAAMC,SAAS,GAAGF,OAAO,CAACE,SAAS;IACnC,MAAMC,YAAY,GAAGH,OAAO,CAACG,YAAY;IACzC,MAAMC,YAAY,GAAGJ,OAAO,CAACI,YAAY;IAEzC;IACA,IAAI,IAAI,CAAClB,mBAAmB,EAAE;MAC5B,IAAI,CAACC,eAAe,GAAG,IAAI;;IAG7B;IACA,IAAI,IAAI,CAACkB,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;IAGlC;IACA,IAAI,CAACA,aAAa,GAAGE,UAAU,CAAC,MAAK;MACnC;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAMC,SAAS,GAAGN,SAAS,GAAG,GAAG;MACjC,MAAMO,aAAa,GAAGP,SAAS,GAAIC,YAAY,GAAGC,YAAY,GAAG,EAAG;MAEpE,IAAII,SAAS,IACTC,aAAa,IACb,IAAI,CAAC1B,WAAW,IAChB,CAAC,IAAI,CAAC9B,gBAAgB,IACtB,IAAI,CAACiC,mBAAmB,IACxB,IAAI,CAACC,eAAe,EAAE;QACxB,IAAI,CAACF,gBAAgB,GAAGkB,YAAY;QACpC,IAAI,CAACO,eAAe,EAAE;;IAE1B,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;EACX;;EAEAf,eAAeA,CAACgB,IAAA,GAAe,CAAC,EAAEC,MAAA,GAAkB,KAAK;IACvD,IAAI,CAAC,IAAI,CAAChC,eAAe,EAAE;IAE3B,IAAI,CAAC3B,gBAAgB,GAAG,IAAI;IAE5B,IAAI,CAAC0B,WAAW,CAACgB,eAAe,CAACgB,IAAI,EAAE,IAAI,CAAC9B,gBAAgB,IAAIgC,SAAS,CAAC,CAACvB,SAAS,CAAC;MACnFC,IAAI,EAAGuB,QAA6B,IAAI;QACtC,MAAMC,WAAW,GAAGD,QAAQ,CAACE,OAAO,CAACC,GAAG,CAACC,GAAG,IAAI,IAAI,CAACC,2BAA2B,CAACD,GAAG,CAAC,CAAC;QAEtF,IAAIN,MAAM,EAAE;UACV;UACA;UACA,MAAMQ,mBAAmB,GAAG,CAAC,GAAGL,WAAW,CAAC,CAACM,OAAO,EAAE;UACtD,IAAI,CAACnE,QAAQ,GAAG,CAAC,GAAGkE,mBAAmB,EAAE,GAAG,IAAI,CAAClE,QAAQ,CAAC;UAC1D,IAAI,CAACoE,sBAAsB,EAAE;SAC9B,MAAM;UACL;UACA,IAAI,CAACpE,QAAQ,GAAG,CAAC,GAAG6D,WAAW,CAAC,CAACM,OAAO,EAAE;UAC1C,IAAI,CAACrC,oBAAoB,GAAG,IAAI;UAEhC;UACAuB,UAAU,CAAC,MAAK;YACd,IAAI,CAACrB,mBAAmB,GAAG,IAAI;UACjC,CAAC,EAAE,GAAG,CAAC;;QAGT;QACA,IAAI,CAACJ,WAAW,GAAG6B,IAAI;QACvB,IAAI,CAAC5B,WAAW,GAAG,CAAC,CAAC+B,QAAQ,CAACvB,IAAI;QAElC,IAAI,CAACtC,gBAAgB,GAAG,KAAK;MAC/B,CAAC;MACD2C,KAAK,EAAGA,KAAK,IAAI;QACfH,OAAO,CAACG,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACnD,IAAI,CAAC3C,gBAAgB,GAAG,KAAK;QAE7B;QACA,IAAI,CAAC2D,MAAM,EAAE;UACXL,UAAU,CAAC,MAAK;YACd,IAAI,CAACrB,mBAAmB,GAAG,IAAI;UACjC,CAAC,EAAE,GAAG,CAAC;;MAEX;KACD,CAAC;EACJ;EAEA;EACQiC,2BAA2BA,CAACI,WAAwB;IAC1D,OAAO;MACLC,EAAE,EAAED,WAAW,CAACC,EAAE;MAClB1F,IAAI,EAAEyF,WAAW,CAACE,OAAO;MACzB/F,MAAM,EAAE6F,WAAW,CAACG,MAAM,KAAK,MAAM;MACrCxF,SAAS,EAAE,IAAIyF,IAAI,CAACJ,WAAW,CAACrF,SAAS,CAAC;MAC1C0F,OAAO,EAAEL,WAAW,CAACK,OAAO;MAC5BC,MAAM,EAAEN,WAAW,CAACM,MAAM,IAAIhB,SAAS;MACvCiB,KAAK,EAAEP,WAAW,CAACO,KAAK,IAAIjB,SAAS;MACrCkB,aAAa,EAAER,WAAW,CAACQ,aAAa,IAAIlB;KAC7C;EACH;EAEA;EACAH,eAAeA,CAAA;IACb,IAAI,IAAI,CAAC3B,WAAW,IAAI,CAAC,IAAI,CAAC9B,gBAAgB,EAAE;MAC9C,IAAI,CAAC0C,eAAe,CAAC,IAAI,CAACb,WAAW,GAAG,CAAC,EAAE,IAAI,CAAC;;EAEpD;EAEA;EACQwC,sBAAsBA,CAAA;IAC5Bf,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACyB,iBAAiB,EAAE;QAC1B,MAAMhC,OAAO,GAAG,IAAI,CAACgC,iBAAiB,CAACC,aAAa;QACpD,MAAMC,eAAe,GAAGlC,OAAO,CAACG,YAAY;QAC5C,MAAMgC,gBAAgB,GAAGD,eAAe,GAAG,IAAI,CAACjD,gBAAgB;QAChEe,OAAO,CAACE,SAAS,GAAGiC,gBAAgB;;IAExC,CAAC,EAAE,EAAE,CAAC;EACR;EAEA/D,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACT,cAAc,CAACa,IAAI,EAAE,IAAI,IAAI,CAACrB,SAAS,IAAI,CAAC,IAAI,CAACyB,eAAe,EAAE;MAC1E;;IAGF;IACA,MAAMwD,aAAa,GAAG,IAAI,CAACzE,cAAc;IACzC,IAAI,CAACA,cAAc,GAAG,EAAE;IAExB;IACA,MAAM0E,eAAe,GAAY;MAC/BvG,IAAI,EAAEsG,aAAa;MACnB1G,MAAM,EAAE,IAAI;MACZQ,SAAS,EAAE,IAAIyF,IAAI;KACpB;IAED;IACA,IAAI,CAACzE,QAAQ,CAACoF,IAAI,CAACD,eAAe,CAAC;IACnC,IAAI,CAACrD,oBAAoB,GAAG,IAAI;IAEhC;IACA,IAAI,CAAC7B,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACwB,WAAW,CAAC4D,oBAAoB,CAACH,aAAa,EAAE,IAAI,CAACvD,gBAAgB,IAAIgC,SAAS,CAAC,CAACvB,SAAS,CAAC;MACjGC,IAAI,EAAGuB,QAAyB,IAAI;QAClC;QACA,MAAM0B,WAAW,GAAG,IAAI,CAACrB,2BAA2B,CAACL,QAAQ,CAAC2B,YAAY,CAAC;QAC3E,MAAMC,UAAU,GAAG,IAAI,CAACvB,2BAA2B,CAACL,QAAQ,CAAC6B,WAAW,CAAC;QAEzE;QACA,MAAMC,gBAAgB,GAAG,IAAI,CAAC1F,QAAQ,CAACoB,MAAM,GAAG,CAAC;QACjD,IAAIsE,gBAAgB,IAAI,CAAC,IAAI,IAAI,CAAC1F,QAAQ,CAAC0F,gBAAgB,CAAC,CAAClH,MAAM,EAAE;UACnE,IAAI,CAACwB,QAAQ,CAAC0F,gBAAgB,CAAC,GAAGJ,WAAW;;QAG/C;QACA,IAAI,CAACtF,QAAQ,CAACoF,IAAI,CAACI,UAAU,CAAC;QAE9B;QACA,IAAI,CAAC,IAAI,CAAC7D,gBAAgB,EAAE;UAC1B,IAAI,CAACA,gBAAgB,GAAGiC,QAAQ,CAAC2B,YAAY,CAACb,OAAO;;QAGvD,IAAI,CAACzE,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC6B,oBAAoB,GAAG,IAAI;MAClC,CAAC;MACDY,KAAK,EAAGA,KAAK,IAAI;QACfH,OAAO,CAACG,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,MAAMiD,YAAY,GAAY;UAC5B/G,IAAI,EAAE,sEAAsE;UAC5EJ,MAAM,EAAE,KAAK;UACbQ,SAAS,EAAE,IAAIyF,IAAI;SACpB;QACD,IAAI,CAACzE,QAAQ,CAACoF,IAAI,CAACO,YAAY,CAAC;QAChC,IAAI,CAAC1F,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC6B,oBAAoB,GAAG,IAAI;MAClC;KACD,CAAC;EACJ;EAEA8D,YAAYA,CAAA;IACV,IAAI,CAAC5F,QAAQ,GAAG,EAAE;IAClB,IAAI,CAAC4B,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACG,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,IAAI,CAACkB,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;EAEpC;EAEA0C,cAAcA,CAAA;IACZ,IAAI,CAACjE,WAAW,GAAG,CAAC;IACpB,IAAI,CAACI,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,IAAI,CAACkB,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;IAElC,IAAI,CAACV,eAAe,EAAE;EACxB;EAEA7B,UAAUA,CAACiC,KAAoB;IAC7B,IAAIA,KAAK,CAACiD,GAAG,KAAK,OAAO,IAAI,CAACjD,KAAK,CAACkD,QAAQ,EAAE;MAC5ClD,KAAK,CAACmD,cAAc,EAAE;MACtB,IAAI,CAAC9E,WAAW,EAAE;;EAEtB;EAEAH,oBAAoBA,CAAC8B,KAAU;IAC7B,MAAMoD,QAAQ,GAAGpD,KAAK,CAACE,MAAM;IAC7BkD,QAAQ,CAACC,KAAK,CAACC,MAAM,GAAG,MAAM;IAC9BF,QAAQ,CAACC,KAAK,CAACC,MAAM,GAAGC,IAAI,CAACC,GAAG,CAACJ,QAAQ,CAAChD,YAAY,EAAE,GAAG,CAAC,GAAG,IAAI;EACrE;EAEQL,cAAcA,CAAA;IACpBS,UAAU,CAAC,MAAK;MACd,IAAI,IAAI,CAACyB,iBAAiB,EAAE;QAC1B,MAAMhC,OAAO,GAAG,IAAI,CAACgC,iBAAiB,CAACC,aAAa;QACpDjC,OAAO,CAACE,SAAS,GAAGF,OAAO,CAACG,YAAY;;IAE5C,CAAC,EAAE,EAAE,CAAC;EACR;EAEA;EACAtE,iBAAiBA,CAACC,IAAY;IAC5B,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;IAEpB,IAAI;MACF;MACAd,MAAM,CAACwI,UAAU,CAAC;QAChBC,MAAM,EAAE,IAAI;QACZC,GAAG,EAAE,IAAI,CAAC;OACX,CAAC;MAEF;MACA,MAAMC,WAAW,GAAG3I,MAAM,CAAC4I,KAAK,CAAC9H,IAAI,CAAW;MAEhD;MACA,IAAI+H,aAAa,GAAGF,WAAW,CAC5BG,OAAO,CAAC,qDAAqD,EAAE,EAAE,CAAC,CAClEA,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAC5BA,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;MAE7B,OAAOD,aAAa;KACrB,CAAC,OAAOjE,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD;MACA,OAAO9D,IAAI,CAACgI,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;;EAEtC;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAAC1D,aAAa,EAAE;MACtBC,YAAY,CAAC,IAAI,CAACD,aAAa,CAAC;;EAEpC;EAAC,QAAA2D,CAAA,G;qBA/SUvF,aAAa,EAAAxD,EAAA,CAAAgJ,iBAAA,CAAAC,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAb3F,aAAa;IAAA4F,SAAA;IAAAC,SAAA,WAAAC,oBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;QCvD1BvJ,EAAA,CAAAE,SAAA,yBAAyC;QAEzCF,EAAA,CAAA2B,UAAA,IAAA8H,4BAAA,kBAsEM;QAGNzJ,EAAA,CAAA2B,UAAA,IAAA+H,4BAAA,iBAGM;;;QA5EuB1J,EAAA,CAAAU,SAAA,GAAqB;QAArBV,EAAA,CAAAK,UAAA,SAAAmJ,GAAA,CAAA7F,eAAA,CAAqB;QAyE5C3D,EAAA,CAAAU,SAAA,GAAsB;QAAtBV,EAAA,CAAAK,UAAA,UAAAmJ,GAAA,CAAA7F,eAAA,CAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}