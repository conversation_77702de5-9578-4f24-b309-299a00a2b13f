import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  // private baseUrl = 'http://localhost:8000/api';
  private baseUrl = 'https://news-agent-backend.duckdns.org/api';
  private tokenKey = 'token';

  constructor(private http: HttpClient) { }

  // Get token from localStorage
  getToken(): string | null {
    return localStorage.getItem(this.tokenKey);
  }

  // Set token in localStorage
  private setToken(token: string): void {
    localStorage.setItem(this.tokenKey, token);
  }

  // Check if token exists and is valid
  checkAuthStatus(): Observable<boolean> {
    const token = this.getToken();
    
    if (!token) {
      return of(false);
    }

    const headers = new HttpHeaders({
      'Authorization': `Token ${token}`
    });

    return this.http.get(`${this.baseUrl}/check-auth/`, { headers }).pipe(
      map(() => true),
      catchError(() => of(false))
    );
  }

  // Create anonymous user and get token
  createAnonymousUser(): Observable<string> {
    return this.http.post<{ token: string }>(`${this.baseUrl}/anonymous-user/`, {}).pipe(
      map(response => {
        this.setToken(response.token);
        return response.token;
      })
    );
  }

  // Main function to ensure user is authenticated
  ensureAuthenticated(): Observable<string> {
    return new Observable(observer => {
      this.checkAuthStatus().subscribe(isValid => {
        if (isValid) {
          observer.next(this.getToken()!);
          observer.complete();
        } else {
          this.createAnonymousUser().subscribe(
            token => {
              observer.next(token);
              observer.complete();
            },
            error => {
              observer.error(error);
            }
          );
        }
      });
    });
  }

  // Get headers with authentication
  private getHeaders(): HttpHeaders {
    const token = this.getToken();
    return new HttpHeaders({
      'Authorization': `Token ${token}`
    });
  }

  // Load chat history with pagination
  loadChatHistory(page: number = 1, sessionId?: number): Observable<any> {
    let url = `${this.baseUrl}/chat-history/?page=${page}`;
    if (sessionId) {
      url += `&session_id=${sessionId}`;
    }
    return this.http.get(url, { 
      headers: this.getHeaders() 
    });
  }

  // Send message to chatbot
  sendMessageToChatbot(message: string, sessionId?: number): Observable<any> {
    const payload: any = { message };
    if (sessionId) {
      payload.session_id = sessionId;
    }
    return this.http.post(`${this.baseUrl}/chatbot/`, 
      payload, 
      { headers: this.getHeaders() }
    );
  }

  // Add this method to your AuthService class
  fetchNews(country: string = 'poland'): Observable<any> {
    return this.http.post(`${this.baseUrl}/news/`, 
      { country }, 
      { headers: this.getHeaders() }
    );
  }

}