{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../auth.service\";\nimport * as i2 from \"@angular/common\";\nfunction NewsComponent_div_0_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 5)(1, \"div\", 6)(2, \"span\", 7);\n    i0.ɵɵtext(3);\n    i0.ɵɵpipe(4, \"titlecase\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 8);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"h3\", 9);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 10);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 11)(12, \"span\", 12);\n    i0.ɵɵtext(13);\n    i0.ɵɵpipe(14, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 13);\n    i0.ɵɵtext(16, \"Read \\u2192\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"href\", item_r3.link, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(4, 6, item_r3.country));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(item_r3.source);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r3.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r3.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(14, 8, item_r3.published, \"medium\"));\n  }\n}\nfunction NewsComponent_div_0_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"p\");\n    i0.ɵɵtext(2, \"No articles found. Try adjusting your search or filters.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function NewsComponent_div_0_div_3_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.clearFilters());\n    });\n    i0.ɵɵtext(4, \" Clear All Filters \");\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c0 = function (a0) {\n  return {\n    \"compact\": a0\n  };\n};\nfunction NewsComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2);\n    i0.ɵɵtemplate(2, NewsComponent_div_0_a_2_Template, 17, 11, \"a\", 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, NewsComponent_div_0_div_3_Template, 5, 0, \"div\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c0, ctx_r0.compact));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.filteredNews)(\"ngForTrackBy\", ctx_r0.trackByTitle);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.filteredNews.length === 0 && (ctx_r0.searchTerm || ctx_r0.selectedCountry || ctx_r0.selectedSource));\n  }\n}\nexport let NewsComponent = /*#__PURE__*/(() => {\n  class NewsComponent {\n    constructor(authService) {\n      this.authService = authService;\n      this.showTitle = true;\n      this.compact = false;\n      // News data\n      this.news = [];\n      this.loading = false;\n      this.error = '';\n      // Filter properties\n      this.filteredNews = [];\n      this.searchTerm = '';\n      this.selectedCountry = '';\n      this.selectedSource = '';\n      // Available filter options\n      this.availableCountries = [];\n      this.availableSources = [];\n    }\n    ngOnInit() {\n      this.loadNews();\n    }\n    loadNews() {\n      this.loading = true;\n      this.error = '';\n      this.authService.ensureAuthenticated().subscribe({\n        next: () => {\n          this.authService.fetchNews('poland').subscribe({\n            next: response => {\n              this.news = response.news || [];\n              this.initializeFilters();\n              this.applyFilters();\n              this.loading = false;\n            },\n            error: error => {\n              this.error = 'Failed to load news';\n              this.loading = false;\n              console.error('Error loading news:', error);\n            }\n          });\n        },\n        error: error => {\n          this.error = 'Authentication failed';\n          this.loading = false;\n          console.error('Auth error:', error);\n        }\n      });\n    }\n    initializeFilters() {\n      this.availableCountries = [...new Set(this.news.map(item => item.country))].sort();\n      this.availableSources = [...new Set(this.news.map(item => item.source))].sort();\n    }\n    applyFilters() {\n      let filtered = [...this.news];\n      // Apply search filter\n      if (this.searchTerm.trim()) {\n        const searchLower = this.searchTerm.toLowerCase();\n        filtered = filtered.filter(item => item.title.toLowerCase().includes(searchLower) || item.description.toLowerCase().includes(searchLower) || item.source.toLowerCase().includes(searchLower));\n      }\n      // Apply country filter\n      if (this.selectedCountry) {\n        filtered = filtered.filter(item => item.country === this.selectedCountry);\n      }\n      // Apply source filter\n      if (this.selectedSource) {\n        filtered = filtered.filter(item => item.source === this.selectedSource);\n      }\n      this.filteredNews = filtered;\n    }\n    onSearchChange(event) {\n      this.searchTerm = event.target.value;\n      this.applyFilters();\n    }\n    onCountryChange(event) {\n      this.selectedCountry = event.target.value;\n      this.applyFilters();\n    }\n    onSourceChange(event) {\n      this.selectedSource = event.target.value;\n      this.applyFilters();\n    }\n    clearFilters() {\n      this.searchTerm = '';\n      this.selectedCountry = '';\n      this.selectedSource = '';\n      this.applyFilters();\n    }\n    trackByTitle(index, item) {\n      return item.title;\n    }\n    refreshNews() {\n      this.loadNews();\n    }\n    static #_ = this.ɵfac = function NewsComponent_Factory(t) {\n      return new (t || NewsComponent)(i0.ɵɵdirectiveInject(i1.AuthService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NewsComponent,\n      selectors: [[\"app-news\"]],\n      inputs: {\n        showTitle: \"showTitle\",\n        compact: \"compact\"\n      },\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"news-wrapper\", 3, \"ngClass\", 4, \"ngIf\"], [1, \"news-wrapper\", 3, \"ngClass\"], [1, \"news-grid\"], [\"class\", \"news-card\", \"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 3, \"href\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"no-results\", 4, \"ngIf\"], [\"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 1, \"news-card\", 3, \"href\"], [1, \"news-card-header\"], [1, \"badge\"], [1, \"source\"], [1, \"title\"], [1, \"description\"], [1, \"news-card-footer\"], [1, \"published\"], [1, \"cta\"], [1, \"no-results\"], [\"type\", \"button\", 1, \"clear-filters-btn\", 3, \"click\"]],\n      template: function NewsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NewsComponent_div_0_Template, 4, 6, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.news && ctx.news.length);\n        }\n      },\n      dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i2.TitleCasePipe, i2.DatePipe],\n      styles: [\"[_nghost-%COMP%]{display:block;height:100%}.news-card[_ngcontent-%COMP%]   .title[_ngcontent-%COMP%]{line-height:1.3;color:var(--color-gray-900);font-weight:var(--font-weight-semibold)}.news-card[_ngcontent-%COMP%]   .description[_ngcontent-%COMP%]{display:-webkit-box;-webkit-line-clamp:3;-webkit-box-orient:vertical;overflow:hidden;color:var(--color-gray-600);line-height:1.5}.news-wrapper.compact[_ngcontent-%COMP%]   .news-card[_ngcontent-%COMP%]{transition:all var(--transition-fast)}.news-wrapper.compact[_ngcontent-%COMP%]   .news-card[_ngcontent-%COMP%]:hover{transform:translateY(-2px);box-shadow:var(--shadow-md)}.news-grid[_ngcontent-%COMP%]{overflow-x:hidden;padding-right:4px;scrollbar-width:thin;scrollbar-color:var(--color-gray-300, #cbd5e1) var(--color-gray-100, #f1f5f9)}.news-grid[_ngcontent-%COMP%]::-webkit-scrollbar{width:8px}.news-grid[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:var(--color-gray-100, #f1f5f9);border-radius:4px}.news-grid[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:var(--color-gray-300, #cbd5e1);border-radius:4px}.news-grid[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:var(--color-gray-400, #94a3b8)}.news-wrapper.compact[_ngcontent-%COMP%]   .news-grid[_ngcontent-%COMP%]{max-height:400px}.news-controls[_ngcontent-%COMP%]{margin-bottom:var(--space-6);padding:var(--space-4);background:var(--color-white);border:1px solid var(--color-gray-200);border-radius:var(--radius-lg);box-shadow:var(--shadow-sm)}.search-container[_ngcontent-%COMP%]{margin-bottom:var(--space-4)}.search-input[_ngcontent-%COMP%]{width:100%;max-width:400px;padding:var(--space-3) var(--space-4);border:2px solid var(--color-gray-200);border-radius:var(--radius-lg);font-size:var(--font-size-base);font-family:var(--font-family-primary);transition:border-color var(--transition-fast)}.search-input[_ngcontent-%COMP%]:focus{outline:none;border-color:var(--color-primary)}.search-input[_ngcontent-%COMP%]::placeholder{color:var(--color-gray-500)}.filter-controls[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:var(--space-3);align-items:center}.filter-select[_ngcontent-%COMP%]{padding:var(--space-2) var(--space-3);border:1px solid var(--color-gray-300);border-radius:var(--radius-md);font-size:var(--font-size-sm);background:var(--color-white);cursor:pointer;min-width:120px}.filter-select[_ngcontent-%COMP%]:focus{outline:none;border-color:var(--color-primary)}.clear-filters-btn[_ngcontent-%COMP%]{padding:var(--space-2) var(--space-4);background:var(--color-gray-100);border:1px solid var(--color-gray-300);border-radius:var(--radius-md);font-size:var(--font-size-sm);color:var(--color-gray-700);cursor:pointer;transition:all var(--transition-fast)}.clear-filters-btn[_ngcontent-%COMP%]:hover{background:var(--color-gray-200)}.no-results[_ngcontent-%COMP%]{text-align:center;padding:var(--space-8);color:var(--color-gray-600)}.no-results[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin-bottom:var(--space-4)}@media (max-width: 768px){.filter-controls[_ngcontent-%COMP%]{flex-direction:column;align-items:stretch}.filter-select[_ngcontent-%COMP%]{min-width:auto}.news-wrapper.compact[_ngcontent-%COMP%]   .news-grid[_ngcontent-%COMP%]{max-height:300px}}\"]\n    });\n  }\n  return NewsComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}