{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./chat/chat.component\";\nimport * as i2 from \"./news/news.component\";\nexport let AppComponent = /*#__PURE__*/(() => {\n  class AppComponent {\n    constructor() {\n      this.title = 'News Agent';\n      // Sample news data to demonstrate search and filters\n      this.sampleNews = [{\n        country: 'ukraine',\n        source: 'Kyiv Independent',\n        title: 'Ukraine receives new military aid package',\n        link: 'https://example.com/news1',\n        published: '2024-01-15T10:30:00Z',\n        description: 'NATO allies announce comprehensive military aid package including advanced defense systems.'\n      }, {\n        country: 'poland',\n        source: 'Warsaw Times',\n        title: 'Poland strengthens eastern border security',\n        link: 'https://example.com/news2',\n        published: '2024-01-14T15:45:00Z',\n        description: 'Polish government implements enhanced security protocols along its eastern border.'\n      }, {\n        country: 'russia',\n        source: 'Moscow Herald',\n        title: 'Russian economic indicators show mixed results',\n        link: 'https://example.com/news3',\n        published: '2024-01-13T09:15:00Z',\n        description: 'Latest economic data from Russia reveals varying performance across different sectors.'\n      }, {\n        country: 'belarus',\n        source: 'Minsk Daily',\n        title: 'Belarus announces new agricultural initiatives',\n        link: 'https://example.com/news4',\n        published: '2024-01-12T14:20:00Z',\n        description: 'Government unveils comprehensive agricultural development program.'\n      }, {\n        country: 'ukraine',\n        source: 'Ukrainian Voice',\n        title: 'Reconstruction efforts accelerate in liberated territories',\n        link: 'https://example.com/news5',\n        published: '2024-01-11T11:00:00Z',\n        description: 'International cooperation drives rapid reconstruction of infrastructure.'\n      }];\n    }\n    static #_ = this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)();\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 9,\n      vars: 1,\n      consts: [[1, \"app-container\"], [1, \"app-header\"], [1, \"main-content\"], [1, \"chat-section\"], [1, \"news-section\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"header\", 1)(2, \"h1\");\n          i0.ɵɵtext(3);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(4, \"main\", 2)(5, \"div\", 3);\n          i0.ɵɵelement(6, \"app-chat\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 4);\n          i0.ɵɵelement(8, \"app-news\");\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.title);\n        }\n      },\n      dependencies: [i1.ChatComponent, i2.NewsComponent],\n      styles: [\".app-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:100vh;background:transparent;overflow:hidden}.app-header[_ngcontent-%COMP%]{flex-shrink:0;padding:var(--space-4) 0;background-color:transparent}.app-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{text-align:center;background:linear-gradient(135deg,#4f46e5 0%,#6366f1 50%,#8b5cf6 100%);-webkit-text-fill-color:transparent;-webkit-background-clip:text;background-clip:text;margin:0;padding:var(--space-4) 0 var(--space-2) 0;font-size:var(--font-size-3xl);font-weight:var(--font-weight-bold);letter-spacing:-.025em;position:relative}.app-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]:after{content:\\\"\\\";position:absolute;bottom:0;left:50%;transform:translate(-50%);width:60px;height:3px;background:linear-gradient(90deg,#4f46e5 0%,#8b5cf6 100%);border-radius:2px}.main-content[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:row;overflow:hidden;background-color:transparent;gap:var(--space-4);padding:0 var(--space-4) var(--space-4) var(--space-4)}.chat-section[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;overflow:hidden;min-width:0}.news-section[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;overflow:hidden;min-width:0;height:100%}@media (max-width: 768px){.main-content[_ngcontent-%COMP%]{flex-direction:column;gap:var(--space-2)}.chat-section[_ngcontent-%COMP%], .news-section[_ngcontent-%COMP%]{flex:1;min-height:0}}@media (max-width: 480px){.main-content[_ngcontent-%COMP%]{padding:0 var(--space-2) var(--space-2) var(--space-2)}}.sr-only[_ngcontent-%COMP%]{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border:0}.text-center[_ngcontent-%COMP%]{text-align:center}.text-left[_ngcontent-%COMP%]{text-align:left}.text-right[_ngcontent-%COMP%]{text-align:right}.mb-0[_ngcontent-%COMP%]{margin-bottom:0}.mb-2[_ngcontent-%COMP%]{margin-bottom:var(--space-2)}.mb-4[_ngcontent-%COMP%]{margin-bottom:var(--space-4)}.mb-6[_ngcontent-%COMP%]{margin-bottom:var(--space-6)}.mt-0[_ngcontent-%COMP%]{margin-top:0}.mt-2[_ngcontent-%COMP%]{margin-top:var(--space-2)}.mt-4[_ngcontent-%COMP%]{margin-top:var(--space-4)}.mt-6[_ngcontent-%COMP%]{margin-top:var(--space-6)}\"]\n    });\n  }\n  return AppComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}