{"ast": null, "code": "import { marked } from 'marked';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../auth.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"../anonymous-user/anonymous-user.component\";\nconst _c0 = [\"messagesContainer\"];\nfunction ChatComponent_div_1_div_1_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵelement(1, \"div\", 17);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c1 = function (a0, a1) {\n  return {\n    \"user-message\": a0,\n    \"bot-message\": a1\n  };\n};\nfunction ChatComponent_div_1_div_1_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19);\n    i0.ɵɵelement(2, \"div\", 20);\n    i0.ɵɵelementStart(3, \"div\", 21);\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const message_r8 = ctx.$implicit;\n    const ctx_r6 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(6, _c1, message_r8.isUser, !message_r8.isUser));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"innerHTML\", ctx_r6.formatMessageText(message_r8.text), i0.ɵɵsanitizeHtml);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(5, 3, message_r8.timestamp, \"short\"));\n  }\n}\nfunction ChatComponent_div_1_div_1_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 19)(2, \"div\", 23);\n    i0.ɵɵelement(3, \"span\")(4, \"span\")(5, \"span\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ChatComponent_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 11, 12);\n    i0.ɵɵlistener(\"scroll\", function ChatComponent_div_1_div_1_Template_div_scroll_0_listener($event) {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r9.onScroll($event));\n    });\n    i0.ɵɵtemplate(2, ChatComponent_div_1_div_1_div_2_Template, 2, 0, \"div\", 13);\n    i0.ɵɵtemplate(3, ChatComponent_div_1_div_1_div_3_Template, 6, 9, \"div\", 14);\n    i0.ɵɵtemplate(4, ChatComponent_div_1_div_1_div_4_Template, 6, 0, \"div\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoadingHistory);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.messages);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isLoading);\n  }\n}\nfunction ChatComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵtemplate(1, ChatComponent_div_1_div_1_Template, 5, 3, \"div\", 3);\n    i0.ɵɵelementStart(2, \"div\", 4)(3, \"div\", 5)(4, \"textarea\", 6, 7);\n    i0.ɵɵlistener(\"ngModelChange\", function ChatComponent_div_1_Template_textarea_ngModelChange_4_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.currentMessage = $event);\n    })(\"keydown\", function ChatComponent_div_1_Template_textarea_keydown_4_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.onKeyPress($event));\n    })(\"input\", function ChatComponent_div_1_Template_textarea_input_4_listener($event) {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.adjustTextareaHeight($event));\n    });\n    i0.ɵɵtext(6, \"      \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function ChatComponent_div_1_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.sendMessage());\n    });\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(8, \"svg\", 9);\n    i0.ɵɵelement(9, \"path\", 10);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.messages.length > 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"centered\", ctx_r0.messages.length === 0)(\"bottom\", ctx_r0.messages.length > 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.currentMessage);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.currentMessage.trim() || ctx_r0.isLoading);\n  }\n}\nfunction ChatComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24);\n    i0.ɵɵelement(1, \"div\", 17);\n    i0.ɵɵelementStart(2, \"p\");\n    i0.ɵɵtext(3, \"Initializing chat...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport let ChatComponent = /*#__PURE__*/(() => {\n  class ChatComponent {\n    constructor(authService) {\n      this.authService = authService;\n      this.messages = [];\n      this.currentMessage = '';\n      this.isLoading = false;\n      this.isAuthenticated = false;\n      this.currentSessionId = null;\n      // Pagination\n      this.currentPage = 1;\n      this.hasNextPage = false;\n      this.isLoadingHistory = false;\n      // Scroll management\n      this.shouldScrollToBottom = true;\n      this.lastScrollHeight = 0;\n      this.initialLoadComplete = false;\n      this.userHasScrolled = false;\n    }\n    ngOnInit() {\n      // Ensure user is authenticated before initializing chat\n      this.authService.ensureAuthenticated().subscribe({\n        next: token => {\n          console.log('User authenticated successfully');\n          this.isAuthenticated = true;\n          this.loadChatHistory(1, false);\n        },\n        error: error => {\n          console.error('Authentication failed:', error);\n          this.isAuthenticated = false;\n        }\n      });\n    }\n    ngAfterViewChecked() {\n      // Auto-scroll to bottom only for new messages\n      if (this.shouldScrollToBottom) {\n        this.scrollToBottom();\n        this.shouldScrollToBottom = false;\n      }\n    }\n    // Listen for scroll events to load more history\n    onScroll(event) {\n      const element = event.target;\n      const scrollTop = element.scrollTop;\n      const scrollHeight = element.scrollHeight;\n      const clientHeight = element.clientHeight;\n      // Mark that user has scrolled manually (not programmatic)\n      if (this.initialLoadComplete) {\n        this.userHasScrolled = true;\n      }\n      // Clear existing timeout\n      if (this.scrollTimeout) {\n        clearTimeout(this.scrollTimeout);\n      }\n      // Debounce scroll events and check conditions\n      this.scrollTimeout = setTimeout(() => {\n        // Only load more if:\n        // 1. Initial load is complete\n        // 2. User has scrolled manually at least once\n        // 3. User is near the top (scrollTop < 100)\n        // 4. There are more pages to load\n        // 5. Not currently loading\n        // 6. User is not at the very bottom (to avoid conflicts with auto-scroll)\n        const isNearTop = scrollTop < 100;\n        const isNotAtBottom = scrollTop < scrollHeight - clientHeight - 50;\n        if (isNearTop && isNotAtBottom && this.hasNextPage && !this.isLoadingHistory && this.initialLoadComplete && this.userHasScrolled) {\n          this.lastScrollHeight = scrollHeight;\n          this.loadMoreHistory();\n        }\n      }, 100); // 100ms debounce\n    }\n\n    loadChatHistory(page = 1, append = false) {\n      if (!this.isAuthenticated) return;\n      this.isLoadingHistory = true;\n      this.authService.loadChatHistory(page, this.currentSessionId || undefined).subscribe({\n        next: response => {\n          const newMessages = response.results.map(msg => this.convertChatMessageToMessage(msg));\n          if (append) {\n            // For pagination - reverse the new messages (since API returns newest first)\n            // and prepend older messages to beginning\n            const reversedNewMessages = [...newMessages].reverse();\n            this.messages = [...reversedNewMessages, ...this.messages];\n            this.maintainScrollPosition();\n          } else {\n            // For initial load - reverse messages to get chronological order (oldest first)\n            this.messages = [...newMessages].reverse();\n            this.shouldScrollToBottom = true;\n            // Set initial load complete after scroll positioning is done\n            setTimeout(() => {\n              this.initialLoadComplete = true;\n            }, 200);\n          }\n          // Update pagination info\n          this.currentPage = page;\n          this.hasNextPage = !!response.next;\n          this.isLoadingHistory = false;\n        },\n        error: error => {\n          console.error('Error loading chat history:', error);\n          this.isLoadingHistory = false;\n          // If this was the initial load, still mark it as complete after delay\n          if (!append) {\n            setTimeout(() => {\n              this.initialLoadComplete = true;\n            }, 200);\n          }\n        }\n      });\n    }\n    // Convert Django ChatMessage to frontend Message format\n    convertChatMessageToMessage(chatMessage) {\n      return {\n        id: chatMessage.id,\n        text: chatMessage.message,\n        isUser: chatMessage.sender === 'user',\n        timestamp: new Date(chatMessage.timestamp),\n        session: chatMessage.session,\n        prompt: chatMessage.prompt || undefined,\n        model: chatMessage.model || undefined,\n        news_articles: chatMessage.news_articles || undefined\n      };\n    }\n    // Load more chat history (pagination) - triggered by scroll\n    loadMoreHistory() {\n      if (this.hasNextPage && !this.isLoadingHistory) {\n        this.loadChatHistory(this.currentPage + 1, true);\n      }\n    }\n    // Maintain scroll position when loading older messages\n    maintainScrollPosition() {\n      setTimeout(() => {\n        if (this.messagesContainer) {\n          const element = this.messagesContainer.nativeElement;\n          const newScrollHeight = element.scrollHeight;\n          const scrollDifference = newScrollHeight - this.lastScrollHeight;\n          element.scrollTop = scrollDifference;\n        }\n      }, 50);\n    }\n    sendMessage() {\n      if (!this.currentMessage.trim() || this.isLoading || !this.isAuthenticated) {\n        return;\n      }\n      // Store the message to send\n      const messageToSend = this.currentMessage;\n      this.currentMessage = '';\n      // Create temporary user message and add it instantly\n      const tempUserMessage = {\n        text: messageToSend,\n        isUser: true,\n        timestamp: new Date()\n      };\n      // Add user message instantly to the chat\n      this.messages.push(tempUserMessage);\n      this.shouldScrollToBottom = true;\n      // Set loading state\n      this.isLoading = true;\n      // Call the API through auth service\n      this.authService.sendMessageToChatbot(messageToSend, this.currentSessionId || undefined).subscribe({\n        next: response => {\n          // Convert backend messages\n          const userMessage = this.convertChatMessageToMessage(response.user_message);\n          const botMessage = this.convertChatMessageToMessage(response.bot_message);\n          // Replace the temporary user message with the one from backend\n          const lastMessageIndex = this.messages.length - 1;\n          if (lastMessageIndex >= 0 && this.messages[lastMessageIndex].isUser) {\n            this.messages[lastMessageIndex] = userMessage;\n          }\n          // Add bot message\n          this.messages.push(botMessage);\n          // Store session ID for future requests\n          if (!this.currentSessionId) {\n            this.currentSessionId = response.user_message.session;\n          }\n          this.isLoading = false;\n          this.shouldScrollToBottom = true;\n        },\n        error: error => {\n          console.error('Error sending message:', error);\n          const errorMessage = {\n            text: 'Sorry, there was an error processing your message. Please try again.',\n            isUser: false,\n            timestamp: new Date()\n          };\n          this.messages.push(errorMessage);\n          this.isLoading = false;\n          this.shouldScrollToBottom = true;\n        }\n      });\n    }\n    clearHistory() {\n      this.messages = [];\n      this.currentPage = 1;\n      this.hasNextPage = false;\n      this.initialLoadComplete = false;\n      this.userHasScrolled = false;\n      if (this.scrollTimeout) {\n        clearTimeout(this.scrollTimeout);\n      }\n    }\n    refreshHistory() {\n      this.currentPage = 1;\n      this.initialLoadComplete = false;\n      this.userHasScrolled = false;\n      if (this.scrollTimeout) {\n        clearTimeout(this.scrollTimeout);\n      }\n      this.loadChatHistory();\n    }\n    onKeyPress(event) {\n      if (event.key === 'Enter' && !event.shiftKey) {\n        event.preventDefault();\n        this.sendMessage();\n      }\n    }\n    adjustTextareaHeight(event) {\n      const textarea = event.target;\n      textarea.style.height = 'auto';\n      textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';\n    }\n    scrollToBottom() {\n      setTimeout(() => {\n        if (this.messagesContainer) {\n          const element = this.messagesContainer.nativeElement;\n          element.scrollTop = element.scrollHeight;\n        }\n      }, 50);\n    }\n    // Format message text using marked library for markdown\n    formatMessageText(text) {\n      if (!text) return '';\n      try {\n        // Configure marked to be more restrictive for security\n        marked.setOptions({\n          breaks: true,\n          gfm: true // Enable GitHub Flavored Markdown\n        });\n        // Convert markdown to HTML using marked (synchronous)\n        const htmlContent = marked.parse(text);\n        // Basic sanitization - remove script tags and dangerous attributes\n        let sanitizedHtml = htmlContent.replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, '').replace(/javascript:/gi, '').replace(/on\\w+\\s*=/gi, '');\n        return sanitizedHtml;\n      } catch (error) {\n        console.error('Error formatting message text:', error);\n        // Fallback to plain text with basic line break conversion\n        return text.replace(/\\n/g, '<br>');\n      }\n    }\n    ngOnDestroy() {\n      // Clean up any pending timeouts\n      if (this.scrollTimeout) {\n        clearTimeout(this.scrollTimeout);\n      }\n    }\n    static #_ = this.ɵfac = function ChatComponent_Factory(t) {\n      return new (t || ChatComponent)(i0.ɵɵdirectiveInject(i1.AuthService));\n    };\n    static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ChatComponent,\n      selectors: [[\"app-chat\"]],\n      viewQuery: function ChatComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.messagesContainer = _t.first);\n        }\n      },\n      decls: 3,\n      vars: 2,\n      consts: [[\"class\", \"chat-container\", 4, \"ngIf\"], [\"class\", \"auth-loading\", 4, \"ngIf\"], [1, \"chat-container\"], [\"class\", \"messages-container\", 3, \"scroll\", 4, \"ngIf\"], [1, \"input-container\"], [1, \"input-wrapper\"], [\"placeholder\", \"Ask anything\", \"rows\", \"1\", 1, \"message-input\", 3, \"ngModel\", \"ngModelChange\", \"keydown\", \"input\"], [\"messageTextarea\", \"\"], [1, \"send-button\", 3, \"disabled\", \"click\"], [\"width\", \"24\", \"height\", \"24\", \"viewBox\", \"0 0 24 24\", \"fill\", \"none\", \"xmlns\", \"http://www.w3.org/2000/svg\"], [\"d\", \"M2 21L23 12L2 3V10L17 12L2 14V21Z\", \"fill\", \"currentColor\"], [1, \"messages-container\", 3, \"scroll\"], [\"messagesContainer\", \"\"], [\"class\", \"pagination-loading\", 4, \"ngIf\"], [\"class\", \"message\", 3, \"ngClass\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"message bot-message\", 4, \"ngIf\"], [1, \"pagination-loading\"], [1, \"loading-spinner\"], [1, \"message\", 3, \"ngClass\"], [1, \"message-content\"], [1, \"message-text\", 3, \"innerHTML\"], [1, \"message-time\"], [1, \"message\", \"bot-message\"], [1, \"typing-indicator\"], [1, \"auth-loading\"]],\n      template: function ChatComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelement(0, \"app-anonymous-user\");\n          i0.ɵɵtemplate(1, ChatComponent_div_1_Template, 10, 7, \"div\", 0);\n          i0.ɵɵtemplate(2, ChatComponent_div_2_Template, 4, 0, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", ctx.isAuthenticated);\n          i0.ɵɵadvance(1);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isAuthenticated);\n        }\n      },\n      dependencies: [i2.NgClass, i2.NgForOf, i2.NgIf, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgModel, i4.AnonymousUserComponent, i2.DatePipe],\n      styles: [\".chat-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;height:100%;max-width:900px;margin:0 auto;background:linear-gradient(to bottom,#ffffff 0%,#fefefe 100%);position:relative;overflow:hidden;font-family:var(--font-family-primary);border-radius:var(--radius-2xl);box-shadow:0 10px 40px #00000014,0 4px 12px #0000000a;border:1px solid rgba(255,255,255,.8)}.messages-container[_ngcontent-%COMP%]{flex:1;overflow-y:auto;padding:var(--space-6) var(--space-8) 120px var(--space-8);background:#f9fafb;max-height:calc(100vh - 160px);scroll-behavior:smooth;position:relative;z-index:10}.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar{width:6px}.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:#f1f5f9;border-radius:var(--radius-md)}.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#cbd5e1;border-radius:var(--radius-md);-webkit-transition:var(--transition-fast);transition:var(--transition-fast)}.messages-container[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#94a3b8}.pagination-loading[_ngcontent-%COMP%]{display:flex;justify-content:center;padding:var(--space-4) 0;margin-bottom:var(--space-4)}.pagination-loading[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%]{width:24px;height:24px;border:2px solid #e2e8f0;border-top:2px solid #bdf2bd;border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite}.message[_ngcontent-%COMP%]{margin-bottom:var(--space-4);display:flex;animation:_ngcontent-%COMP%_fadeInUp .3s ease-out;position:relative}.message.user-message[_ngcontent-%COMP%]{justify-content:flex-end}.message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]{background:linear-gradient(135deg,#d0ceff 0%,#e6e6ff 100%);color:#fff;max-width:75%;border-radius:20px 20px 6px;box-shadow:0 3px 12px #4f46e540;transition:all var(--transition-fast);position:relative;border:none}.message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 6px 20px #4f46e54d}.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   code[_ngcontent-%COMP%]{background:rgba(0,0,0,.15);color:#059669}.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]{background:rgba(0,0,0,.05);border-color:#0000001a}.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]   code[_ngcontent-%COMP%]{color:#047857}.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#059669}.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:#047857}.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]{border-left-color:#059669;background:rgba(0,0,0,.05)}.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{border-color:#0000001a}.message.user-message[_ngcontent-%COMP%]   .message-text[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{background:rgba(0,0,0,.05)}.message.bot-message[_ngcontent-%COMP%]{justify-content:flex-start;flex-direction:column;align-items:flex-start}.message.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]{background:linear-gradient(135deg,#f8fafc 0%,#f1f5f9 100%);color:var(--color-gray-800);max-width:75%;border-radius:20px 20px 20px 6px;box-shadow:0 2px 8px #0000000f;transition:all var(--transition-fast);position:relative;border:1px solid var(--color-gray-200)}.message.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 4px 12px #00000014;border-color:var(--color-gray-300)}.message.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]:hover   .message-news[_ngcontent-%COMP%]{margin-top:var(--space-3)}.message.bot-message[_ngcontent-%COMP%]   .message.bot-message[_ngcontent-%COMP%]{flex-direction:column;align-items:flex-start}.message.bot-message[_ngcontent-%COMP%]   .message-news[_ngcontent-%COMP%]{max-width:80%;margin-left:0;align-self:flex-start}.message-content[_ngcontent-%COMP%]{padding:var(--space-4) var(--space-5);word-wrap:break-word;position:relative}.message-text[_ngcontent-%COMP%]{font-size:var(--font-size-base);line-height:1.6;margin:0;font-weight:var(--font-weight-normal);word-break:break-word}.message-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0 0 var(--space-3) 0}.message-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:last-child{margin-bottom:0}.message-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{margin:var(--space-4) 0 var(--space-2) 0;font-weight:var(--font-weight-semibold);line-height:1.3}.message-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]:first-child{margin-top:0}.message-text[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]{font-size:1.5em}.message-text[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%]{font-size:1.3em}.message-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-size:1.2em}.message-text[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{font-size:1.1em}.message-text[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{font-size:1em}.message-text[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   b[_ngcontent-%COMP%]{font-weight:var(--font-weight-semibold)}.message-text[_ngcontent-%COMP%]   em[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   i[_ngcontent-%COMP%]{font-style:italic}.message-text[_ngcontent-%COMP%]   del[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   strike[_ngcontent-%COMP%]{text-decoration:line-through}.message-text[_ngcontent-%COMP%]   u[_ngcontent-%COMP%]{text-decoration:underline}.message-text[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]{margin:var(--space-3) 0;padding-left:var(--space-6)}.message-text[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]:first-child, .message-text[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]:first-child{margin-top:0}.message-text[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]:last-child, .message-text[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%]:last-child{margin-bottom:0}.message-text[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{margin:var(--space-1) 0;line-height:1.5}.message-text[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]{margin:var(--space-4) 0;padding:var(--space-3) var(--space-4);border-left:4px solid #e5e7eb;background:rgba(0,0,0,.02);border-radius:0 var(--radius-sm) var(--radius-sm) 0;font-style:italic}.message-text[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]:first-child{margin-top:0}.message-text[_ngcontent-%COMP%]   blockquote[_ngcontent-%COMP%]:last-child{margin-bottom:0}.message-text[_ngcontent-%COMP%]   code[_ngcontent-%COMP%]{background:rgba(0,0,0,.08);padding:2px 6px;border-radius:var(--radius-sm);font-family:Monaco,Menlo,Ubuntu Mono,monospace;font-size:.9em;color:#e11d48}.message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]{background:#f8fafc;border:1px solid #e2e8f0;border-radius:var(--radius-md);padding:var(--space-4);margin:var(--space-4) 0;overflow-x:auto}.message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]:first-child{margin-top:0}.message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]:last-child{margin-bottom:0}.message-text[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%]   code[_ngcontent-%COMP%]{background:none;padding:0;color:#334155;font-size:.875em}.message-text[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{color:#2563eb;text-decoration:underline;transition:var(--transition-fast)}.message-text[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover{color:#1d4ed8;text-decoration:none}.message-text[_ngcontent-%COMP%]   table[_ngcontent-%COMP%]{width:100%;border-collapse:collapse;margin:var(--space-4) 0}.message-text[_ngcontent-%COMP%]   table[_ngcontent-%COMP%]:first-child{margin-top:0}.message-text[_ngcontent-%COMP%]   table[_ngcontent-%COMP%]:last-child{margin-bottom:0}.message-text[_ngcontent-%COMP%]   th[_ngcontent-%COMP%], .message-text[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]{padding:var(--space-2) var(--space-3);border:1px solid #e2e8f0;text-align:left}.message-text[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]{background:#f8fafc;font-weight:var(--font-weight-semibold)}.message-text[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:100%;height:auto;border-radius:var(--radius-sm);margin:var(--space-2) 0}.message-time[_ngcontent-%COMP%]{font-size:var(--font-size-xs);color:#ffffffb3;margin-top:var(--space-2);text-align:right;opacity:.8;transition:var(--transition-fast);font-weight:var(--font-weight-normal)}.bot-message[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%]{text-align:left;color:var(--color-gray-600)}.user-message[_ngcontent-%COMP%]   .message-time[_ngcontent-%COMP%]{text-align:right;color:var(--color-gray-600)}.message[_ngcontent-%COMP%]:hover   .message-time[_ngcontent-%COMP%]{opacity:1}.input-container[_ngcontent-%COMP%]{padding:var(--space-6) var(--space-8) var(--space-8) var(--space-8);background:linear-gradient(to top,#ffffff 0%,#fefefe 100%);position:absolute;bottom:0;left:0;right:0;transform:none;width:100%;z-index:50;border-top:1px solid rgba(0,0,0,.06);transition:var(--transition-fast);-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.input-container.centered[_ngcontent-%COMP%]{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);width:calc(100% - var(--space-8));max-width:600px;background:linear-gradient(135deg,#ffffff 0%,#fefefe 100%);z-index:50;padding:var(--space-8);bottom:auto;border-radius:var(--radius-2xl);border:1px solid rgba(0,0,0,.06);box-shadow:0 20px 60px #0000001f,0 8px 24px #00000014}.input-container.bottom[_ngcontent-%COMP%]{position:absolute;bottom:0;left:0;right:0;transform:none;border-top:1px solid rgba(0,0,0,.06);background:linear-gradient(to top,#ffffff 0%,#fefefe 100%);width:100%;z-index:50;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px)}.input-wrapper[_ngcontent-%COMP%]{display:flex;align-items:flex-end;gap:var(--space-4);border:2px solid var(--color-gray-200);border-radius:28px;padding:var(--space-4) var(--space-5);background:var(--color-white);transition:all var(--transition-normal);position:relative;box-shadow:0 2px 8px #0000000a}.input-wrapper[_ngcontent-%COMP%]:focus-within{border-color:var(--color-primary);background:var(--color-white);box-shadow:0 0 0 4px #4f46e514,0 4px 12px #00000014;transform:translateY(-1px)}.input-wrapper[_ngcontent-%COMP%]:hover{border-color:var(--color-gray-300);box-shadow:0 4px 12px #0000000f}.message-input[_ngcontent-%COMP%]{flex:1;border:none;outline:none;background:transparent;resize:none;font-size:var(--font-size-base);line-height:1.6;padding:var(--space-2) 0;min-height:28px;max-height:120px;font-family:var(--font-family-primary);overflow-y:auto;transition:var(--transition-fast);color:var(--color-gray-900);font-weight:var(--font-weight-normal)}.message-input[_ngcontent-%COMP%]::placeholder{color:var(--color-gray-500);opacity:1;font-weight:var(--font-weight-normal)}.message-input[_ngcontent-%COMP%]:disabled{opacity:.5;cursor:not-allowed}.message-input[_ngcontent-%COMP%]::-webkit-scrollbar{width:4px}.message-input[_ngcontent-%COMP%]::-webkit-scrollbar-track{background:transparent}.message-input[_ngcontent-%COMP%]::-webkit-scrollbar-thumb{background:#cbd5e1;border-radius:var(--radius-sm)}.message-input[_ngcontent-%COMP%]::-webkit-scrollbar-thumb:hover{background:#94a3b8}.send-button[_ngcontent-%COMP%]{background:linear-gradient(135deg,#4f46e5 0%,#6366f1 100%);border:none;border-radius:50%;width:44px;height:44px;display:flex;align-items:center;justify-content:center;color:var(--color-white);cursor:pointer;flex-shrink:0;transition:all var(--transition-normal);box-shadow:0 4px 12px #4f46e54d;position:relative;overflow:hidden}.send-button[_ngcontent-%COMP%]:before{content:\\\"\\\";position:absolute;inset:0;background:linear-gradient(135deg,rgba(255,255,255,.2) 0%,transparent 50%);border-radius:50%;opacity:0;transition:opacity var(--transition-fast)}.send-button[_ngcontent-%COMP%]:hover:not(:disabled){transform:translateY(-2px) scale(1.05);box-shadow:0 8px 20px #4f46e566}.send-button[_ngcontent-%COMP%]:hover:not(:disabled):before{opacity:1}.send-button[_ngcontent-%COMP%]:active:not(:disabled){transform:translateY(-1px) scale(1.02);box-shadow:0 4px 12px #4f46e54d}.send-button[_ngcontent-%COMP%]:disabled{background:var(--color-gray-400);cursor:not-allowed;transform:none;box-shadow:0 2px 4px #0000001a}.send-button[_ngcontent-%COMP%]:disabled:before{display:none}.send-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{width:20px;height:20px;transition:var(--transition-fast);transform:translate(1px)}.send-button[_ngcontent-%COMP%]:hover:not(:disabled)   svg[_ngcontent-%COMP%]{transform:translate(1px) scale(1.1)}.typing-indicator[_ngcontent-%COMP%]{display:flex;align-items:center;gap:var(--space-1);padding:var(--space-1) 0}.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]{width:8px;height:8px;background:#94a3b8;border-radius:50%;animation:_ngcontent-%COMP%_typing 1.4s infinite ease-in-out}.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(1){animation-delay:0s}.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(2){animation-delay:.2s}.typing-indicator[_ngcontent-%COMP%]   span[_ngcontent-%COMP%]:nth-child(3){animation-delay:.4s}.auth-loading[_ngcontent-%COMP%]{display:flex;flex-direction:column;justify-content:center;align-items:center;height:100vh;color:var(--color-gray-600);background:linear-gradient(135deg,var(--color-gray-50) 0%,var(--color-white) 50%,#f0f4f8 100%)}.auth-loading[_ngcontent-%COMP%]   .loading-spinner[_ngcontent-%COMP%]{width:48px;height:48px;border:4px solid var(--color-gray-200);border-top:4px solid var(--color-accent);border-radius:50%;animation:_ngcontent-%COMP%_spin 1s linear infinite;margin-bottom:var(--space-4)}.auth-loading[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:var(--font-size-lg);color:var(--color-gray-600);margin:0;font-weight:var(--font-weight-medium)}@keyframes _ngcontent-%COMP%_typing{0%,60%,to{opacity:.3;transform:scale(.7) translateY(0)}30%{opacity:1;transform:scale(1.3) translateY(-6px)}}@keyframes _ngcontent-%COMP%_slideInUp{0%{opacity:0;transform:translateY(40px) scale(.9)}to{opacity:1;transform:translateY(0) scale(1)}}@media (max-width: 768px){.chat-container[_ngcontent-%COMP%]{max-width:100%;height:100vh;border-radius:0;border:none}.messages-container[_ngcontent-%COMP%]{padding:var(--space-5) var(--space-5) 140px var(--space-5)}.input-container[_ngcontent-%COMP%]{padding:var(--space-5) var(--space-5) var(--space-6) var(--space-5);max-width:100%}.input-container.centered[_ngcontent-%COMP%]{max-width:100%;padding:var(--space-5);margin:0 var(--space-4);width:calc(100% - var(--space-8))}.message[_ngcontent-%COMP%]{margin-bottom:var(--space-5)}.message.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%], .message.bot-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]{max-width:88%}.input-wrapper[_ngcontent-%COMP%]{padding:var(--space-1) var(--space-2) var(--space-1) var(--space-4);gap:var(--space-2)}.send-button[_ngcontent-%COMP%]{width:42px;height:42px}.send-button[_ngcontent-%COMP%]   svg[_ngcontent-%COMP%]{width:18px;height:18px}}*[_ngcontent-%COMP%]{transition:color var(--transition-fast),background-color var(--transition-fast),border-color var(--transition-fast),transform var(--transition-fast),box-shadow var(--transition-fast)}.send-button[_ngcontent-%COMP%]:focus{outline:2px solid var(--color-primary);outline-offset:3px}.message-input[_ngcontent-%COMP%]:focus{box-shadow:none}.message-content[_ngcontent-%COMP%]::selection{background:rgba(16,185,129,.2)}.user-message[_ngcontent-%COMP%]   .message-content[_ngcontent-%COMP%]::selection{background:rgba(255,255,255,.3)}@keyframes _ngcontent-%COMP%_fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes _ngcontent-%COMP%_typing{0%,60%,to{transform:translateY(0);opacity:.4}30%{transform:translateY(-10px);opacity:1}}@keyframes _ngcontent-%COMP%_spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}\"]\n    });\n  }\n  return ChatComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}